environment=ppr
legacy_env=preprod
environmentForInputs=prd
environmentPns=npr
dagGroup=profilingTV
workflowName=${project.artifactId}
workflowVersion=${project.version}
startDate=START_DATE_TO_REPLACE
branchName=BRANCH_NAME_TO_REPLACE

# Projet GCP
gcpProjectId=ofr-pfg-profilingtv-${environment}

# Refstor client GP
refStoreProjectId=ofr-0np-refstor-cltgp-${environment}

# Bigquery Project Keyring
bigqueryPnsKeyringProject=ofr-pns-data-prd

# Emails
emailFrom=Profiling Team
profilingMailAlert=GITLAB_USER_EMAIL

# Bucket des Artifacts
bucketArtifacts=ofr-pfg-artifact-allwf-${environment}
# Profiling Private Data bucket en lecture
bucketDataIn=ofr-pfg-data-allwf-${environmentForInputs}
# Profiling Private Data bucket en écriture
bucketData=ofr-pfg-data-allwf-${environment}

# Bucket d'export vers PNS
# Real pns croco bucket
#pnscrocodilebucket=ofr-pns-data-crocofile-pbi-pig-profiling-${environmentPns}
# Fake pns croco path
pnscrocodilebucket=${bucketData}/fake/pns-croco-file/ravennepns

# Service account : obligatoirement ici car gcp_iam_access_token utilis @task.virtualenv
# ce qui ne permet pas d'utiliser les valeurs du pre_init (ti non passé, arti_dag_common_package non passé, ...)
gcpServiceAccount=sa-ofr-pfg-profilingtv-allwf@ofr-pfg-profilingtv-${environment}.iam.gserviceaccount.com

# Url okapi pour récupération des tokens
urlOkapi=https://okapi-v2.api.hbx.geo.infra.ftgroup/v2/token

# Accès à l'ihm en lecture
urlBackOfficeRO=https://genericmarker.prod.api.hbx.geo.infra.ftgroup
serviceScopeIhmRO=api-genericmarker-v1-prd:readwrite
clientIdIhmRO=cli-profilingworkflows-v1-prd
secretVersionIhmRO=1

# Accès à l'ihm en écriture
urlBackOfficeRW=https://genericmarkerstaging.int.api.hbx.geo.infra.ftgroup
serviceScopeIhmRW=api-genericmarkerstaging-v1-ppd:readwrite
clientIdIhmRW=cli-profilingworkflowsstaging-v1-ppd
secretVersionIhmRW=1

# Ecriture de mails
mailApi=https://mail2fed.stable.api.hbx.geo.infra.ftgroup/v1/email/send
serviceScopeMail=api-mail2fed-v1-dev:emailsend_access
clientIdMail=cli-profilingworkflowsstaging-v1-ppd
secretVersionMail=1

# Dimensionnement du cluster dataproc
masterType=n2-standard-8
workerType=n2-standard-16
numWorkers=8
numPreemptibleWorkers=0
idleDeleteTtl=3600
autoDeleteTtl=14400

# Paramétrage du traitement
schedule=true
disableAfterRun=true
