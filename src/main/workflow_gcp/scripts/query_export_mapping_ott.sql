-- export mappingott profilingHash => aidHash
EXPORT DATA OPTIONS (
    uri ='{{ uri }}',
    overwrite = true,
    header = false,
    field_delimiter ='\t',
    format ='CSV'
) AS (
    WITH optin_ott AS (
        SELECT DISTINCT profiling_hash, iseh, terminalId
        FROM `{{ profilingtv_project_id }}.{{ optin_dataset }}.{{ optinott_table }}`
    ),
    isehash_aid AS (
        SELECT DISTINCT A.s_isehash AS isehash, A.d_aid AS aid
        FROM (
            SELECT s_isehash, d_aid, creation_timestamp, last_modification_tmp
            FROM `{{ pns_project_id }}.{{ aid_iseh_dataset }}.{{ aid_iseh_table }}`
            QUALIFY ROW_NUMBER() OVER (PARTITION BY s_isehash ORDER BY last_modification_tmp.t_write_time DESC) = 1
        ) AS A
        WHERE A.last_modification_tmp[offset(1)] IS NULL
    ),
    optin_ott_aid AS (
        SELECT DISTINCT profiling_hash, iseh, terminalId, isehash_aid.aid
        FROM optin_ott
        JOIN isehash_aid ON isehash_aid.isehash = optin_ott.iseh
    ),
    optin_calcul AS (
        SELECT DISTINCT CAST(aid AS STRING) AS aid, aid_hash
        FROM `{{ profilingtv_project_id }}.{{ optin_dataset }}.{{ optincalculation_table }}`
    )
    SELECT DISTINCT profiling_hash, optin_calcul.aid_hash, optin_calcul.aid, iseh
    FROM optin_ott_aid
    JOIN optin_calcul ON optin_calcul.aid = optin_ott_aid.aid
);