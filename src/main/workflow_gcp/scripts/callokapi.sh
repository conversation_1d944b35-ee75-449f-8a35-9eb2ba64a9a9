#!/bin/bash

# url okapi
url_okapi="$1"
# service configuration
service_scope="$2"
# client configuration
client_id="$3"
# secret version
secret_version="$4"

#tous les parametres suivants
shift;shift;shift;shift;
curl_params=$@

# Recuperation du secret dans le secret manager
client_secret=$(gcloud secrets  versions access $secret_version --secret="${client_id}_pwd")

okapi_data=$(
    curl --silent \
        --show-error \
        --url "${url_okapi}" \
        --data "grant_type=client_credentials" \
        --data "scope=${service_scope}" \
        --header "Accept: application/json;charset=utf-8" \
        --header "Content-Type: application/x-www-form-urlencoded" \
        -k \
        --user "${client_id}:${client_secret}"
)


# The access token is in the "access_token" field of ${okapi_data}
access_token=$(sed -n 's/.*"access_token":"\([^\"]*\)".*$/\1/p' <<< "${okapi_data}")
# The token lifetime in seconds
token_lifetime=$(sed -n 's/.*"expires_in":\([^\,}]*\).*$/\1/p' <<< "${okapi_data}")


if [ -z "${access_token}" ]; then
    echo "[ERROR] Failed to retrieve the access token from okapi"
    echo "Reponse body:"
    sed -e '$d' <<< "${okapi_data}"
    exit 1
fi

# Call the service through api gateway with Okapi authentication data
# and store its output in ${service_data}
curl --header "Authorization: Bearer ${access_token}"  -k $curl_params
