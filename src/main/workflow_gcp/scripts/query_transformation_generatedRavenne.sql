BEGIN
create or replace external table `{{ visionnageRavenneProject }}.{{ visionnageRavenneDataset }}.{{ visionnageRavenneRawTable }}`
(    aid STRING ,
     conceptstr STRING ,
     dayOfWeek INTEGER ,
     periode INTEGER ,
     timeslot INTEGER ,
     vodLive STRING ,
     zapduration INTEGER ,
     averageTime INTEGER ,
     provider STRING ,
     contentId STRING ,
     title STRING ,
     SeasonName STRING ,
     SerieName STRING
)

OPTIONS (
    uris = ['{{ uri }}'],
    format = 'CSV',
    skip_leading_rows = 0,
    field_delimiter = '\t',
    quote = '',
    allow_jagged_rows=True,
    ignore_unknown_values= True
);
truncate table `{{ refStoreProject }}.{{ refStoreVisionnageRavenneDataset }}.{{ visionnageRavenneTable }}`;
insert into  `{{ refStoreProject }}.{{ refStoreVisionnageRavenneDataset }}.{{ visionnageRavenneTable }}` (concept,vodLive,title ,SeasonName,SerieName,views)
SELECT split(conceptstr,",") as concept, vodLive ,title , SeasonName , SerieName, count(aid)
FROM
`{{ visionnageRavenneProject }}.{{ visionnageRavenneDataset }}.{{ visionnageRavenneRawTable }}` group by conceptstr,vodLive,title,SeasonName,SerieName;

END;
