{"2": [{"catégories/magazine": "5.5", "catégories/programme d'information": "7.9", "sujets/société": "5.9", "type/talk-show": "4.7"}, {"catégories/magazine": "10.0", "catégories/programme d'information": "3.5", "sujets/société": "9.0", "type/talk-show": "10.0"}, {"catégories/magazine": "2.0", "catégories/programme d'information": "5.0", "sujets/société": "10.0", "type/talk-show": "5.0"}, {"catégories/programme d'information": "8.1", "sujets/société": "6.5", "type/talk-show": "5.1"}, {"catégories/programme d'information": "9.6", "sujets/société": "7.3"}, {"catégories/magazine": "5.5", "catégories/programme d'information": "9.2"}, {"catégories/magazine": "3.5", "catégories/programme d'information": "3.5", "sujets/société": "10.0", "type/talk-show": "7.5"}, {"catégories/magazine": "3.0", "catégories/programme d'information": "6.0", "sujets/société": "3.0", "type/reportage": "6.0"}, {"catégories/magazine": "7.5", "catégories/programme d'information": "8.4", "sujets/société": "5.5", "type/reportage": "6.7"}, {"catégories/magazine": "6.3", "catégories/programme d'information": "7.9", "type/reportage": "6.6"}, {"catégories/magazine": "7.4", "catégories/programme d'information": "7.9", "type/talk-show": "7.0"}, {"catégories/magazine": "5.1", "catégories/programme d'information": "8.2", "sujets/société": "6.4"}, {"catégories/magazine": "5.0", "catégories/programme d'information": "5.0", "sujets/société": "5.0", "type/talk-show": "10.0"}, {"catégories/programme d'information": "8.2", "sujets/société": "5.6", "type/reportage": "6.5"}], "3": [{"catégories/documentaire tv": "10.0", "catégories/fiction": "10.0", "catégories/programme d'information": "8.0", "catégories/émissions": "10.0", "sujets/actualité": "8.0", "sujets/société": "8.0"}, {"catégories/documentaire tv": "10.0", "catégories/fiction": "6.0", "catégories/programme d'information": "6.0", "sujets/actualité": "6.0", "sujets/société": "4.0"}, {"catégories/documentaire tv": "10.0", "catégories/fiction": "6.0", "catégories/programme d'information": "6.0", "sujets/société": "6.0"}, {"catégories/documentaire tv": "7.3", "catégories/magazine": "5.5", "catégories/émissions": "3.7"}, {"catégories/documentaire tv": "2.0", "catégories/émissions": "2.0", "sujets/actualité": "5.0", "sujets/société": "10.0"}, {"catégories/documentaire tv": "8.5", "catégories/programme d'information": "3.5", "catégories/émissions": "3.5", "sujets/actualité": "7.0", "sujets/société": "6.5"}, {"catégories/documentaire tv": "5.0", "catégories/fiction": "5.0", "catégories/programme d'information": "6.0", "catégories/émissions": "5.0", "sujets/actualité": "6.0", "sujets/société": "6.0"}, {"catégories/documentaire tv": "6.8", "sujets/société": "7.8"}, {"catégories/documentaire tv": "7.0", "catégories/programme d'information": "3.0", "catégories/émissions": "3.0", "sujets/actualité": "3.0", "sujets/société": "10.0"}, {"catégories/documentaire tv": "6.8", "catégories/fiction": "6.6", "catégories/magazine": "7.0"}, {"catégories/documentaire tv": "5.0", "catégories/fiction": "5.0", "catégories/magazine": "10.0", "sujets/actualité": "8.0", "sujets/société": "8.0"}, {"catégories/documentaire tv": "5.9", "catégories/magazine": "5.7", "catégories/programme d'information": "7.1", "sujets/société": "7.8"}, {"catégories/documentaire tv": "5.4", "catégories/fiction": "6.0", "catégories/émissions": "5.4", "sujets/actualité": "6.7", "sujets/société": "8.2"}, {"catégories/documentaire tv": "6.0", "catégories/fiction": "4.0", "catégories/magazine": "4.0", "sujets/actualité": "6.0", "sujets/société": "10.0"}, {"catégories/documentaire tv": "3.0", "catégories/fiction": "3.0", "catégories/magazine": "7.0", "catégories/émissions": "3.0", "sujets/actualité": "10.0"}, {"catégories/documentaire tv": "3.5", "catégories/magazine": "8.9", "catégories/émissions": "3.5"}, {"catégories/documentaire tv": "3.9", "catégories/programme d'information": "6.5", "catégories/émissions": "3.9", "sujets/actualité": "7.8", "sujets/société": "7.0"}, {"catégories/documentaire tv": "6.5", "catégories/magazine": "6.3", "catégories/programme d'information": "9.4", "sujets/actualité": "8.9", "sujets/société": "7.4"}, {"catégories/documentaire tv": "7.6", "catégories/magazine": "7.6", "catégories/émissions": "4.6", "sujets/actualité": "5.0", "sujets/société": "5.1"}, {"catégories/documentaire tv": "10.0", "catégories/magazine": "10.0", "catégories/programme d'information": "6.0", "sujets/société": "4.0"}, {"catégories/documentaire tv": "10.0", "catégories/programme d'information": "4.0", "catégories/émissions": "4.0", "sujets/actualité": "4.0", "sujets/société": "3.0"}, {"catégories/documentaire tv": "7.0", "catégories/fiction": "10.0", "catégories/magazine": "7.0", "catégories/programme d'information": "10.0", "sujets/société": "10.0"}, {"catégories/documentaire tv": "10.0", "catégories/fiction": "6.0", "catégories/magazine": "6.0", "sujets/société": "4.0"}, {"catégories/documentaire tv": "5.7", "catégories/programme d'information": "8.1", "sujets/société": "7.1"}, {"catégories/documentaire tv": "5.5", "catégories/émissions": "4.7", "sujets/société": "8.7"}, {"catégories/documentaire tv": "5.0", "catégories/fiction": "5.0", "catégories/programme d'information": "10.0", "catégories/émissions": "5.0", "sujets/actualité": "5.0", "sujets/société": "10.0"}, {"catégories/documentaire tv": "4.0", "catégories/magazine": "4.0", "catégories/programme d'information": "10.0", "sujets/actualité": "5.0", "sujets/société": "5.5"}, {"catégories/documentaire tv": "6.0", "catégories/fiction": "7.0", "catégories/magazine": "7.0", "sujets/actualité": "5.0", "sujets/société": "10.0"}, {"catégories/documentaire tv": "8.4", "catégories/magazine": "3.4", "catégories/émissions": "3.4", "sujets/actualité": "5.4"}, {"catégories/documentaire tv": "7.0", "catégories/fiction": "10.0", "catégories/magazine": "7.0", "catégories/programme d'information": "10.0"}, {"catégories/documentaire tv": "5.4", "catégories/fiction": "5.9", "catégories/magazine": "5.7", "sujets/société": "8.5"}, {"catégories/documentaire tv": "6.0", "catégories/fiction": "6.0", "catégories/programme d'information": "10.0", "sujets/société": "3.0"}, {"catégories/documentaire tv": "6.1", "catégories/magazine": "6.4", "catégories/émissions": "3.7", "sujets/société": "7.8"}, {"catégories/documentaire tv": "6.5", "sujets/actualité": "7.4", "sujets/société": "8.1"}, {"catégories/documentaire tv": "10.0", "catégories/magazine": "4.0", "catégories/émissions": "4.0", "sujets/société": "3.0"}, {"catégories/documentaire tv": "9.8", "catégories/programme d'information": "9.8", "catégories/émissions": "3.3", "sujets/société": "9.3"}, {"catégories/documentaire tv": "6.4", "catégories/fiction": "8.0", "catégories/magazine": "7.0", "sujets/actualité": "9.6", "sujets/société": "9.9"}, {"catégories/documentaire tv": "7.0", "catégories/magazine": "7.1", "catégories/émissions": "4.2", "sujets/actualité": "8.1"}, {"catégories/documentaire tv": "6.1", "catégories/fiction": "6.6", "sujets/actualité": "7.4", "sujets/société": "7.9"}, {"catégories/documentaire tv": "2.0", "catégories/fiction": "2.0", "catégories/programme d'information": "9.0", "sujets/société": "7.0"}, {"catégories/documentaire tv": "7.0", "catégories/magazine": "7.0", "catégories/émissions": "4.5", "sujets/actualité": "10.0", "sujets/société": "5.0"}, {"catégories/documentaire tv": "3.6", "catégories/magazine": "9.0", "catégories/émissions": "3.6", "sujets/actualité": "6.9"}, {"catégories/documentaire tv": "10.0", "catégories/programme d'information": "2.0", "catégories/émissions": "10.0", "sujets/actualité": "2.0", "sujets/société": "7.0"}, {"catégories/documentaire tv": "4.0", "catégories/fiction": "4.0", "catégories/programme d'information": "4.0", "sujets/actualité": "10.0", "sujets/société": "5.0"}, {"catégories/documentaire tv": "3.0", "catégories/magazine": "3.0", "catégories/programme d'information": "10.0", "sujets/actualité": "2.0"}, {"catégories/documentaire tv": "4.4", "catégories/programme d'information": "7.0", "catégories/émissions": "4.4", "sujets/société": "7.7"}, {"catégories/documentaire tv": "8.0", "catégories/fiction": "4.0", "catégories/magazine": "4.0", "sujets/société": "10.0"}, {"catégories/documentaire tv": "6.3", "catégories/fiction": "6.7", "sujets/société": "7.8"}, {"catégories/documentaire tv": "6.9", "catégories/magazine": "7.1"}, {"catégories/documentaire tv": "5.0", "catégories/fiction": "2.0", "catégories/émissions": "5.0", "sujets/actualité": "10.0", "sujets/société": "5.0"}, {"catégories/documentaire tv": "8.0", "catégories/fiction": "10.0", "sujets/actualité": "3.0", "sujets/société": "3.0"}, {"catégories/documentaire tv": "5.0", "catégories/magazine": "10.0", "catégories/programme d'information": "10.0", "catégories/émissions": "5.0", "sujets/actualité": "10.0"}, {"catégories/documentaire tv": "10.0", "catégories/fiction": "4.0", "catégories/magazine": "4.0", "catégories/émissions": "4.0"}, {"catégories/documentaire tv": "3.0", "catégories/magazine": "7.0", "catégories/programme d'information": "3.0", "catégories/émissions": "3.0"}, {"catégories/documentaire tv": "8.1", "catégories/magazine": "3.4", "catégories/émissions": "3.4", "sujets/actualité": "5.1", "sujets/société": "4.9"}, {"catégories/documentaire tv": "2.0", "catégories/programme d'information": "5.0", "catégories/émissions": "2.0", "sujets/actualité": "5.0", "sujets/société": "10.0"}, {"catégories/documentaire tv": "7.0", "catégories/magazine": "7.0", "catégories/émissions": "4.3", "sujets/actualité": "4.8", "sujets/société": "10.0"}, {"catégories/documentaire tv": "9.3", "catégories/programme d'information": "3.6", "catégories/émissions": "3.6", "sujets/société": "4.4"}, {"catégories/documentaire tv": "5.0", "catégories/programme d'information": "6.8", "sujets/actualité": "8.2", "sujets/société": "6.7"}, {"catégories/documentaire tv": "4.0", "catégories/magazine": "2.0", "catégories/émissions": "2.0", "sujets/actualité": "10.0", "sujets/société": "3.0"}, {"catégories/documentaire tv": "3.5", "catégories/fiction": "3.5", "catégories/programme d'information": "5.0", "sujets/société": "10.0"}, {"catégories/documentaire tv": "3.3", "catégories/magazine": "3.5", "catégories/programme d'information": "4.5", "sujets/actualité": "10.0", "sujets/société": "4.7"}, {"catégories/documentaire tv": "3.0", "catégories/fiction": "6.0", "catégories/programme d'information": "3.0", "sujets/société": "6.0"}, {"catégories/documentaire tv": "6.5", "catégories/magazine": "6.6", "catégories/émissions": "3.8", "sujets/actualité": "9.4", "sujets/société": "9.9"}, {"catégories/documentaire tv": "6.9", "catégories/magazine": "7.6", "sujets/actualité": "6.7"}, {"catégories/documentaire tv": "2.0", "catégories/magazine": "10.0", "catégories/programme d'information": "2.0", "sujets/actualité": "5.0"}, {"catégories/documentaire tv": "2.0", "catégories/magazine": "8.0", "catégories/programme d'information": "2.0", "catégories/émissions": "2.0", "sujets/société": "7.0"}, {"catégories/documentaire tv": "2.6", "catégories/magazine": "6.2", "catégories/émissions": "2.6", "sujets/actualité": "10.0", "sujets/société": "5.2"}, {"catégories/documentaire tv": "6.6", "catégories/fiction": "7.0", "catégories/magazine": "7.5", "sujets/actualité": "7.9"}, {"catégories/documentaire tv": "5.3", "catégories/émissions": "4.7", "sujets/actualité": "7.3", "sujets/société": "8.8"}, {"catégories/documentaire tv": "9.0", "catégories/programme d'information": "5.0", "catégories/émissions": "9.0", "sujets/actualité": "2.0", "sujets/société": "10.0"}, {"catégories/documentaire tv": "3.0", "catégories/magazine": "4.0", "catégories/programme d'information": "4.0", "sujets/actualité": "4.0", "sujets/société": "10.0"}, {"catégories/documentaire tv": "6.0", "catégories/fiction": "6.0", "catégories/programme d'information": "10.0", "sujets/société": "7.0"}, {"catégories/documentaire tv": "3.0", "catégories/magazine": "3.0", "catégories/programme d'information": "10.0", "sujets/actualité": "2.0", "sujets/société": "2.0"}, {"catégories/documentaire tv": "5.0", "catégories/fiction": "5.0", "catégories/magazine": "10.0", "sujets/société": "6.0"}, {"catégories/documentaire tv": "3.0", "catégories/magazine": "3.0", "catégories/programme d'information": "4.0", "sujets/actualité": "9.0", "sujets/société": "10.0"}, {"catégories/documentaire tv": "7.0", "catégories/fiction": "10.0", "catégories/magazine": "7.0", "catégories/programme d'information": "10.0", "sujets/actualité": "10.0"}, {"catégories/documentaire tv": "6.5", "catégories/fiction": "6.3", "catégories/programme d'information": "6.5", "sujets/société": "8.3"}, {"catégories/documentaire tv": "5.0", "catégories/fiction": "5.0", "catégories/magazine": "6.0", "catégories/programme d'information": "5.0"}, {"catégories/documentaire tv": "5.3", "catégories/magazine": "6.0", "sujets/actualité": "7.5", "sujets/société": "8.4"}, {"catégories/documentaire tv": "5.9", "catégories/magazine": "6.5", "sujets/société": "8.3"}, {"catégories/documentaire tv": "5.0", "catégories/fiction": "5.0", "catégories/magazine": "5.0", "catégories/programme d'information": "9.0"}, {"catégories/documentaire tv": "5.0", "catégories/fiction": "5.0", "catégories/programme d'information": "9.0", "catégories/émissions": "5.0", "sujets/société": "10.0"}, {"catégories/documentaire tv": "3.0", "catégories/magazine": "3.0", "catégories/programme d'information": "3.0", "catégories/émissions": "2.0", "sujets/société": "10.0"}, {"catégories/documentaire tv": "5.5", "catégories/fiction": "6.6", "catégories/émissions": "5.5", "sujets/société": "7.9"}, {"catégories/documentaire tv": "6.4", "catégories/magazine": "6.9", "catégories/programme d'information": "7.3"}, {"catégories/documentaire tv": "7.0", "catégories/fiction": "2.5", "sujets/actualité": "10.0", "sujets/société": "2.5"}, {"catégories/documentaire tv": "5.0", "catégories/magazine": "5.0", "catégories/programme d'information": "3.0", "catégories/émissions": "3.0"}, {"catégories/documentaire tv": "6.2", "catégories/magazine": "6.3", "catégories/programme d'information": "8.7", "sujets/actualité": "8.9"}, {"catégories/documentaire tv": "5.0", "catégories/fiction": "2.3", "catégories/programme d'information": "2.3", "catégories/émissions": "5.0", "sujets/société": "10.0"}, {"catégories/documentaire tv": "7.0", "catégories/fiction": "6.0", "catégories/programme d'information": "6.0", "sujets/actualité": "6.0", "sujets/société": "10.0"}, {"catégories/documentaire tv": "9.0", "catégories/fiction": "5.0", "catégories/magazine": "9.0", "sujets/société": "5.0"}, {"catégories/documentaire tv": "5.0", "catégories/fiction": "7.0", "catégories/programme d'information": "6.5", "catégories/émissions": "5.0", "sujets/société": "10.0"}, {"catégories/documentaire tv": "6.0", "catégories/fiction": "4.0", "catégories/magazine": "6.0", "catégories/programme d'information": "4.0", "sujets/actualité": "8.0"}, {"catégories/documentaire tv": "5.0", "catégories/fiction": "2.0", "catégories/émissions": "5.0", "sujets/actualité": "10.0", "sujets/société": "2.0"}, {"catégories/documentaire tv": "3.0", "catégories/magazine": "7.0", "catégories/programme d'information": "7.0", "catégories/émissions": "3.0"}], "4": [{"catégories/programme événementiel": "9.8", "catégories/sport": "6.3", "sujets/pétanque": "4.9", "sujets/sport": "4.5"}, {"catégories/documentaire tv": "8.0", "catégories/programme événementiel": "10.0", "catégories/sport": "8.0", "sujets/pétanque": "7.0"}, {"catégories/documentaire tv": "4.0", "catégories/magazine": "4.0", "catégories/programme événementiel": "5.0", "catégories/sport": "4.0", "sujets/sport": "10.0"}, {"catégories/documentaire tv": "4.0", "catégories/programme événementiel": "4.0", "catégories/sport": "10.0", "sujets/sport": "2.0"}, {"catégories/magazine": "8.8", "catégories/programme événementiel": "9.8", "catégories/sport": "8.3", "sujets/pétanque": "5.5"}, {"catégories/magazine": "4.1", "catégories/programme événementiel": "10.0", "catégories/sport": "4.1", "sujets/sport": "3.3"}, {"catégories/sport": "9.0", "sujets/pétanque": "6.0", "sujets/sport": "2.0"}, {"catégories/documentaire tv": "7.0", "catégories/programme événementiel": "10.0", "catégories/sport": "7.1", "sujets/sport": "4.4"}, {"catégories/magazine": "8.0", "catégories/sport": "7.5", "sujets/sport": "8.3"}, {"catégories/documentaire tv": "7.0", "catégories/programme événementiel": "10.0", "catégories/sport": "7.0", "sujets/pétanque": "5.0", "sujets/sport": "5.0"}, {"catégories/documentaire tv": "5.0", "catégories/magazine": "5.0", "catégories/sport": "10.0", "sujets/sport": "6.0"}, {"catégories/magazine": "7.4", "catégories/programme événementiel": "8.9", "catégories/sport": "7.3"}, {"catégories/magazine": "3.5", "catégories/programme événementiel": "10.0", "catégories/sport": "10.0", "sujets/pétanque": "4.5"}, {"catégories/magazine": "3.4", "catégories/programme événementiel": "4.4", "catégories/sport": "3.0", "sujets/sport": "10.0"}, {"catégories/documentaire tv": "8.0", "catégories/magazine": "4.0", "catégories/sport": "8.0", "sujets/sport": "5.0"}, {"catégories/magazine": "5.0", "catégories/programme événementiel": "5.0", "catégories/sport": "10.0", "sujets/pétanque": "4.0"}, {"catégories/documentaire tv": "7.4", "catégories/magazine": "6.8", "catégories/programme événementiel": "8.4", "catégories/sport": "6.7"}, {"catégories/documentaire tv": "7.4", "catégories/sport": "7.1", "sujets/sport": "9.1"}, {"catégories/documentaire tv": "6.4", "catégories/magazine": "9.5", "catégories/programme événementiel": "5.5", "catégories/sport": "9.5"}, {"catégories/programme événementiel": "9.6", "catégories/sport": "6.7"}, {"catégories/documentaire tv": "7.1", "catégories/programme événementiel": "9.5", "catégories/sport": "7.0"}, {"catégories/sport": "8.1", "sujets/pétanque": "3.1", "sujets/sport": "3.1"}, {"catégories/magazine": "10.0", "catégories/programme événementiel": "5.5", "catégories/sport": "5.5", "sujets/sport": "2.0"}, {"catégories/documentaire tv": "2.0", "catégories/programme événementiel": "7.0", "catégories/sport": "2.0", "sujets/sport": "10.0"}, {"catégories/documentaire tv": "9.0", "catégories/sport": "7.0", "sujets/pétanque": "3.0", "sujets/sport": "3.0"}, {"catégories/magazine": "7.5", "catégories/programme événementiel": "10.0", "catégories/sport": "7.5", "sujets/pétanque": "5.0", "sujets/sport": "5.0"}, {"catégories/magazine": "7.0", "catégories/programme événementiel": "9.2", "catégories/sport": "7.4", "sujets/sport": "7.6"}, {"catégories/documentaire tv": "7.1", "catégories/magazine": "7.0", "catégories/sport": "7.1", "sujets/sport": "8.3"}, {"catégories/magazine": "9.1", "catégories/sport": "8.9", "sujets/pétanque": "3.2", "sujets/sport": "3.2"}, {"catégories/programme événementiel": "9.3", "catégories/sport": "8.0", "sujets/pétanque": "4.9"}, {"catégories/documentaire tv": "6.9", "catégories/programme événementiel": "9.5", "catégories/sport": "6.9", "sujets/sport": "10.0"}, {"catégories/documentaire tv": "6.0", "catégories/sport": "10.0", "sujets/pétanque": "2.2", "sujets/sport": "2.2"}, {"catégories/programme événementiel": "9.8", "catégories/sport": "5.8", "sujets/sport": "4.7"}, {"catégories/magazine": "10.0", "catégories/programme événementiel": "7.0", "catégories/sport": "10.0", "sujets/pétanque": "5.0"}, {"catégories/sport": "5.3", "sujets/sport": "7.2"}], "7": [{"catégories/programme musical": "10.0", "sujets/musique": "2.0", "type/concert": "9.9"}, {"catégories/documentaire tv": "4.3", "catégories/programme musical": "9.9", "sujets/musique": "2.3", "type/concert": "9.9"}, {"catégories/programme musical": "9.6", "sujets/musique": "4.9", "type/clip": "9.5"}, {"catégories/documentaire tv": "4.3", "catégories/programme musical": "9.7", "sujets/musique": "5.3", "type/clip": "9.7"}, {"catégories/magazine": "3.1", "catégories/programme musical": "9.8", "sujets/musique": "2.3", "type/concert": "9.8"}, {"catégories/documentaire tv": "5.3", "catégories/programme musical": "9.8", "sujets/musique": "4.5"}, {"catégories/magazine": "6.0", "catégories/programme musical": "9.6", "sujets/musique": "4.7"}, {"catégories/magazine": "4.1", "catégories/programme musical": "10.0", "sujets/musique": "5.3", "type/clip": "4.1"}, {"catégories/programme musical": "10.0", "sujets/musique": "5.7", "type/concert": "5.0"}, {"catégories/magazine": "5.0", "catégories/programme musical": "9.1", "sujets/musique": "4.9", "type/clip": "9.1"}, {"catégories/documentaire tv": "4.0", "catégories/programme musical": "2.0", "sujets/musique": "2.0", "type/concert": "2.0"}, {"catégories/documentaire tv": "7.0", "catégories/magazine": "7.5", "catégories/programme musical": "9.6", "sujets/musique": "4.9"}, {"catégories/programme musical": "9.3", "sujets/musique": "3.2"}, {"catégories/documentaire tv": "4.7", "catégories/magazine": "4.7", "catégories/programme musical": "9.7", "sujets/musique": "5.0", "type/clip": "9.7"}, {"catégories/programme musical": "10.0", "sujets/musique": "2.0", "type/clip": "5.0", "type/concert": "5.0"}], "8": [{"catégories/fiction": "9.7", "envies/montée d'adrénaline": "7.7", "genres/action": "9.7", "personnages/super-héros": "9.7", "personnages/super-pouvoir": "9.7", "sous-genres/science-fiction": "9.7"}, {"catégories/fiction": "10.0", "envies/montée d'adrénaline": "4.0", "genres/action": "10.0", "personnages/super-héros": "5.0", "personnages/super-pouvoir": "5.0", "sous-genres/science-fiction": "10.0"}, {"catégories/fiction": "9.4", "envies/montée d'adrénaline": "7.8", "personnages/méchant d'anthologie": "9.3", "personnages/super-héros": "9.3", "personnages/super-pouvoir": "9.3", "sous-genres/fantastique": "9.3", "sous-genres/science-fiction": "9.3"}, {"catégories/fiction": "9.5", "envies/montée d'adrénaline": "7.2", "genres/action": "7.2", "personnages/super-héros": "7.1"}, {"catégories/fiction": "10.0", "envies/montée d'adrénaline": "5.0", "genres/action": "10.0", "personnages/super-héros": "5.0", "sous-genres/fantastique": "10.0"}, {"catégories/fiction": "10.0", "envies/montée d'adrénaline": "5.0", "genres/action": "5.0", "personnages/méchant d'anthologie": "5.0", "personnages/super-héros": "5.0"}, {"personnages/méchant d'anthologie": "3.4", "personnages/super-héros": "3.4", "personnages/super-pouvoir": "3.4"}, {"catégories/fiction": "10.0", "envies/montée d'adrénaline": "5.0", "genres/action": "5.0", "personnages/super-héros": "5.0", "sous-genres/fantastique": "10.0", "sous-genres/science-fiction": "5.0"}, {"catégories/fiction": "9.7", "genres/action": "9.7", "personnages/super-héros": "5.0", "personnages/super-pouvoir": "5.0", "sous-genres/science-fiction": "5.0"}, {"catégories/fiction": "10.0", "genres/action": "5.0", "personnages/méchant d'anthologie": "3.0", "personnages/super-héros": "3.0", "personnages/super-pouvoir": "3.0", "sous-genres/fantastique": "3.0", "sous-genres/science-fiction": "3.0"}, {"catégories/fiction": "10.0", "genres/action": "10.0", "personnages/méchant d'anthologie": "6.0", "personnages/super-héros": "6.0"}, {"catégories/fiction": "6.0", "envies/montée d'adrénaline": "6.0", "genres/action": "6.0", "personnages/super-héros": "10.0", "sous-genres/fantastique": "6.0", "sous-genres/science-fiction": "6.0"}, {"catégories/fiction": "10.0", "envies/montée d'adrénaline": "5.0", "genres/action": "5.0", "personnages/super-héros": "5.0", "personnages/super-pouvoir": "5.0", "sous-genres/fantastique": "10.0", "sous-genres/science-fiction": "10.0"}, {"catégories/fiction": "10.0", "envies/montée d'adrénaline": "8.1", "genres/action": "10.0", "personnages/super-héros": "5.0", "personnages/super-pouvoir": "5.0", "sous-genres/fantastique": "10.0"}, {"catégories/fiction": "10.0", "genres/action": "3.0", "personnages/super-héros": "3.0", "sous-genres/fantastique": "3.0", "sous-genres/science-fiction": "7.0"}, {"envies/montée d'adrénaline": "4.6", "genres/action": "4.6", "personnages/super-héros": "4.6", "personnages/super-pouvoir": "4.6"}, {"envies/montée d'adrénaline": "4.0", "personnages/méchant d'anthologie": "4.3", "personnages/super-héros": "4.3", "personnages/super-pouvoir": "4.3", "sous-genres/fantastique": "4.3", "sous-genres/science-fiction": "4.3"}, {"catégories/fiction": "4.0", "envies/montée d'adrénaline": "10.0", "genres/action": "4.0", "personnages/super-héros": "4.0"}, {"catégories/fiction": "10.0", "genres/action": "5.0", "personnages/super-héros": "10.0", "personnages/super-pouvoir": "10.0", "sous-genres/fantastique": "10.0"}, {"envies/montée d'adrénaline": "6.5", "genres/action": "3.0", "personnages/super-héros": "3.0", "sous-genres/fantastique": "3.0"}, {"catégories/fiction": "5.0", "genres/action": "5.0", "personnages/super-héros": "10.0", "personnages/super-pouvoir": "10.0", "sous-genres/fantastique": "5.0"}, {"personnages/méchant d'anthologie": "3.7", "personnages/super-héros": "3.7", "personnages/super-pouvoir": "3.7", "sous-genres/fantastique": "3.7", "sous-genres/science-fiction": "3.7"}, {"catégories/fiction": "9.3", "envies/montée d'adrénaline": "8.7", "genres/action": "8.7", "personnages/super-héros": "8.8", "personnages/super-pouvoir": "8.8", "sous-genres/fantastique": "8.8", "sous-genres/science-fiction": "8.8"}, {"catégories/fiction": "6.9", "personnages/méchant d'anthologie": "6.8", "personnages/super-héros": "6.8", "sous-genres/fantastique": "6.8"}, {"personnages/méchant d'anthologie": "4.0", "personnages/super-héros": "6.0", "personnages/super-pouvoir": "6.0", "sous-genres/fantastique": "4.0", "sous-genres/science-fiction": "4.0"}, {"catégories/fiction": "10.0", "envies/montée d'adrénaline": "6.0", "personnages/méchant d'anthologie": "5.0", "personnages/super-héros": "5.0"}, {"catégories/fiction": "8.0", "envies/montée d'adrénaline": "10.0", "personnages/méchant d'anthologie": "8.0", "personnages/super-héros": "8.0", "personnages/super-pouvoir": "8.0", "sous-genres/fantastique": "8.0"}, {"personnages/méchant d'anthologie": "4.2", "personnages/super-héros": "4.2", "personnages/super-pouvoir": "4.2", "sous-genres/fantastique": "4.2"}, {"envies/montée d'adrénaline": "4.6", "genres/action": "4.6", "personnages/super-héros": "4.6", "personnages/super-pouvoir": "4.6", "sous-genres/fantastique": "4.6"}, {"envies/montée d'adrénaline": "4.3", "genres/action": "4.3", "personnages/super-héros": "4.3", "sous-genres/fantastique": "4.3"}, {"catégories/fiction": "9.3", "personnages/méchant d'anthologie": "8.8", "personnages/super-héros": "8.8", "personnages/super-pouvoir": "8.8", "sous-genres/science-fiction": "8.8"}, {"catégories/fiction": "9.8", "personnages/méchant d'anthologie": "3.8", "personnages/super-héros": "3.8", "sous-genres/fantastique": "9.5"}, {"genres/action": "5.0", "personnages/super-héros": "10.0", "personnages/super-pouvoir": "10.0"}, {"genres/action": "4.6", "personnages/méchant d'anthologie": "4.6", "personnages/super-héros": "4.6", "personnages/super-pouvoir": "4.6"}, {"personnages/méchant d'anthologie": "4.0", "personnages/super-héros": "4.0"}, {"personnages/méchant d'anthologie": "6.0", "personnages/super-héros": "3.0", "personnages/super-pouvoir": "3.0", "sous-genres/fantastique": "3.0"}, {"catégories/fiction": "9.0", "envies/montée d'adrénaline": "10.0", "genres/action": "9.0", "personnages/méchant d'anthologie": "9.0", "personnages/super-héros": "9.0"}, {"catégories/fiction": "9.8", "envies/montée d'adrénaline": "9.6", "genres/action": "9.6", "personnages/super-héros": "4.8", "personnages/super-pouvoir": "4.8", "sous-genres/fantastique": "9.6", "sous-genres/science-fiction": "9.6"}, {"catégories/fiction": "9.2", "genres/action": "6.6", "personnages/super-héros": "4.5", "sous-genres/fantastique": "4.5"}, {"catégories/fiction": "5.0", "personnages/méchant d'anthologie": "5.0", "personnages/super-héros": "5.0", "personnages/super-pouvoir": "10.0", "sous-genres/science-fiction": "5.0"}, {"catégories/fiction": "10.0", "personnages/méchant d'anthologie": "3.1", "personnages/super-héros": "3.1", "sous-genres/fantastique": "3.1", "sous-genres/science-fiction": "9.8"}, {"catégories/fiction": "10.0", "genres/action": "4.3", "personnages/super-héros": "3.9", "sous-genres/science-fiction": "4.2"}, {"catégories/fiction": "10.0", "personnages/méchant d'anthologie": "7.0", "personnages/super-héros": "7.0", "personnages/super-pouvoir": "7.0", "sous-genres/fantastique": "3.0", "sous-genres/science-fiction": "7.0"}, {"genres/action": "4.5", "personnages/super-héros": "4.5", "sous-genres/fantastique": "4.5", "sous-genres/science-fiction": "4.5"}, {"genres/action": "4.0", "personnages/super-héros": "3.9", "personnages/super-pouvoir": "3.9"}, {"catégories/fiction": "5.0", "personnages/méchant d'anthologie": "5.0", "personnages/super-héros": "10.0", "personnages/super-pouvoir": "10.0", "sous-genres/science-fiction": "5.0"}, {"catégories/fiction": "9.2", "genres/action": "4.6", "personnages/méchant d'anthologie": "4.5", "personnages/super-héros": "4.5"}, {"catégories/fiction": "10.0", "envies/montée d'adrénaline": "10.0", "genres/action": "10.0", "personnages/super-héros": "5.0", "personnages/super-pouvoir": "5.0", "sous-genres/fantastique": "5.0", "sous-genres/science-fiction": "5.0"}, {"catégories/fiction": "10.0", "personnages/méchant d'anthologie": "4.3", "personnages/super-héros": "3.7", "sous-genres/science-fiction": "4.0"}, {"genres/action": "3.7", "personnages/super-héros": "3.7", "sous-genres/fantastique": "3.7"}, {"catégories/fiction": "10.0", "envies/montée d'adrénaline": "5.9", "personnages/méchant d'anthologie": "5.4", "personnages/super-héros": "5.4", "personnages/super-pouvoir": "5.4", "sous-genres/fantastique": "5.4", "sous-genres/science-fiction": "5.4"}, {"catégories/fiction": "10.0", "envies/montée d'adrénaline": "4.5", "genres/action": "10.0", "personnages/méchant d'anthologie": "4.5", "personnages/super-héros": "4.5", "personnages/super-pouvoir": "4.5"}, {"genres/action": "4.2", "personnages/super-héros": "4.2", "personnages/super-pouvoir": "4.2", "sous-genres/science-fiction": "4.2"}, {"catégories/fiction": "10.0", "genres/action": "3.0", "personnages/méchant d'anthologie": "3.0", "personnages/super-héros": "7.0", "personnages/super-pouvoir": "3.0"}, {"catégories/fiction": "9.1", "envies/montée d'adrénaline": "8.0", "genres/action": "8.1", "personnages/super-héros": "7.9", "sous-genres/fantastique": "8.0", "sous-genres/science-fiction": "8.0"}, {"catégories/fiction": "9.0", "envies/montée d'adrénaline": "10.0", "personnages/méchant d'anthologie": "6.3", "personnages/super-héros": "6.3"}, {"catégories/fiction": "5.0", "envies/montée d'adrénaline": "5.0", "genres/action": "10.0", "personnages/super-héros": "5.0", "personnages/super-pouvoir": "5.0", "sous-genres/fantastique": "5.0", "sous-genres/science-fiction": "5.0"}, {"catégories/fiction": "9.7", "genres/action": "5.5", "personnages/super-héros": "5.7", "personnages/super-pouvoir": "5.7", "sous-genres/fantastique": "5.8"}, {"catégories/fiction": "7.0", "envies/montée d'adrénaline": "8.7", "genres/action": "7.0", "personnages/méchant d'anthologie": "7.0", "personnages/super-héros": "7.0", "personnages/super-pouvoir": "7.0"}, {"genres/action": "3.7", "personnages/super-héros": "3.6", "personnages/super-pouvoir": "3.6", "sous-genres/fantastique": "3.6"}, {"catégories/fiction": "8.9", "genres/action": "7.0", "personnages/super-héros": "7.0", "personnages/super-pouvoir": "7.0", "sous-genres/fantastique": "7.0", "sous-genres/science-fiction": "7.0"}, {"catégories/fiction": "9.5", "personnages/méchant d'anthologie": "4.1", "personnages/super-héros": "4.0", "sous-genres/fantastique": "4.1"}, {"catégories/fiction": "10.0", "personnages/méchant d'anthologie": "5.0", "personnages/super-héros": "5.0", "sous-genres/fantastique": "9.9", "sous-genres/science-fiction": "9.9"}, {"catégories/fiction": "10.0", "genres/action": "3.0", "personnages/méchant d'anthologie": "3.0", "personnages/super-héros": "3.0", "sous-genres/fantastique": "6.0"}, {"catégories/fiction": "7.0", "envies/montée d'adrénaline": "3.0", "genres/action": "3.0", "personnages/super-héros": "3.0", "personnages/super-pouvoir": "3.0", "sous-genres/fantastique": "7.0", "sous-genres/science-fiction": "3.0"}, {"personnages/méchant d'anthologie": "4.4", "personnages/super-héros": "4.4", "sous-genres/fantastique": "4.4"}, {"catégories/fiction": "5.7", "genres/action": "10.0", "personnages/méchant d'anthologie": "5.7", "personnages/super-héros": "5.7", "personnages/super-pouvoir": "5.7", "sous-genres/science-fiction": "5.7"}, {"catégories/fiction": "5.0", "envies/montée d'adrénaline": "5.0", "personnages/méchant d'anthologie": "5.0", "personnages/super-héros": "10.0", "personnages/super-pouvoir": "10.0", "sous-genres/fantastique": "5.0"}, {"catégories/fiction": "5.0", "personnages/méchant d'anthologie": "5.0", "personnages/super-héros": "5.0", "personnages/super-pouvoir": "5.0", "sous-genres/fantastique": "10.0", "sous-genres/science-fiction": "5.0"}, {"envies/montée d'adrénaline": "3.0", "genres/action": "3.0", "personnages/super-héros": "7.0", "sous-genres/fantastique": "7.0", "sous-genres/science-fiction": "7.0"}, {"catégories/fiction": "6.0", "envies/montée d'adrénaline": "5.0", "personnages/méchant d'anthologie": "6.0", "personnages/super-héros": "6.0", "personnages/super-pouvoir": "6.0", "sous-genres/science-fiction": "6.0"}, {"catégories/fiction": "9.3", "genres/action": "7.6", "personnages/méchant d'anthologie": "7.1", "personnages/super-héros": "7.2", "personnages/super-pouvoir": "7.2"}, {"catégories/fiction": "9.3", "personnages/méchant d'anthologie": "8.7", "personnages/super-héros": "8.7", "personnages/super-pouvoir": "8.7", "sous-genres/fantastique": "8.8", "sous-genres/science-fiction": "8.7"}, {"catégories/fiction": "10.0", "personnages/méchant d'anthologie": "4.0", "personnages/super-héros": "4.0", "sous-genres/fantastique": "9.9", "sous-genres/science-fiction": "4.0"}, {"envies/montée d'adrénaline": "4.0", "personnages/méchant d'anthologie": "4.0", "personnages/super-héros": "4.0", "personnages/super-pouvoir": "4.0", "sous-genres/fantastique": "4.0"}, {"catégories/fiction": "10.0", "envies/montée d'adrénaline": "4.8", "genres/action": "10.0", "personnages/super-héros": "5.0", "personnages/super-pouvoir": "5.0", "sous-genres/fantastique": "5.0", "sous-genres/science-fiction": "5.0"}, {"catégories/fiction": "5.0", "personnages/méchant d'anthologie": "5.0", "personnages/super-héros": "5.0", "personnages/super-pouvoir": "10.0", "sous-genres/fantastique": "5.0"}, {"catégories/fiction": "9.8", "envies/montée d'adrénaline": "4.4", "genres/action": "4.5", "personnages/super-héros": "4.3", "personnages/super-pouvoir": "4.3", "sous-genres/fantastique": "4.3"}, {"catégories/fiction": "10.0", "personnages/méchant d'anthologie": "4.9", "personnages/super-héros": "9.7", "sous-genres/fantastique": "9.7", "sous-genres/science-fiction": "9.7"}, {"catégories/fiction": "10.0", "personnages/méchant d'anthologie": "2.0", "personnages/super-héros": "2.0", "personnages/super-pouvoir": "2.0", "sous-genres/fantastique": "10.0", "sous-genres/science-fiction": "2.0"}, {"genres/action": "4.2", "personnages/super-héros": "4.2", "personnages/super-pouvoir": "4.2", "sous-genres/fantastique": "4.2", "sous-genres/science-fiction": "4.2"}, {"catégories/fiction": "7.0", "genres/action": "9.7", "personnages/méchant d'anthologie": "7.0", "personnages/super-héros": "7.0", "personnages/super-pouvoir": "7.0", "sous-genres/fantastique": "7.0", "sous-genres/science-fiction": "7.0"}, {"catégories/fiction": "10.0", "personnages/méchant d'anthologie": "10.0", "personnages/super-héros": "5.0", "sous-genres/fantastique": "10.0"}, {"catégories/fiction": "5.0", "envies/montée d'adrénaline": "10.0", "personnages/méchant d'anthologie": "5.0", "personnages/super-héros": "5.0", "personnages/super-pouvoir": "5.0", "sous-genres/fantastique": "5.0"}, {"catégories/fiction": "8.6", "genres/action": "5.2", "personnages/super-héros": "5.0", "sous-genres/fantastique": "5.5", "sous-genres/science-fiction": "5.3"}, {"envies/montée d'adrénaline": "4.8", "genres/action": "4.7", "personnages/super-héros": "4.7", "sous-genres/fantastique": "4.7", "sous-genres/science-fiction": "4.7"}, {"genres/action": "3.8", "personnages/super-héros": "3.8"}, {"catégories/fiction": "10.0", "envies/montée d'adrénaline": "5.0", "genres/action": "3.0", "personnages/super-héros": "3.0", "personnages/super-pouvoir": "3.0", "sous-genres/science-fiction": "6.0"}, {"catégories/fiction": "10.0", "genres/action": "5.0", "personnages/méchant d'anthologie": "5.0", "personnages/super-héros": "10.0", "personnages/super-pouvoir": "5.0"}, {"catégories/fiction": "7.0", "envies/montée d'adrénaline": "5.0", "personnages/méchant d'anthologie": "4.0", "personnages/super-héros": "4.0", "personnages/super-pouvoir": "4.0"}, {"catégories/fiction": "9.2", "personnages/méchant d'anthologie": "4.8", "personnages/super-héros": "4.7", "personnages/super-pouvoir": "4.7"}, {"catégories/fiction": "5.0", "personnages/méchant d'anthologie": "5.0", "personnages/super-héros": "10.0", "personnages/super-pouvoir": "10.0", "sous-genres/fantastique": "5.0", "sous-genres/science-fiction": "5.0"}, {"catégories/fiction": "10.0", "envies/montée d'adrénaline": "6.0", "genres/action": "3.0", "personnages/super-héros": "3.0", "personnages/super-pouvoir": "3.0", "sous-genres/fantastique": "3.0", "sous-genres/science-fiction": "3.0"}, {"catégories/fiction": "10.0", "envies/montée d'adrénaline": "9.0", "personnages/méchant d'anthologie": "5.0", "personnages/super-héros": "5.0", "personnages/super-pouvoir": "5.0", "sous-genres/fantastique": "10.0"}, {"catégories/fiction": "10.0", "personnages/méchant d'anthologie": "10.0", "personnages/super-héros": "10.0", "personnages/super-pouvoir": "10.0", "sous-genres/fantastique": "5.0"}, {"catégories/fiction": "10.0", "envies/montée d'adrénaline": "7.0", "personnages/méchant d'anthologie": "10.0", "personnages/super-héros": "6.0", "personnages/super-pouvoir": "6.0", "sous-genres/fantastique": "6.0", "sous-genres/science-fiction": "6.0"}, {"catégories/fiction": "10.0", "personnages/méchant d'anthologie": "7.0", "personnages/super-héros": "3.0", "personnages/super-pouvoir": "3.0", "sous-genres/fantastique": "7.0"}, {"catégories/fiction": "9.5", "genres/action": "9.4", "personnages/super-héros": "9.4", "personnages/super-pouvoir": "9.4", "sous-genres/science-fiction": "9.4"}, {"catégories/fiction": "10.0", "genres/action": "4.7", "personnages/méchant d'anthologie": "4.0", "personnages/super-héros": "4.0", "personnages/super-pouvoir": "4.0", "sous-genres/fantastique": "4.2"}, {"catégories/fiction": "9.4", "genres/action": "4.8", "personnages/super-héros": "4.2"}, {"catégories/fiction": "10.0", "genres/action": "5.0", "personnages/super-héros": "5.0", "personnages/super-pouvoir": "5.0", "sous-genres/fantastique": "10.0", "sous-genres/science-fiction": "10.0"}, {"catégories/fiction": "10.0", "personnages/méchant d'anthologie": "5.0", "personnages/super-héros": "5.0", "personnages/super-pouvoir": "10.0", "sous-genres/fantastique": "10.0", "sous-genres/science-fiction": "5.0"}, {"genres/action": "4.0", "personnages/super-héros": "2.0", "personnages/super-pouvoir": "2.0"}, {"catégories/fiction": "10.0", "envies/montée d'adrénaline": "4.0", "genres/action": "5.0", "personnages/super-héros": "5.0", "personnages/super-pouvoir": "5.0", "sous-genres/science-fiction": "5.0"}, {"catégories/fiction": "9.9", "genres/action": "4.5", "personnages/super-héros": "9.1", "personnages/super-pouvoir": "4.5", "sous-genres/fantastique": "9.1"}, {"catégories/fiction": "10.0", "envies/montée d'adrénaline": "4.0", "personnages/méchant d'anthologie": "5.0", "personnages/super-héros": "5.0", "personnages/super-pouvoir": "5.0", "sous-genres/fantastique": "10.0"}, {"catégories/fiction": "9.5", "envies/montée d'adrénaline": "9.5", "genres/action": "4.6", "personnages/super-héros": "4.6"}, {"catégories/fiction": "10.0", "envies/montée d'adrénaline": "3.6", "genres/action": "5.1", "personnages/méchant d'anthologie": "5.1", "personnages/super-héros": "5.1", "personnages/super-pouvoir": "5.1"}, {"envies/montée d'adrénaline": "7.0", "genres/action": "7.0", "personnages/super-héros": "3.0", "sous-genres/fantastique": "3.0", "sous-genres/science-fiction": "3.0"}, {"envies/montée d'adrénaline": "10.0", "genres/action": "4.0", "personnages/super-héros": "4.0"}, {"personnages/méchant d'anthologie": "5.0", "personnages/super-héros": "5.0", "personnages/super-pouvoir": "5.0", "sous-genres/fantastique": "5.0", "sous-genres/science-fiction": "10.0"}, {"catégories/fiction": "9.3", "envies/montée d'adrénaline": "5.7", "personnages/méchant d'anthologie": "5.7", "personnages/super-héros": "4.7", "personnages/super-pouvoir": "4.7", "sous-genres/fantastique": "4.7"}, {"catégories/fiction": "10.0", "genres/action": "5.0", "personnages/super-héros": "5.0", "personnages/super-pouvoir": "5.0", "sous-genres/science-fiction": "10.0"}, {"catégories/fiction": "3.0", "genres/action": "3.0", "personnages/super-héros": "6.0"}, {"catégories/fiction": "10.0", "genres/action": "10.0", "personnages/super-héros": "5.0", "personnages/super-pouvoir": "5.0", "sous-genres/fantastique": "5.0", "sous-genres/science-fiction": "5.0"}, {"catégories/fiction": "9.4", "personnages/méchant d'anthologie": "3.9", "personnages/super-héros": "3.9"}, {"envies/montée d'adrénaline": "6.5", "genres/action": "6.5", "personnages/super-héros": "3.5"}, {"catégories/fiction": "5.0", "envies/montée d'adrénaline": "10.0", "genres/action": "5.0", "personnages/super-héros": "5.0", "sous-genres/fantastique": "10.0", "sous-genres/science-fiction": "5.0"}, {"catégories/fiction": "10.0", "envies/montée d'adrénaline": "10.0", "genres/action": "5.0", "personnages/super-héros": "5.0", "sous-genres/fantastique": "10.0"}, {"envies/montée d'adrénaline": "4.0", "personnages/méchant d'anthologie": "2.0", "personnages/super-héros": "2.0", "personnages/super-pouvoir": "2.0", "sous-genres/fantastique": "2.0", "sous-genres/science-fiction": "2.0"}, {"catégories/fiction": "10.0", "envies/montée d'adrénaline": "10.0", "genres/action": "5.0", "personnages/super-héros": "5.0", "personnages/super-pouvoir": "5.0", "sous-genres/fantastique": "10.0"}, {"catégories/fiction": "10.0", "genres/action": "3.5", "personnages/méchant d'anthologie": "5.5", "personnages/super-héros": "5.5", "personnages/super-pouvoir": "5.5", "sous-genres/fantastique": "5.5", "sous-genres/science-fiction": "5.5"}, {"catégories/fiction": "9.4", "personnages/méchant d'anthologie": "7.1", "personnages/super-héros": "7.1", "personnages/super-pouvoir": "7.1", "sous-genres/fantastique": "7.2"}, {"catégories/fiction": "10.0", "genres/action": "4.1", "personnages/super-héros": "4.1", "personnages/super-pouvoir": "4.1", "sous-genres/science-fiction": "4.1"}, {"catégories/fiction": "9.0", "envies/montée d'adrénaline": "7.6", "genres/action": "7.5", "personnages/super-héros": "7.1", "personnages/super-pouvoir": "7.1"}, {"personnages/méchant d'anthologie": "3.5", "personnages/super-héros": "3.5", "personnages/super-pouvoir": "3.5", "sous-genres/science-fiction": "3.5"}, {"genres/action": "3.9", "personnages/méchant d'anthologie": "3.9", "personnages/super-héros": "3.9"}, {"catégories/fiction": "10.0", "personnages/méchant d'anthologie": "5.0", "personnages/super-héros": "10.0", "personnages/super-pouvoir": "10.0", "sous-genres/fantastique": "8.3", "sous-genres/science-fiction": "5.0"}, {"catégories/fiction": "9.1", "envies/montée d'adrénaline": "8.2", "genres/action": "8.1", "personnages/super-héros": "7.8", "sous-genres/fantastique": "8.0"}, {"catégories/fiction": "5.0", "personnages/méchant d'anthologie": "5.0", "personnages/super-héros": "10.0", "personnages/super-pouvoir": "5.0", "sous-genres/fantastique": "5.0", "sous-genres/science-fiction": "5.0"}, {"personnages/méchant d'anthologie": "5.4", "personnages/super-héros": "5.4", "sous-genres/fantastique": "5.4", "sous-genres/science-fiction": "5.4"}, {"catégories/fiction": "10.0", "envies/montée d'adrénaline": "5.0", "genres/action": "10.0", "personnages/méchant d'anthologie": "10.0", "personnages/super-héros": "10.0", "personnages/super-pouvoir": "10.0"}, {"catégories/fiction": "9.9", "envies/montée d'adrénaline": "9.6", "genres/action": "4.8", "personnages/super-héros": "4.8", "personnages/super-pouvoir": "4.8", "sous-genres/fantastique": "4.8", "sous-genres/science-fiction": "4.8"}, {"catégories/fiction": "8.7", "personnages/méchant d'anthologie": "3.7", "personnages/super-héros": "7.0", "personnages/super-pouvoir": "7.0", "sous-genres/fantastique": "3.7"}, {"catégories/fiction": "9.6", "genres/action": "7.3", "personnages/super-héros": "7.2", "personnages/super-pouvoir": "7.2"}, {"genres/action": "5.0", "personnages/super-héros": "5.0", "personnages/super-pouvoir": "9.0"}, {"envies/montée d'adrénaline": "3.9", "genres/action": "3.9", "personnages/super-héros": "3.9"}, {"personnages/méchant d'anthologie": "5.0", "personnages/super-héros": "5.0", "personnages/super-pouvoir": "5.0", "sous-genres/fantastique": "10.0", "sous-genres/science-fiction": "5.0"}, {"catégories/fiction": "5.0", "envies/montée d'adrénaline": "5.0", "genres/action": "5.0", "personnages/méchant d'anthologie": "5.0", "personnages/super-héros": "10.0", "personnages/super-pouvoir": "10.0"}, {"catégories/fiction": "8.5", "personnages/méchant d'anthologie": "4.0", "personnages/super-héros": "4.0", "personnages/super-pouvoir": "4.0", "sous-genres/fantastique": "4.0", "sous-genres/science-fiction": "8.5"}, {"catégories/fiction": "5.0", "envies/montée d'adrénaline": "5.0", "genres/action": "5.0", "personnages/super-héros": "5.0", "personnages/super-pouvoir": "10.0", "sous-genres/fantastique": "5.0", "sous-genres/science-fiction": "5.0"}, {"catégories/fiction": "9.6", "personnages/méchant d'anthologie": "8.4", "personnages/super-héros": "8.2", "sous-genres/fantastique": "8.4", "sous-genres/science-fiction": "8.4"}, {"catégories/fiction": "10.0", "envies/montée d'adrénaline": "10.0", "genres/action": "10.0", "personnages/DC comics": "5.0", "personnages/super-héros": "5.0", "sous-genres/fantastique": "10.0", "sous-genres/science-fiction": "10.0"}, {"catégories/fiction": "10.0", "envies/montée d'adrénaline": "6.0", "genres/action": "6.0", "personnages/super-héros": "4.0", "sous-genres/science-fiction": "4.0"}, {"catégories/fiction": "10.0", "personnages/méchant d'anthologie": "6.3", "personnages/super-héros": "6.4", "personnages/super-pouvoir": "5.1", "sous-genres/fantastique": "9.6", "sous-genres/science-fiction": "9.6"}, {"catégories/fiction": "7.5", "envies/montée d'adrénaline": "7.5", "genres/action": "7.5", "personnages/super-héros": "7.5", "personnages/super-pouvoir": "7.5", "sous-genres/fantastique": "7.5"}, {"catégories/fiction": "5.0", "envies/montée d'adrénaline": "10.0", "genres/action": "5.0", "personnages/super-héros": "5.0", "personnages/super-pouvoir": "5.0", "sous-genres/fantastique": "5.0"}], "9": [{"ambiance et ton/humoristique": "8.3", "envies/éclats de rire": "7.6", "genres/comédie": "8.1"}, {"ambiance et ton/humoristique": "8.0", "genres/comédie": "7.5"}], "10": [{"ambiance et ton/suspense": "8.6", "genres/thriller": "8.3", "sous-genres/policier": "8.5"}, {"ambiance et ton/angoissant": "8.6", "ambiance et ton/suspense": "8.8", "genres/thriller": "8.7", "sous-genres/policier": "8.7"}, {"ambiance et ton/angoissant": "7.2", "ambiance et ton/suspense": "8.0", "genres/thriller": "8.1"}, {"ambiance et ton/suspense": "7.4", "genres/thriller": "7.1"}, {"ambiance et ton/suspense": "7.9", "sous-genres/policier": "8.1"}, {"ambiance et ton/angoissant": "7.6", "ambiance et ton/suspense": "8.6", "sous-genres/policier": "8.7"}], "11": [{"catégories/magazine": "8.7", "sujets/voyage et évasion": "7.0", "thèmes/aux 4 coins du monde": "5.4", "type/reportage": "6.6"}, {"thèmes/aux 4 coins du monde": "3.1", "type/reportage": "9.6"}, {"catégories/magazine": "8.0", "thèmes/aux 4 coins du monde": "3.8", "type/reportage": "8.3"}, {"sujets/voyage et évasion": "8.0", "thèmes/aux 4 coins du monde": "8.0", "type/reportage": "8.5"}, {"catégories/documentaire tv": "4.3", "catégories/magazine": "4.7", "sujets/voyage et évasion": "9.4", "thèmes/aux 4 coins du monde": "5.1"}, {"catégories/magazine": "7.8", "thèmes/aux 4 coins du monde": "3.9"}, {"catégories/magazine": "6.1", "sujets/voyage et évasion": "8.4", "thèmes/aux 4 coins du monde": "7.3"}, {"catégories/documentaire tv": "6.1", "catégories/magazine": "6.2", "thèmes/aux 4 coins du monde": "5.2"}], "12": [{"catégories/documentaire tv": "3.3", "catégories/magazine": "2.8", "personnages/animaux": "10.0", "thèmes/nature et vie sauvage": "5.3"}, {"catégories/documentaire tv": "5.2", "personnages/animaux": "10.0", "sujets/environnement": "5.0", "thèmes/nature et vie sauvage": "4.1"}, {"catégories/documentaire tv": "5.4", "personnages/animaux": "8.8", "sujets/environnement": "5.8", "thèmes/nature et vie sauvage": "9.7"}, {"catégories/documentaire tv": "6.0", "personnages/animaux": "8.2", "thèmes/nature et vie sauvage": "9.1"}, {"catégories/documentaire tv": "6.1", "sujets/environnement": "7.5", "thèmes/nature et vie sauvage": "9.2"}, {"catégories/documentaire tv": "2.0", "personnages/animaux": "2.0", "sujets/environnement": "5.0", "thèmes/nature et vie sauvage": "10.0"}, {"catégories/documentaire tv": "6.3", "catégories/magazine": "6.4", "personnages/animaux": "7.5", "thèmes/nature et vie sauvage": "8.6"}, {"catégories/documentaire tv": "7.0", "catégories/magazine": "3.0", "personnages/animaux": "10.0", "thèmes/nature et vie sauvage": "4.0"}, {"catégories/documentaire tv": "6.8", "catégories/magazine": "6.6", "thèmes/nature et vie sauvage": "7.1"}, {"catégories/documentaire tv": "7.0", "personnages/animaux": "2.0", "sujets/environnement": "10.0", "thèmes/nature et vie sauvage": "2.0"}, {"catégories/documentaire tv": "6.1", "catégories/magazine": "5.7", "personnages/animaux": "7.4", "sujets/environnement": "6.0", "thèmes/nature et vie sauvage": "8.6"}, {"catégories/documentaire tv": "7.0", "catégories/magazine": "4.0", "personnages/animaux": "5.0", "sujets/environnement": "10.0", "thèmes/nature et vie sauvage": "5.0"}, {"catégories/documentaire tv": "6.8", "thèmes/nature et vie sauvage": "7.7"}, {"catégories/documentaire tv": "6.6", "catégories/magazine": "6.0", "sujets/environnement": "7.1", "thèmes/nature et vie sauvage": "8.5"}], "14": [{"thèmes/énigmes et jeu de piste": "6.9", "type/jeu télévisé": "7.8"}, {"thèmes/énigmes et jeu de piste": "7.0", "type/jeu télévisé": "7.0", "type/télé-réalité": "6.3"}], "15": [{"catégories/fiction": "9.6", "type/film": "7.9"}, {"catégories/fiction": "9.6", "type/film": "9.2", "type/plébiscité par la critique": "7.7"}, {"catégories/fiction": "8.4", "type/film": "7.9", "type/plébiscité par la critique": "6.4", "type/plébiscité par le public": "6.5"}, {"catégories/fiction": "9.4", "type/film": "8.9", "type/plébiscité par le public": "7.0"}], "16": [{"catégories/documentaire tv": "7.0", "catégories/magazine": "7.3", "sujets/art et culture": "7.0", "sujets/sciences et techniques": "8.2", "thèmes/découverte": "7.2"}, {"catégories/documentaire tv": "4.0", "catégories/magazine": "7.0", "sujets/art et culture": "7.0", "sujets/sciences et techniques": "7.0", "sujets/voyage et évasion": "10.0", "thèmes/découverte": "3.0"}, {"catégories/documentaire tv": "7.0", "catégories/magazine": "5.0", "sujets/sciences et techniques": "5.0", "sujets/voyage et évasion": "5.0", "type/reportage": "7.0"}, {"catégories/magazine": "5.0", "sujets/art et culture": "10.0", "sujets/sciences et techniques": "6.0", "sujets/voyage et évasion": "6.0", "thèmes/découverte": "3.0", "type/reportage": "7.0"}, {"catégories/magazine": "8.6", "sujets/sciences et techniques": "4.9", "sujets/voyage et évasion": "5.0", "thèmes/découverte": "5.0"}, {"catégories/magazine": "8.8", "sujets/art et culture": "3.9", "sujets/voyage et évasion": "4.1", "type/reportage": "7.9"}, {"catégories/magazine": "10.0", "sujets/sciences et techniques": "7.5", "sujets/voyage et évasion": "3.5", "type/reportage": "4.5"}, {"catégories/documentaire tv": "7.3", "catégories/magazine": "6.3", "sujets/art et culture": "8.3", "sujets/sciences et techniques": "10.0", "thèmes/découverte": "8.3", "type/reportage": "8.3"}, {"catégories/magazine": "8.1", "sujets/sciences et techniques": "4.8", "type/reportage": "8.3"}, {"catégories/documentaire tv": "4.0", "catégories/magazine": "4.0", "sujets/art et culture": "2.0", "sujets/voyage et évasion": "10.0", "thèmes/découverte": "5.0"}, {"catégories/documentaire tv": "6.7", "catégories/magazine": "7.7", "sujets/art et culture": "6.3", "thèmes/découverte": "7.3"}, {"catégories/documentaire tv": "7.0", "catégories/magazine": "5.0", "sujets/art et culture": "5.0", "sujets/sciences et techniques": "10.0", "sujets/voyage et évasion": "10.0", "thèmes/découverte": "7.0"}, {"catégories/magazine": "10.0", "sujets/sciences et techniques": "8.0", "thèmes/découverte": "2.0", "type/reportage": "4.0"}, {"catégories/magazine": "7.9", "sujets/art et culture": "5.7", "sujets/sciences et techniques": "5.9", "sujets/voyage et évasion": "5.3"}, {"catégories/magazine": "8.4", "sujets/art et culture": "5.2", "sujets/voyage et évasion": "3.8", "thèmes/découverte": "5.4", "type/reportage": "8.8"}, {"catégories/magazine": "7.0", "sujets/sciences et techniques": "10.0", "sujets/voyage et évasion": "5.0", "thèmes/découverte": "4.0", "type/reportage": "5.0"}, {"catégories/magazine": "10.0", "sujets/art et culture": "6.3", "sujets/sciences et techniques": "6.8", "sujets/voyage et évasion": "7.3", "type/reportage": "7.5"}, {"catégories/magazine": "9.2", "sujets/art et culture": "4.0", "thèmes/découverte": "4.5"}, {"catégories/documentaire tv": "7.0", "catégories/magazine": "6.2", "sujets/sciences et techniques": "7.7"}, {"catégories/documentaire tv": "5.0", "catégories/magazine": "4.0", "sujets/art et culture": "2.0", "sujets/sciences et techniques": "2.0", "sujets/voyage et évasion": "8.0", "thèmes/découverte": "5.0"}, {"catégories/documentaire tv": "4.0", "catégories/magazine": "4.0", "sujets/art et culture": "5.0", "sujets/sciences et techniques": "10.0", "sujets/voyage et évasion": "5.0"}, {"catégories/documentaire tv": "3.0", "catégories/magazine": "10.0", "sujets/art et culture": "7.0", "sujets/voyage et évasion": "4.0", "thèmes/découverte": "2.0"}, {"catégories/documentaire tv": "10.0", "catégories/magazine": "6.0", "sujets/art et culture": "5.0", "sujets/voyage et évasion": "5.0", "thèmes/découverte": "5.0", "type/reportage": "6.0"}, {"catégories/documentaire tv": "10.0", "catégories/magazine": "6.0", "sujets/art et culture": "7.5", "sujets/sciences et techniques": "6.5", "sujets/voyage et évasion": "7.5", "thèmes/découverte": "8.0"}, {"catégories/magazine": "6.0", "sujets/sciences et techniques": "10.0", "thèmes/découverte": "3.0", "type/reportage": "3.0"}, {"catégories/magazine": "8.2", "sujets/sciences et techniques": "4.4", "thèmes/découverte": "5.8", "type/reportage": "8.7"}, {"catégories/documentaire tv": "6.0", "catégories/magazine": "7.5", "sujets/art et culture": "10.0", "sujets/sciences et techniques": "10.0", "sujets/voyage et évasion": "8.5", "thèmes/découverte": "8.3"}, {"catégories/documentaire tv": "6.9", "catégories/magazine": "7.0", "sujets/art et culture": "8.7", "sujets/sciences et techniques": "8.8", "sujets/voyage et évasion": "8.6"}, {"catégories/documentaire tv": "3.0", "catégories/magazine": "3.0", "sujets/sciences et techniques": "5.0", "sujets/voyage et évasion": "5.0", "thèmes/découverte": "5.0", "type/reportage": "5.0"}, {"catégories/documentaire tv": "8.0", "catégories/magazine": "3.0", "sujets/sciences et techniques": "10.0", "sujets/voyage et évasion": "2.0"}, {"catégories/magazine": "6.0", "sujets/art et culture": "4.0", "sujets/sciences et techniques": "5.0", "sujets/voyage et évasion": "5.0", "thèmes/découverte": "10.0", "type/reportage": "8.0"}, {"catégories/documentaire tv": "2.8", "catégories/magazine": "4.0", "sujets/art et culture": "5.3", "sujets/sciences et techniques": "10.0", "thèmes/découverte": "3.8"}, {"catégories/magazine": "8.5", "sujets/art et culture": "5.6", "sujets/sciences et techniques": "5.2", "thèmes/découverte": "4.3"}, {"catégories/magazine": "9.2", "sujets/art et culture": "5.1", "sujets/voyage et évasion": "4.9"}, {"catégories/documentaire tv": "3.0", "catégories/magazine": "3.0", "sujets/art et culture": "4.0", "thèmes/découverte": "10.0", "type/reportage": "4.0"}, {"catégories/magazine": "10.0", "sujets/art et culture": "5.0", "sujets/sciences et techniques": "3.0", "sujets/voyage et évasion": "3.0", "type/reportage": "7.0"}, {"catégories/documentaire tv": "8.0", "catégories/magazine": "6.0", "sujets/art et culture": "10.0", "sujets/sciences et techniques": "5.5", "sujets/voyage et évasion": "5.5"}, {"catégories/documentaire tv": "4.0", "catégories/magazine": "4.0", "sujets/sciences et techniques": "3.0", "sujets/voyage et évasion": "3.0", "thèmes/découverte": "3.0", "type/reportage": "3.0"}, {"catégories/magazine": "7.6", "sujets/sciences et techniques": "4.9", "sujets/voyage et évasion": "4.9"}, {"catégories/documentaire tv": "7.2", "catégories/magazine": "8.1", "sujets/art et culture": "4.8", "sujets/voyage et évasion": "6.9", "thèmes/découverte": "7.0"}, {"catégories/documentaire tv": "4.5", "catégories/magazine": "8.5", "sujets/sciences et techniques": "6.5", "type/reportage": "4.5"}, {"catégories/magazine": "10.0", "sujets/art et culture": "8.0", "sujets/voyage et évasion": "5.0", "type/reportage": "2.0"}, {"catégories/magazine": "7.7", "sujets/sciences et techniques": "5.1", "sujets/voyage et évasion": "5.3", "type/reportage": "8.1"}, {"catégories/documentaire tv": "3.0", "catégories/magazine": "3.0", "sujets/art et culture": "4.0", "sujets/voyage et évasion": "4.0", "thèmes/découverte": "10.0"}, {"catégories/magazine": "4.0", "sujets/sciences et techniques": "5.0", "sujets/voyage et évasion": "10.0", "thèmes/découverte": "4.0", "type/reportage": "5.0"}, {"catégories/magazine": "7.9", "sujets/sciences et techniques": "6.3"}, {"catégories/documentaire tv": "6.7", "catégories/magazine": "6.2", "sujets/sciences et techniques": "7.5", "thèmes/découverte": "7.4"}, {"catégories/documentaire tv": "4.0", "catégories/magazine": "9.0", "sujets/art et culture": "6.0", "sujets/sciences et techniques": "6.0", "sujets/voyage et évasion": "10.0"}, {"catégories/magazine": "8.9", "sujets/art et culture": "3.8", "sujets/voyage et évasion": "4.7", "thèmes/découverte": "4.9"}, {"catégories/documentaire tv": "3.0", "catégories/magazine": "4.0", "sujets/sciences et techniques": "4.0", "sujets/voyage et évasion": "5.0", "thèmes/découverte": "10.0"}, {"catégories/magazine": "7.0", "sujets/art et culture": "10.0", "sujets/sciences et techniques": "10.0", "sujets/voyage et évasion": "4.0", "thèmes/découverte": "4.0"}, {"catégories/magazine": "10.0", "sujets/art et culture": "4.0", "sujets/sciences et techniques": "3.0", "sujets/voyage et évasion": "3.0", "thèmes/découverte": "4.0", "type/reportage": "9.0"}, {"catégories/documentaire tv": "7.3", "catégories/magazine": "4.8", "sujets/art et culture": "5.0", "sujets/sciences et techniques": "10.0", "sujets/voyage et évasion": "5.0"}, {"catégories/magazine": "10.0", "sujets/art et culture": "6.5", "sujets/sciences et techniques": "3.5", "sujets/voyage et évasion": "3.5", "thèmes/découverte": "6.5", "type/reportage": "7.0"}, {"catégories/documentaire tv": "3.0", "catégories/magazine": "8.0", "sujets/art et culture": "5.0", "sujets/sciences et techniques": "10.0", "thèmes/découverte": "4.0"}, {"catégories/documentaire tv": "6.0", "catégories/magazine": "5.0", "sujets/sciences et techniques": "5.0", "thèmes/découverte": "10.0", "type/reportage": "6.0"}, {"catégories/magazine": "6.5", "sujets/sciences et techniques": "2.5", "sujets/voyage et évasion": "2.5", "thèmes/découverte": "10.0", "type/reportage": "6.5"}, {"catégories/magazine": "7.0", "sujets/sciences et techniques": "10.0", "sujets/voyage et évasion": "2.0", "thèmes/découverte": "2.0"}, {"catégories/magazine": "3.5", "sujets/sciences et techniques": "9.0", "sujets/voyage et évasion": "10.0", "thèmes/découverte": "5.0"}, {"catégories/documentaire tv": "2.0", "catégories/magazine": "4.0", "sujets/sciences et techniques": "10.0", "sujets/voyage et évasion": "10.0", "thèmes/découverte": "3.0"}, {"catégories/documentaire tv": "6.3", "catégories/magazine": "7.0", "sujets/sciences et techniques": "6.5", "thèmes/découverte": "4.3", "type/reportage": "6.7"}, {"catégories/magazine": "7.0", "sujets/art et culture": "3.0", "sujets/sciences et techniques": "3.0", "sujets/voyage et évasion": "6.0", "thèmes/découverte": "10.0"}, {"catégories/magazine": "4.0", "sujets/art et culture": "5.0", "sujets/voyage et évasion": "9.3", "thèmes/découverte": "4.7"}, {"catégories/magazine": "8.0", "sujets/art et culture": "2.0", "sujets/voyage et évasion": "10.0", "type/reportage": "4.0"}, {"catégories/magazine": "8.0", "sujets/art et culture": "5.8", "sujets/sciences et techniques": "4.2", "thèmes/découverte": "6.1", "type/reportage": "9.2"}, {"catégories/documentaire tv": "10.0", "catégories/magazine": "4.0", "sujets/art et culture": "4.0", "sujets/voyage et évasion": "8.0", "thèmes/découverte": "3.0"}, {"catégories/magazine": "2.5", "sujets/art et culture": "9.5", "type/reportage": "4.5"}, {"catégories/documentaire tv": "6.5", "catégories/magazine": "6.8", "sujets/sciences et techniques": "8.3", "type/reportage": "8.4"}, {"catégories/documentaire tv": "4.0", "catégories/magazine": "9.0", "sujets/art et culture": "10.0", "sujets/sciences et techniques": "6.0", "sujets/voyage et évasion": "6.0", "thèmes/découverte": "3.0"}, {"catégories/documentaire tv": "3.6", "catégories/magazine": "5.0", "sujets/art et culture": "3.8", "sujets/sciences et techniques": "4.6", "thèmes/découverte": "9.2"}, {"catégories/magazine": "5.3", "sujets/art et culture": "3.5", "sujets/sciences et techniques": "10.0", "thèmes/découverte": "10.0"}, {"catégories/documentaire tv": "6.6", "catégories/magazine": "6.6", "sujets/sciences et techniques": "7.3", "sujets/voyage et évasion": "7.3"}, {"catégories/documentaire tv": "4.0", "catégories/magazine": "3.0", "sujets/art et culture": "5.0", "sujets/sciences et techniques": "5.0", "sujets/voyage et évasion": "10.0"}, {"catégories/documentaire tv": "6.3", "catégories/magazine": "7.9", "sujets/art et culture": "5.9", "sujets/voyage et évasion": "6.6"}, {"catégories/documentaire tv": "6.0", "catégories/magazine": "9.0", "sujets/art et culture": "10.0", "thèmes/découverte": "4.0", "type/reportage": "6.0"}, {"catégories/magazine": "6.0", "sujets/art et culture": "10.0", "sujets/sciences et techniques": "6.0", "sujets/voyage et évasion": "6.0", "type/reportage": "6.0"}, {"catégories/magazine": "10.0", "sujets/sciences et techniques": "5.0", "sujets/voyage et évasion": "4.0", "thèmes/découverte": "2.0", "type/reportage": "6.0"}, {"catégories/documentaire tv": "3.5", "catégories/magazine": "3.0", "sujets/art et culture": "4.5", "sujets/sciences et techniques": "4.5", "thèmes/découverte": "10.0", "type/reportage": "4.5"}, {"catégories/documentaire tv": "10.0", "catégories/magazine": "9.0", "sujets/art et culture": "3.0", "sujets/sciences et techniques": "6.0", "sujets/voyage et évasion": "3.0", "thèmes/découverte": "6.0"}, {"catégories/documentaire tv": "6.4", "catégories/magazine": "7.1", "sujets/art et culture": "7.5", "sujets/sciences et techniques": "7.9"}, {"catégories/magazine": "8.6", "sujets/art et culture": "6.9", "sujets/sciences et techniques": "4.4", "type/reportage": "9.3"}, {"catégories/magazine": "8.3", "sujets/art et culture": "5.5", "sujets/sciences et techniques": "5.5", "sujets/voyage et évasion": "10.0", "thèmes/découverte": "9.5"}, {"catégories/documentaire tv": "5.0", "catégories/magazine": "10.0", "sujets/sciences et techniques": "6.0", "thèmes/découverte": "5.0", "type/reportage": "10.0"}, {"catégories/documentaire tv": "3.8", "catégories/magazine": "7.0", "sujets/art et culture": "10.0", "sujets/sciences et techniques": "5.4", "thèmes/découverte": "4.6"}, {"catégories/documentaire tv": "5.7", "catégories/magazine": "5.8", "sujets/sciences et techniques": "7.3", "sujets/voyage et évasion": "7.3", "type/reportage": "7.5"}, {"catégories/magazine": "10.0", "sujets/art et culture": "4.0", "sujets/sciences et techniques": "7.0", "sujets/voyage et évasion": "4.0", "thèmes/découverte": "3.0"}, {"catégories/magazine": "8.2", "sujets/art et culture": "4.4", "type/reportage": "7.9"}, {"catégories/documentaire tv": "6.5", "catégories/magazine": "7.4", "sujets/art et culture": "6.6"}, {"catégories/documentaire tv": "3.0", "catégories/magazine": "10.0", "sujets/art et culture": "3.0", "sujets/voyage et évasion": "3.0", "thèmes/découverte": "6.0"}, {"catégories/documentaire tv": "6.0", "catégories/magazine": "10.0", "sujets/art et culture": "6.0", "thèmes/découverte": "6.0", "type/reportage": "6.0"}, {"catégories/documentaire tv": "3.0", "catégories/magazine": "3.0", "sujets/art et culture": "4.0", "sujets/sciences et techniques": "4.0", "sujets/voyage et évasion": "5.0", "thèmes/découverte": "10.0"}, {"catégories/documentaire tv": "4.0", "catégories/magazine": "7.0", "sujets/art et culture": "2.0", "sujets/sciences et techniques": "2.0", "thèmes/découverte": "7.0"}, {"catégories/documentaire tv": "9.0", "catégories/magazine": "9.0", "sujets/art et culture": "5.0", "sujets/sciences et techniques": "5.0", "type/reportage": "7.0"}, {"catégories/magazine": "8.3", "sujets/art et culture": "7.0"}, {"catégories/documentaire tv": "5.3", "catégories/magazine": "9.0", "sujets/art et culture": "5.1", "thèmes/découverte": "5.8", "type/reportage": "9.7"}, {"catégories/documentaire tv": "4.5", "catégories/magazine": "6.5", "sujets/art et culture": "3.0", "sujets/sciences et techniques": "3.0", "sujets/voyage et évasion": "8.0", "thèmes/découverte": "8.5"}, {"catégories/magazine": "5.0", "sujets/sciences et techniques": "9.0", "sujets/voyage et évasion": "10.0", "thèmes/découverte": "3.0", "type/reportage": "6.0"}, {"catégories/documentaire tv": "7.0", "catégories/magazine": "7.0", "sujets/art et culture": "5.0", "sujets/sciences et techniques": "10.0", "type/reportage": "10.0"}, {"catégories/documentaire tv": "10.0", "catégories/magazine": "7.0", "sujets/art et culture": "7.0", "type/reportage": "10.0"}, {"catégories/documentaire tv": "6.3", "catégories/magazine": "7.3", "sujets/sciences et techniques": "5.6", "sujets/voyage et évasion": "7.7", "thèmes/découverte": "7.3"}, {"catégories/magazine": "5.0", "sujets/art et culture": "2.0", "sujets/voyage et évasion": "10.0", "type/reportage": "3.0"}, {"catégories/magazine": "8.5", "sujets/art et culture": "4.5", "sujets/sciences et techniques": "3.0", "sujets/voyage et évasion": "3.0", "thèmes/découverte": "7.5", "type/reportage": "10.0"}, {"catégories/magazine": "7.5", "sujets/sciences et techniques": "2.9", "sujets/voyage et évasion": "3.0", "thèmes/découverte": "5.0", "type/reportage": "8.4"}, {"catégories/documentaire tv": "9.5", "catégories/magazine": "8.5", "sujets/art et culture": "7.0", "sujets/sciences et techniques": "7.0", "sujets/voyage et évasion": "7.5", "thèmes/découverte": "3.5"}, {"catégories/documentaire tv": "7.0", "catégories/magazine": "10.0", "sujets/art et culture": "3.0", "sujets/sciences et techniques": "3.0", "sujets/voyage et évasion": "3.0", "thèmes/découverte": "3.0"}, {"catégories/magazine": "6.0", "sujets/art et culture": "8.6", "sujets/sciences et techniques": "7.5"}, {"catégories/magazine": "3.0", "sujets/sciences et techniques": "3.0", "sujets/voyage et évasion": "6.0", "thèmes/découverte": "10.0"}, {"catégories/magazine": "6.7", "sujets/art et culture": "4.8", "thèmes/découverte": "4.8", "type/reportage": "9.8"}, {"catégories/magazine": "7.5", "sujets/sciences et techniques": "5.6", "thèmes/découverte": "5.3"}, {"catégories/magazine": "4.0", "sujets/art et culture": "4.0", "sujets/sciences et techniques": "5.0", "sujets/voyage et évasion": "5.0", "thèmes/découverte": "4.0", "type/reportage": "5.0"}, {"catégories/documentaire tv": "8.0", "catégories/magazine": "3.0", "sujets/art et culture": "8.0", "sujets/sciences et techniques": "10.0", "thèmes/découverte": "4.0"}, {"catégories/documentaire tv": "10.0", "catégories/magazine": "7.0", "sujets/sciences et techniques": "6.0", "sujets/voyage et évasion": "6.0", "thèmes/découverte": "7.0", "type/reportage": "10.0"}, {"catégories/documentaire tv": "7.0", "catégories/magazine": "7.0", "sujets/art et culture": "10.0", "sujets/voyage et évasion": "5.0", "type/reportage": "9.7"}, {"catégories/magazine": "10.0", "sujets/art et culture": "3.0", "sujets/sciences et techniques": "3.0", "sujets/voyage et évasion": "3.0", "thèmes/découverte": "3.0"}, {"catégories/magazine": "9.3", "sujets/art et culture": "5.8", "sujets/sciences et techniques": "5.8", "sujets/voyage et évasion": "5.8", "thèmes/découverte": "5.8"}, {"catégories/magazine": "10.0", "sujets/art et culture": "3.0", "sujets/sciences et techniques": "3.0", "sujets/voyage et évasion": "3.0", "thèmes/découverte": "7.0"}, {"catégories/magazine": "7.0", "sujets/art et culture": "4.0", "sujets/sciences et techniques": "3.0", "sujets/voyage et évasion": "3.0", "thèmes/découverte": "4.0", "type/reportage": "10.0"}, {"catégories/documentaire tv": "3.5", "catégories/magazine": "7.0", "sujets/sciences et techniques": "5.5", "sujets/voyage et évasion": "5.5", "thèmes/découverte": "10.0"}], "18": [{"catégories/émissions": "6.0", "envies/se cultiver": "4.0", "sujets/art et culture": "4.0", "sujets/cinéma": "10.0"}, {"catégories/magazine": "7.0", "envies/se cultiver": "4.0", "sujets/art et culture": "5.0", "sujets/beaux-arts": "5.0"}, {"catégories/magazine": "6.0", "catégories/émissions": "6.0", "envies/se cultiver": "8.0", "sujets/art et culture": "4.0", "sujets/cinéma": "10.0"}, {"catégories/émissions": "4.9", "envies/se cultiver": "8.9", "sujets/art et culture": "8.6"}, {"envies/se cultiver": "10.0", "sujets/art et culture": "2.0", "sujets/beaux-arts": "7.0"}, {"catégories/magazine": "10.0", "catégories/émissions": "4.0", "envies/se cultiver": "5.5", "sujets/art et culture": "8.5", "sujets/littérature": "8.5"}, {"catégories/magazine": "7.7", "envies/se cultiver": "8.1", "sujets/art et culture": "3.9", "sujets/cinéma": "4.0"}, {"catégories/émissions": "7.2", "envies/se cultiver": "9.4", "sujets/cinéma": "5.9"}, {"catégories/magazine": "8.0", "catégories/émissions": "3.3", "envies/se cultiver": "3.0", "sujets/art et culture": "3.0", "sujets/cinéma": "10.0"}, {"catégories/magazine": "8.7", "catégories/émissions": "8.7", "envies/se cultiver": "5.0", "sujets/cinéma": "3.5", "sujets/littérature": "3.5"}, {"catégories/magazine": "8.3", "envies/se cultiver": "4.1", "sujets/cinéma": "2.7", "sujets/littérature": "2.7"}, {"catégories/magazine": "7.6", "catégories/émissions": "7.6", "envies/se cultiver": "2.4", "sujets/art et culture": "3.1", "sujets/cinéma": "9.9"}, {"catégories/émissions": "5.0", "envies/se cultiver": "3.0", "sujets/art et culture": "2.0", "sujets/beaux-arts": "10.0"}, {"catégories/magazine": "9.3", "catégories/émissions": "9.3", "envies/se cultiver": "6.3", "sujets/art et culture": "3.0", "sujets/cinéma": "8.3"}, {"catégories/émissions": "7.2", "envies/se cultiver": "9.7", "sujets/art et culture": "4.9", "sujets/cinéma": "4.9"}, {"envies/se cultiver": "6.0", "sujets/art et culture": "3.0", "sujets/cinéma": "5.0", "sujets/littérature": "5.0"}, {"catégories/émissions": "4.9", "envies/se cultiver": "10.0", "sujets/art et culture": "4.7", "sujets/beaux-arts": "5.0"}, {"catégories/émissions": "3.9", "envies/se cultiver": "10.0", "sujets/art et culture": "2.0", "sujets/littérature": "2.0"}, {"catégories/magazine": "7.5", "catégories/émissions": "7.4", "envies/se cultiver": "9.5", "sujets/cinéma": "2.3", "sujets/littérature": "2.2"}, {"catégories/magazine": "5.0", "catégories/émissions": "4.0", "envies/se cultiver": "10.0", "sujets/art et culture": "4.0", "sujets/cinéma": "8.0", "sujets/littérature": "8.0"}, {"catégories/magazine": "9.3", "envies/se cultiver": "5.8", "sujets/art et culture": "3.0", "sujets/beaux-arts": "3.0"}, {"catégories/émissions": "6.0", "envies/se cultiver": "7.0", "sujets/beaux-arts": "5.0", "sujets/cinéma": "5.0"}, {"catégories/émissions": "4.0", "envies/se cultiver": "10.0", "sujets/beaux-arts": "7.0", "sujets/cinéma": "7.0"}, {"catégories/magazine": "10.0", "catégories/émissions": "10.0", "envies/se cultiver": "8.0", "sujets/art et culture": "2.0", "sujets/cinéma": "4.0", "sujets/littérature": "4.0"}, {"catégories/magazine": "8.0", "catégories/émissions": "3.0", "envies/se cultiver": "2.0", "sujets/art et culture": "2.0", "sujets/littérature": "10.0"}, {"catégories/magazine": "7.0", "catégories/émissions": "3.0", "envies/se cultiver": "10.0", "sujets/art et culture": "2.0", "sujets/beaux-arts": "2.0"}, {"envies/se cultiver": "9.3", "sujets/beaux-arts": "3.7", "sujets/cinéma": "3.7", "sujets/littérature": "3.7"}, {"catégories/magazine": "6.4", "catégories/émissions": "6.0", "envies/se cultiver": "9.4", "sujets/art et culture": "4.0", "sujets/cinéma": "4.3"}, {"catégories/magazine": "6.6", "envies/se cultiver": "6.8", "sujets/art et culture": "6.9"}, {"catégories/magazine": "7.0", "envies/se cultiver": "10.0", "sujets/beaux-arts": "4.0", "sujets/cinéma": "4.0"}, {"catégories/magazine": "8.1", "envies/se cultiver": "7.6", "sujets/cinéma": "4.6"}, {"catégories/émissions": "3.0", "envies/se cultiver": "6.0", "sujets/cinéma": "8.0", "sujets/littérature": "5.0"}, {"catégories/magazine": "10.0", "envies/se cultiver": "6.7", "sujets/beaux-arts": "5.3", "sujets/cinéma": "6.0"}, {"envies/se cultiver": "7.0", "sujets/art et culture": "7.0"}, {"catégories/magazine": "9.0", "envies/se cultiver": "3.0", "sujets/beaux-arts": "3.0", "sujets/cinéma": "3.0"}, {"envies/se cultiver": "3.5", "sujets/art et culture": "3.3", "sujets/cinéma": "9.0"}, {"catégories/magazine": "10.0", "catégories/émissions": "10.0", "envies/se cultiver": "10.0", "sujets/art et culture": "4.0", "sujets/cinéma": "7.0", "sujets/littérature": "7.0"}, {"envies/se cultiver": "8.0", "sujets/art et culture": "4.2", "sujets/cinéma": "4.2"}, {"catégories/magazine": "7.0", "catégories/émissions": "7.0", "envies/se cultiver": "10.0", "sujets/art et culture": "9.0", "sujets/littérature": "9.0"}, {"catégories/magazine": "4.6", "catégories/émissions": "4.6", "envies/se cultiver": "9.2", "sujets/cinéma": "7.4", "sujets/littérature": "7.4"}, {"catégories/émissions": "4.1", "envies/se cultiver": "10.0", "sujets/art et culture": "2.0", "sujets/beaux-arts": "2.0"}, {"catégories/magazine": "10.0", "catégories/émissions": "9.0", "envies/se cultiver": "2.0", "sujets/beaux-arts": "9.0", "sujets/cinéma": "5.0"}, {"envies/se cultiver": "7.3", "sujets/beaux-arts": "4.5", "sujets/cinéma": "4.7"}, {"catégories/magazine": "6.8", "envies/se cultiver": "8.7", "sujets/art et culture": "3.8", "sujets/littérature": "3.8"}, {"catégories/magazine": "4.8", "catégories/émissions": "4.0", "envies/se cultiver": "10.0", "sujets/cinéma": "3.5", "sujets/littérature": "3.5"}, {"catégories/magazine": "5.2", "catégories/émissions": "5.2", "envies/se cultiver": "9.8", "sujets/art et culture": "4.9", "sujets/littérature": "4.9"}, {"catégories/magazine": "10.0", "catégories/émissions": "10.0", "envies/se cultiver": "9.0", "sujets/beaux-arts": "7.0", "sujets/cinéma": "7.0"}, {"catégories/magazine": "10.0", "catégories/émissions": "4.0", "envies/se cultiver": "3.0", "sujets/beaux-arts": "2.0", "sujets/cinéma": "2.0"}, {"catégories/magazine": "7.0", "catégories/émissions": "7.0", "envies/se cultiver": "10.0", "sujets/beaux-arts": "4.0", "sujets/cinéma": "4.0", "sujets/littérature": "4.0"}, {"envies/se cultiver": "8.5", "sujets/cinéma": "5.4"}, {"catégories/émissions": "4.9", "envies/se cultiver": "9.8", "sujets/art et culture": "4.7", "sujets/littérature": "4.9"}, {"envies/se cultiver": "9.3", "sujets/cinéma": "6.7", "sujets/littérature": "6.6"}, {"catégories/magazine": "8.0", "catégories/émissions": "8.0", "envies/se cultiver": "3.0", "sujets/art et culture": "4.0", "sujets/beaux-arts": "10.0"}, {"catégories/magazine": "7.0", "catégories/émissions": "7.0", "envies/se cultiver": "6.0", "sujets/art et culture": "2.0", "sujets/cinéma": "10.0"}, {"catégories/magazine": "2.6", "catégories/émissions": "2.6", "envies/se cultiver": "10.0", "sujets/art et culture": "2.6", "sujets/beaux-arts": "2.6"}, {"catégories/magazine": "5.0", "catégories/émissions": "5.0", "envies/se cultiver": "10.0", "sujets/beaux-arts": "8.0", "sujets/cinéma": "8.0"}, {"catégories/émissions": "10.0", "envies/se cultiver": "5.5", "sujets/cinéma": "3.5", "sujets/littérature": "3.5"}, {"catégories/magazine": "9.5", "catégories/émissions": "8.9", "envies/se cultiver": "8.1", "sujets/art et culture": "3.7", "sujets/beaux-arts": "3.7"}, {"envies/se cultiver": "5.0", "sujets/art et culture": "3.0", "sujets/cinéma": "7.0"}, {"envies/se cultiver": "6.0", "sujets/cinéma": "10.0", "sujets/littérature": "5.0"}, {"catégories/magazine": "10.0", "catégories/émissions": "3.6", "envies/se cultiver": "6.1", "sujets/cinéma": "3.0", "sujets/littérature": "3.3"}, {"catégories/magazine": "7.0", "envies/se cultiver": "9.8", "sujets/beaux-arts": "7.3", "sujets/cinéma": "7.3"}, {"envies/se cultiver": "6.8", "sujets/art et culture": "3.0", "sujets/beaux-arts": "3.1"}, {"envies/se cultiver": "8.9", "sujets/art et culture": "4.4", "sujets/littérature": "4.4"}, {"catégories/magazine": "7.6", "catégories/émissions": "6.5", "envies/se cultiver": "8.8", "sujets/cinéma": "3.8"}, {"catégories/magazine": "5.4", "catégories/émissions": "5.1", "envies/se cultiver": "7.5", "sujets/art et culture": "7.5"}, {"catégories/magazine": "6.9", "envies/se cultiver": "9.8", "sujets/art et culture": "3.6", "sujets/beaux-arts": "3.6"}, {"catégories/magazine": "4.0", "envies/se cultiver": "10.0", "sujets/beaux-arts": "2.0", "sujets/cinéma": "2.0"}, {"catégories/magazine": "8.0", "catégories/émissions": "3.0", "envies/se cultiver": "10.0", "sujets/cinéma": "3.0", "sujets/littérature": "2.0"}, {"catégories/magazine": "10.0", "catégories/émissions": "9.0", "envies/se cultiver": "3.0", "sujets/cinéma": "9.0", "sujets/littérature": "5.0"}, {"catégories/émissions": "5.0", "envies/se cultiver": "9.9", "sujets/cinéma": "7.7", "sujets/littérature": "7.7"}], "20": [{"thèmes/vengeance et auto-justice": "8.4"}], "21": [{"ambiance et ton/sentimental": "9.8", "catégories/fiction": "9.9", "envies/soirées entre filles": "9.8", "genres/comédie romantique": "4.9", "sujets/relation de couple": "6.0", "thèmes/amour et séduction": "9.8"}, {"ambiance et ton/sentimental": "6.0", "envies/soirées entre filles": "6.0", "genres/comédie romantique": "2.7", "sujets/relation de couple": "2.7"}, {"catégories/fiction": "9.7", "genres/comédie romantique": "2.9", "thèmes/amour et séduction": "8.0"}, {"ambiance et ton/sentimental": "10.0", "catégories/fiction": "10.0", "genres/comédie romantique": "2.5", "sujets/relation de couple": "2.5", "thèmes/amour et séduction": "9.0"}, {"ambiance et ton/sentimental": "5.3", "envies/soirées entre filles": "5.3", "genres/comédie romantique": "2.6"}, {"catégories/fiction": "9.7", "envies/soirées entre filles": "9.7", "genres/comédie romantique": "4.8", "thèmes/amour et séduction": "9.7"}, {"catégories/fiction": "6.0", "envies/soirées entre filles": "6.0", "genres/comédie romantique": "3.0", "thèmes/amour et séduction": "10.0"}, {"ambiance et ton/sentimental": "10.0", "catégories/fiction": "10.0", "envies/soirées entre filles": "10.0", "genres/comédie romantique": "5.0", "sujets/relation de couple": "5.0"}, {"ambiance et ton/sentimental": "8.1", "catégories/fiction": "9.5", "genres/comédie romantique": "2.6"}, {"ambiance et ton/sentimental": "6.0", "envies/soirées entre filles": "6.0", "genres/comédie romantique": "2.8", "sujets/relation de couple": "6.0", "thèmes/amour et séduction": "6.0"}, {"ambiance et ton/sentimental": "9.9", "catégories/fiction": "9.9", "envies/soirées entre filles": "9.9", "genres/comédie romantique": "4.9"}, {"ambiance et ton/sentimental": "6.6", "catégories/fiction": "9.8", "genres/comédie romantique": "2.2", "thèmes/amour et séduction": "6.5"}, {"catégories/fiction": "10.0", "envies/soirées entre filles": "4.0", "genres/comédie romantique": "2.0", "sujets/relation de couple": "2.0", "thèmes/amour et séduction": "4.0"}, {"ambiance et ton/sentimental": "5.4", "envies/soirées entre filles": "5.4", "genres/comédie romantique": "2.5", "thèmes/amour et séduction": "5.4"}, {"ambiance et ton/sentimental": "9.3", "catégories/fiction": "9.8", "envies/soirées entre filles": "9.3", "genres/comédie romantique": "4.6", "thèmes/amour et séduction": "9.3"}, {"ambiance et ton/sentimental": "5.0", "envies/soirées entre filles": "5.0", "genres/comédie romantique": "2.0", "sujets/relation de couple": "2.0", "thèmes/amour et séduction": "5.0"}, {"catégories/fiction": "10.0", "envies/soirées entre filles": "10.0", "genres/comédie romantique": "5.0", "sujets/relation de couple": "5.0", "thèmes/amour et séduction": "10.0"}, {"envies/soirées entre filles": "5.6", "genres/comédie romantique": "2.6", "thèmes/amour et séduction": "5.6"}, {"ambiance et ton/sentimental": "5.0", "catégories/fiction": "10.0", "envies/soirées entre filles": "5.0", "genres/comédie romantique": "2.0", "sujets/relation de couple": "2.5"}], "22": [{"catégories/divertissement": "8.8", "type/jeu télévisé": "4.4", "type/télé-réalité": "4.4"}, {"catégories/divertissement": "10.0", "type/jeu télévisé": "10.0", "type/télé-réalité": "4.5"}, {"catégories/divertissement": "5.0", "type/jeu télévisé": "5.0", "type/télé-réalité": "5.0"}, {"catégories/divertissement": "8.5", "sujets/jeux vidéo": "5.4", "type/jeu télévisé": "9.4"}, {"catégories/divertissement": "8.4", "sujets/jeux vidéo": "4.9"}], "23": [{"actions/combat": "4.6", "genres/action": "4.6", "thèmes/seul contre tous": "4.8"}, {"actions/combat": "10.0", "catégories/fiction": "5.0", "envies/montée d'adrénaline": "10.0", "genres/action": "10.0", "thèmes/seul contre tous": "5.0"}, {"actions/combat": "4.8", "envies/montée d'adrénaline": "4.8", "genres/action": "4.7"}, {"catégories/fiction": "3.0", "envies/montée d'adrénaline": "10.0", "genres/action": "3.0", "thèmes/seul contre tous": "3.0"}, {"catégories/fiction": "6.0", "genres/action": "10.0", "thèmes/seul contre tous": "6.0"}, {"actions/combat": "7.8", "catégories/fiction": "9.5", "genres/action": "7.8", "thèmes/seul contre tous": "7.7"}, {"actions/combat": "5.0", "catégories/fiction": "10.0", "envies/montée d'adrénaline": "5.0", "genres/action": "10.0", "thèmes/seul contre tous": "10.0"}, {"actions/combat": "6.0", "catégories/fiction": "6.0", "envies/montée d'adrénaline": "10.0", "genres/action": "6.0", "sujets/arts martiaux": "5.0"}, {"catégories/fiction": "9.7", "envies/montée d'adrénaline": "9.0", "genres/action": "9.1", "thèmes/seul contre tous": "8.9"}, {"actions/combat": "9.8", "catégories/fiction": "9.8", "genres/action": "9.8", "sujets/arts martiaux": "5.0", "thèmes/seul contre tous": "9.6"}, {"envies/montée d'adrénaline": "8.0", "genres/action": "8.0", "thèmes/seul contre tous": "4.0"}, {"actions/combat": "8.0", "catégories/fiction": "9.4", "envies/montée d'adrénaline": "8.0", "genres/action": "8.1"}, {"catégories/fiction": "9.8", "genres/action": "9.4", "sujets/arts martiaux": "4.7", "thèmes/seul contre tous": "9.4"}, {"genres/action": "4.6", "thèmes/seul contre tous": "4.6"}, {"actions/combat": "5.2", "envies/montée d'adrénaline": "5.2", "genres/action": "5.2", "sujets/arts martiaux": "2.4"}, {"actions/combat": "10.0", "catégories/fiction": "10.0", "envies/montée d'adrénaline": "5.0", "genres/action": "5.0", "thèmes/seul contre tous": "10.0"}, {"envies/montée d'adrénaline": "4.4", "genres/action": "4.4"}, {"catégories/fiction": "9.5", "envies/montée d'adrénaline": "7.8", "genres/action": "8.3"}, {"actions/combat": "8.7", "catégories/fiction": "9.4", "envies/montée d'adrénaline": "8.7", "genres/action": "8.7", "thèmes/seul contre tous": "8.6"}, {"catégories/fiction": "9.2", "genres/action": "8.5", "thèmes/seul contre tous": "8.0"}, {"actions/combat": "6.0", "catégories/fiction": "6.0", "genres/action": "10.0", "thèmes/seul contre tous": "3.0"}, {"actions/combat": "9.5", "envies/montée d'adrénaline": "5.0", "genres/action": "5.0", "thèmes/seul contre tous": "5.0"}, {"actions/combat": "4.4", "envies/montée d'adrénaline": "4.5", "genres/action": "4.4", "thèmes/seul contre tous": "4.5"}, {"envies/montée d'adrénaline": "4.7", "genres/action": "4.8", "thèmes/seul contre tous": "6.9"}, {"actions/combat": "5.0", "catégories/fiction": "10.0", "envies/montée d'adrénaline": "5.0", "genres/action": "10.0", "sujets/arts martiaux": "5.0"}, {"catégories/fiction": "9.9", "envies/montée d'adrénaline": "9.8", "genres/action": "9.8", "sujets/arts martiaux": "4.9", "thèmes/seul contre tous": "9.8"}, {"actions/combat": "9.7", "catégories/fiction": "9.9", "envies/montée d'adrénaline": "9.7", "genres/action": "9.7", "sujets/arts martiaux": "4.8"}, {"actions/combat": "5.0", "envies/montée d'adrénaline": "10.0", "genres/action": "5.0", "sujets/arts martiaux": "4.0"}, {"catégories/fiction": "10.0", "envies/montée d'adrénaline": "10.0", "genres/action": "10.0", "sujets/arts martiaux": "9.0"}], "24": [{"sujets/histoire": "9.1"}, {"genres/historique": "9.1", "sujets/histoire": "7.8"}], "25": [{"catégories/divertissement": "8.0", "personnages/idiot": "5.0", "personnages/jolie fille": "5.0", "type/télé-réalité": "10.0"}, {"catégories/divertissement": "8.0", "personnages/idiot": "5.0", "personnages/jolie fille": "4.0", "type/télé-réalité": "6.0"}, {"catégories/divertissement": "6.5", "personnages/jolie fille": "5.9", "type/télé-réalité": "9.0"}, {"catégories/divertissement": "3.0", "personnages/jolie fille": "10.0", "type/télé-réalité": "6.0"}, {"catégories/divertissement": "6.3", "type/télé-réalité": "9.1"}, {"catégories/divertissement": "9.1", "personnages/idiot": "6.3", "type/télé-réalité": "6.5"}], "26": [{"sous-genres/science-fiction": "10.0", "temps/anticipation": "5.0"}, {"temps/anticipation": "5.0"}], "27": [{"ambiance et ton/triste": "6.6", "envies/à vos mouchoirs": "6.6", "genres/drame": "8.0", "thèmes/mal de vivre": "6.6"}, {"ambiance et ton/émouvant": "10.0", "envies/à vos mouchoirs": "5.0", "genres/drame": "10.0", "thèmes/mal de vivre": "5.0"}, {"ambiance et ton/triste": "3.3", "ambiance et ton/émouvant": "3.3", "genres/drame": "9.6", "thèmes/mal de vivre": "5.4"}, {"genres/drame": "2.0", "genres/mélodrame": "2.0", "thèmes/mal de vivre": "4.0"}, {"genres/drame": "8.5", "thèmes/mal de vivre": "7.7"}, {"ambiance et ton/émouvant": "3.8", "envies/à vos mouchoirs": "3.7", "genres/drame": "8.8", "thèmes/mal de vivre": "4.5"}, {"ambiance et ton/émouvant": "8.0", "envies/à vos mouchoirs": "8.0", "genres/drame": "8.0", "thèmes/mal de vivre": "7.9"}, {"ambiance et ton/triste": "5.0", "ambiance et ton/émouvant": "5.0", "envies/à vos mouchoirs": "10.0", "genres/drame": "10.0", "thèmes/mal de vivre": "5.0"}, {"ambiance et ton/triste": "5.0", "ambiance et ton/émouvant": "5.0", "envies/à vos mouchoirs": "5.0", "genres/drame": "10.0", "thèmes/mal de vivre": "10.0"}, {"ambiance et ton/triste": "8.5", "envies/à vos mouchoirs": "4.5", "genres/drame": "8.5", "thèmes/mal de vivre": "4.5"}, {"ambiance et ton/triste": "3.9", "genres/drame": "9.9", "thèmes/mal de vivre": "5.0"}, {"ambiance et ton/triste": "3.0", "ambiance et ton/émouvant": "6.0", "envies/à vos mouchoirs": "3.0", "genres/drame": "10.0", "thèmes/mal de vivre": "3.0"}, {"ambiance et ton/triste": "5.0", "envies/à vos mouchoirs": "8.0", "genres/drame": "4.0", "thèmes/mal de vivre": "5.0"}, {"ambiance et ton/triste": "8.3", "ambiance et ton/émouvant": "4.0", "envies/à vos mouchoirs": "8.3", "genres/drame": "8.3", "thèmes/mal de vivre": "4.0"}, {"ambiance et ton/émouvant": "6.7", "genres/drame": "8.2", "thèmes/mal de vivre": "6.9"}, {"envies/à vos mouchoirs": "5.1", "genres/drame": "9.4", "thèmes/mal de vivre": "6.0"}, {"ambiance et ton/triste": "7.5", "ambiance et ton/émouvant": "7.5", "envies/à vos mouchoirs": "7.5", "genres/drame": "8.1", "thèmes/mal de vivre": "7.5"}, {"ambiance et ton/triste": "10.0", "envies/à vos mouchoirs": "10.0", "genres/drame": "4.0", "thèmes/mal de vivre": "5.0"}, {"genres/drame": "5.0", "genres/mélodrame": "5.0"}], "28": [{"catégories/magazine": "3.0", "catégories/programme d'information": "5.0", "envies/se cultiver": "10.0", "sujets/actualité": "2.0", "sujets/politique": "5.0", "type/débat": "5.0", "type/interview": "5.0"}, {"catégories/magazine": "8.1", "sujets/media et journalisme": "6.0", "sujets/politique": "8.2", "type/débat": "8.2"}, {"catégories/programme d'information": "9.8", "envies/se cultiver": "4.8", "sujets/actualité": "2.0", "sujets/media et journalisme": "2.0", "sujets/politique": "4.8", "type/débat": "8.4"}, {"envies/se cultiver": "5.4", "sujets/actualité": "9.0", "sujets/politique": "5.8", "type/débat": "7.7"}, {"catégories/programme d'information": "8.5", "type/débat": "6.6", "type/interview": "5.9"}, {"catégories/programme d'information": "4.0", "envies/se cultiver": "7.0", "sujets/actualité": "5.0", "sujets/politique": "4.0", "type/débat": "10.0"}, {"envies/se cultiver": "7.0", "sujets/actualité": "5.0", "sujets/politique": "4.0", "type/débat": "5.0", "type/interview": "5.0"}, {"catégories/magazine": "3.0", "catégories/programme d'information": "6.0", "envies/se cultiver": "6.0", "sujets/actualité": "10.0", "sujets/politique": "6.0", "type/débat": "3.0", "type/interview": "3.0"}, {"catégories/magazine": "9.1", "catégories/programme d'information": "4.4", "sujets/media et journalisme": "5.8", "sujets/politique": "5.0", "type/débat": "4.4"}, {"catégories/magazine": "2.3", "type/débat": "10.0", "type/interview": "10.0"}, {"sujets/actualité": "9.9", "sujets/media et journalisme": "3.8", "type/débat": "4.0", "type/interview": "4.4"}, {"catégories/programme d'information": "4.0", "sujets/politique": "4.0", "type/débat": "4.0", "type/interview": "10.0"}, {"envies/se cultiver": "2.0", "sujets/actualité": "4.0", "sujets/politique": "2.0", "type/débat": "10.0"}, {"envies/se cultiver": "2.0", "sujets/media et journalisme": "4.0", "type/débat": "8.0", "type/interview": "8.0"}, {"catégories/magazine": "3.0", "catégories/programme d'information": "7.5", "envies/se cultiver": "6.5", "sujets/politique": "10.0", "type/débat": "3.0", "type/interview": "3.0"}, {"envies/se cultiver": "8.9", "sujets/actualité": "9.3", "sujets/media et journalisme": "9.0", "type/débat": "9.3", "type/interview": "9.0"}, {"catégories/programme d'information": "9.0", "sujets/actualité": "5.0", "sujets/politique": "5.2", "type/débat": "5.0"}, {"catégories/magazine": "10.0", "catégories/programme d'information": "9.0", "envies/se cultiver": "9.0", "sujets/actualité": "9.0", "type/débat": "9.0", "type/interview": "10.0"}, {"catégories/programme d'information": "10.0", "envies/se cultiver": "4.5", "sujets/actualité": "4.5", "sujets/politique": "10.0", "type/débat": "10.0", "type/interview": "4.5"}, {"catégories/programme d'information": "3.4", "envies/se cultiver": "9.9", "type/débat": "3.4", "type/interview": "5.0"}, {"catégories/programme d'information": "9.3", "sujets/actualité": "8.2", "sujets/media et journalisme": "8.2", "sujets/politique": "9.3", "type/débat": "9.3", "type/interview": "8.2"}, {"catégories/programme d'information": "10.0", "sujets/actualité": "5.0", "sujets/media et journalisme": "5.0", "sujets/politique": "10.0", "type/débat": "2.0", "type/interview": "2.0"}, {"catégories/programme d'information": "3.7", "envies/se cultiver": "3.6", "sujets/actualité": "10.0", "type/débat": "5.3", "type/interview": "3.7"}, {"catégories/magazine": "8.5", "catégories/programme d'information": "4.0", "sujets/media et journalisme": "7.5", "sujets/politique": "10.0", "type/débat": "4.0"}, {"catégories/magazine": "7.0", "envies/se cultiver": "3.9", "sujets/actualité": "5.0", "sujets/media et journalisme": "5.0", "sujets/politique": "10.0", "type/débat": "10.0"}, {"catégories/magazine": "6.0", "catégories/programme d'information": "10.0", "envies/se cultiver": "2.5", "sujets/politique": "4.0", "type/débat": "10.0"}, {"catégories/magazine": "10.0", "catégories/programme d'information": "10.0", "envies/se cultiver": "9.0", "sujets/politique": "5.0", "type/débat": "5.0"}, {"catégories/magazine": "2.5", "catégories/programme d'information": "3.5", "sujets/actualité": "7.5", "sujets/politique": "10.0", "type/débat": "3.5"}, {"catégories/magazine": "3.0", "catégories/programme d'information": "6.0", "envies/se cultiver": "2.0", "sujets/actualité": "10.0", "sujets/politique": "2.0", "type/débat": "3.0"}, {"catégories/programme d'information": "4.5", "envies/se cultiver": "4.5", "sujets/actualité": "10.0", "sujets/politique": "9.9", "type/débat": "4.7", "type/interview": "4.3"}, {"sujets/media et journalisme": "9.5", "sujets/politique": "5.8", "type/débat": "4.3", "type/interview": "4.3"}, {"envies/se cultiver": "2.0", "sujets/actualité": "9.5", "sujets/politique": "6.0", "type/débat": "6.5", "type/interview": "9.0"}, {"envies/se cultiver": "7.6", "sujets/media et journalisme": "7.8", "type/débat": "5.0", "type/interview": "6.7"}, {"sujets/actualité": "9.0", "type/débat": "9.0", "type/interview": "8.9"}, {"sujets/actualité": "7.0", "sujets/media et journalisme": "9.1", "type/débat": "7.0", "type/interview": "6.5"}, {"catégories/programme d'information": "8.0", "sujets/actualité": "4.0", "sujets/politique": "10.0", "type/débat": "8.0", "type/interview": "6.0"}, {"catégories/programme d'information": "10.0", "sujets/politique": "10.0", "type/débat": "2.0", "type/interview": "2.0"}, {"catégories/programme d'information": "10.0", "sujets/politique": "4.0", "type/débat": "4.0", "type/interview": "4.0"}, {"catégories/magazine": "3.0", "catégories/programme d'information": "10.0", "sujets/politique": "3.0", "type/débat": "3.0"}, {"catégories/programme d'information": "5.0", "sujets/media et journalisme": "10.0", "sujets/politique": "8.0", "type/débat": "5.0"}, {"catégories/magazine": "9.0", "envies/se cultiver": "8.0", "sujets/media et journalisme": "2.0", "sujets/politique": "7.0", "type/débat": "10.0"}, {"catégories/magazine": "6.8", "sujets/media et journalisme": "10.0", "type/débat": "7.0", "type/interview": "7.0"}, {"catégories/magazine": "5.0", "catégories/programme d'information": "10.0", "envies/se cultiver": "10.0", "sujets/actualité": "10.0", "sujets/politique": "10.0", "type/débat": "10.0", "type/interview": "5.0"}, {"catégories/magazine": "2.0", "envies/se cultiver": "2.0", "sujets/actualité": "5.0", "sujets/politique": "10.0", "type/débat": "5.0", "type/interview": "5.0"}, {"catégories/programme d'information": "3.0", "sujets/actualité": "3.0", "sujets/media et journalisme": "3.0", "sujets/politique": "10.0", "type/débat": "6.0", "type/interview": "6.0"}, {"catégories/programme d'information": "10.0", "sujets/actualité": "4.8", "type/débat": "3.7", "type/interview": "3.7"}, {"catégories/magazine": "7.5", "sujets/actualité": "4.1", "sujets/politique": "3.8", "type/débat": "9.2"}, {"catégories/magazine": "5.0", "catégories/programme d'information": "2.0", "sujets/actualité": "10.0", "sujets/politique": "5.0", "type/débat": "2.0"}, {"catégories/programme d'information": "5.0", "envies/se cultiver": "10.0", "sujets/media et journalisme": "5.0", "sujets/politique": "5.0", "type/débat": "9.0"}, {"catégories/programme d'information": "6.8", "envies/se cultiver": "3.8", "sujets/actualité": "10.0", "sujets/media et journalisme": "5.2", "sujets/politique": "10.0", "type/débat": "3.8"}, {"envies/se cultiver": "2.5", "sujets/actualité": "9.0", "sujets/media et journalisme": "7.5", "sujets/politique": "7.5", "type/débat": "5.0"}, {"envies/se cultiver": "5.0", "sujets/media et journalisme": "3.0", "sujets/politique": "3.0", "type/débat": "6.0", "type/interview": "5.0"}, {"sujets/media et journalisme": "10.0", "sujets/politique": "10.0", "type/débat": "10.0", "type/interview": "7.0"}, {"catégories/magazine": "2.0", "catégories/programme d'information": "10.0", "envies/se cultiver": "4.7", "sujets/politique": "7.7", "type/débat": "2.0", "type/interview": "2.0"}, {"catégories/magazine": "9.0", "sujets/media et journalisme": "5.0", "sujets/politique": "10.0", "type/débat": "4.0", "type/interview": "4.0"}, {"catégories/magazine": "3.0", "catégories/programme d'information": "10.0", "envies/se cultiver": "3.0", "sujets/media et journalisme": "2.0", "sujets/politique": "3.0", "type/débat": "3.0"}, {"catégories/magazine": "2.0", "catégories/programme d'information": "10.0", "envies/se cultiver": "4.0", "sujets/politique": "4.0", "type/débat": "2.0", "type/interview": "2.0"}, {"catégories/programme d'information": "5.0", "envies/se cultiver": "5.0", "sujets/actualité": "5.0", "sujets/politique": "10.0", "type/débat": "5.0", "type/interview": "4.0"}, {"envies/se cultiver": "5.0", "sujets/actualité": "5.0", "sujets/media et journalisme": "10.0", "type/débat": "5.0", "type/interview": "5.0"}, {"catégories/magazine": "3.5", "catégories/programme d'information": "10.0", "envies/se cultiver": "10.0", "sujets/actualité": "10.0", "sujets/media et journalisme": "5.5", "sujets/politique": "10.0", "type/débat": "3.5"}, {"catégories/programme d'information": "6.0", "sujets/media et journalisme": "10.0", "sujets/politique": "5.0", "type/débat": "5.0"}, {"catégories/magazine": "2.0", "catégories/programme d'information": "2.0", "envies/se cultiver": "10.0", "type/débat": "2.0", "type/interview": "3.0"}, {"catégories/magazine": "5.0", "sujets/actualité": "2.0", "sujets/politique": "10.0", "type/débat": "7.5"}, {"sujets/actualité": "8.1", "sujets/politique": "8.0", "type/débat": "7.7"}, {"catégories/magazine": "5.0", "catégories/programme d'information": "2.0", "sujets/actualité": "10.0", "sujets/politique": "2.0", "type/débat": "2.0"}, {"catégories/magazine": "7.4", "envies/se cultiver": "6.3", "sujets/actualité": "8.4", "sujets/politique": "6.4", "type/débat": "7.3"}, {"catégories/magazine": "2.0", "catégories/programme d'information": "7.0", "sujets/actualité": "10.0", "sujets/politique": "2.0", "type/débat": "2.0"}, {"sujets/actualité": "6.4", "sujets/politique": "7.6", "type/débat": "6.0", "type/interview": "6.3"}, {"catégories/magazine": "5.8", "catégories/programme d'information": "8.6", "envies/se cultiver": "9.7", "sujets/actualité": "8.8", "sujets/politique": "8.8", "type/débat": "8.6"}, {"catégories/magazine": "6.6", "sujets/actualité": "4.2", "sujets/media et journalisme": "4.2", "sujets/politique": "5.2", "type/débat": "10.0"}, {"catégories/programme d'information": "10.0", "sujets/actualité": "2.0", "type/débat": "4.5", "type/interview": "4.5"}, {"catégories/magazine": "8.7", "envies/se cultiver": "6.3", "sujets/politique": "6.5", "type/débat": "7.4"}, {"catégories/magazine": "5.4", "sujets/actualité": "5.0", "sujets/media et journalisme": "5.0", "sujets/politique": "10.0", "type/débat": "10.0"}, {"catégories/magazine": "3.0", "sujets/politique": "10.0", "type/débat": "2.3", "type/interview": "2.3"}, {"catégories/magazine": "8.7", "envies/se cultiver": "6.3", "type/débat": "6.0", "type/interview": "6.1"}, {"catégories/programme d'information": "4.0", "envies/se cultiver": "3.8", "sujets/politique": "10.0", "type/débat": "4.0", "type/interview": "4.6"}, {"catégories/programme d'information": "5.0", "envies/se cultiver": "4.0", "sujets/actualité": "2.0", "sujets/media et journalisme": "2.0", "sujets/politique": "5.0", "type/débat": "10.0"}, {"catégories/magazine": "7.0", "envies/se cultiver": "5.0", "sujets/actualité": "9.0", "sujets/politique": "10.0", "type/débat": "9.0", "type/interview": "9.0"}, {"catégories/programme d'information": "9.0", "envies/se cultiver": "3.0", "sujets/media et journalisme": "10.0", "type/débat": "5.5", "type/interview": "5.5"}, {"catégories/magazine": "9.7", "envies/se cultiver": "3.0", "sujets/actualité": "7.7", "type/débat": "2.3", "type/interview": "2.3"}, {"catégories/programme d'information": "5.0", "envies/se cultiver": "5.0", "sujets/actualité": "10.0", "sujets/politique": "5.0", "type/débat": "4.0", "type/interview": "4.0"}, {"catégories/magazine": "6.6", "sujets/politique": "8.2", "type/débat": "9.1"}, {"catégories/programme d'information": "5.0", "envies/se cultiver": "2.0", "sujets/actualité": "4.0", "sujets/politique": "5.0", "type/débat": "10.0"}, {"envies/se cultiver": "6.0", "sujets/media et journalisme": "5.0", "type/débat": "10.0", "type/interview": "6.0"}, {"envies/se cultiver": "2.7", "sujets/actualité": "4.7", "sujets/politique": "10.0", "type/débat": "4.7", "type/interview": "4.7"}, {"sujets/actualité": "3.1", "sujets/media et journalisme": "3.1", "sujets/politique": "6.4", "type/débat": "8.2"}, {"catégories/magazine": "6.0", "catégories/programme d'information": "6.0", "sujets/politique": "4.0", "type/débat": "10.0"}, {"catégories/programme d'information": "8.7", "sujets/actualité": "3.6", "sujets/media et journalisme": "3.6", "sujets/politique": "7.5", "type/débat": "8.0"}, {"catégories/programme d'information": "3.5", "sujets/politique": "7.0", "type/débat": "3.5", "type/interview": "10.0"}, {"envies/se cultiver": "7.0", "sujets/actualité": "3.0", "sujets/politique": "10.0", "type/débat": "3.0"}, {"catégories/magazine": "3.0", "sujets/actualité": "5.0", "sujets/media et journalisme": "10.0", "type/débat": "10.0", "type/interview": "10.0"}, {"catégories/programme d'information": "10.0", "sujets/actualité": "3.0", "sujets/media et journalisme": "3.0", "sujets/politique": "10.0", "type/débat": "10.0", "type/interview": "3.0"}, {"catégories/magazine": "7.4", "catégories/programme d'information": "9.9", "envies/se cultiver": "9.9", "sujets/actualité": "9.9", "sujets/politique": "2.1", "type/débat": "9.9"}, {"catégories/magazine": "9.0", "envies/se cultiver": "2.0", "sujets/actualité": "3.0", "sujets/politique": "10.0", "type/débat": "9.0"}, {"catégories/programme d'information": "8.7", "sujets/media et journalisme": "5.4", "sujets/politique": "6.1", "type/débat": "6.9"}, {"envies/se cultiver": "3.4", "sujets/actualité": "7.9", "sujets/media et journalisme": "4.3", "sujets/politique": "4.3", "type/débat": "7.5"}, {"catégories/magazine": "2.0", "sujets/actualité": "4.0", "sujets/politique": "4.0", "type/débat": "2.0", "type/interview": "10.0"}, {"catégories/programme d'information": "4.0", "envies/se cultiver": "9.0", "sujets/actualité": "10.0", "sujets/politique": "10.0", "type/débat": "4.0", "type/interview": "5.0"}, {"catégories/magazine": "8.0", "sujets/actualité": "10.0", "sujets/media et journalisme": "3.0", "sujets/politique": "3.0", "type/débat": "4.0"}, {"catégories/magazine": "7.0", "catégories/programme d'information": "3.0", "envies/se cultiver": "10.0", "sujets/politique": "3.0", "type/débat": "3.0", "type/interview": "3.0"}, {"catégories/magazine": "9.0", "envies/se cultiver": "3.0", "sujets/actualité": "10.0", "sujets/media et journalisme": "9.0", "type/débat": "10.0", "type/interview": "10.0"}, {"envies/se cultiver": "6.0", "sujets/actualité": "3.0", "sujets/media et journalisme": "3.0", "sujets/politique": "3.0", "type/débat": "6.0"}, {"catégories/magazine": "7.0", "envies/se cultiver": "10.0", "sujets/actualité": "10.0", "sujets/media et journalisme": "10.0", "type/débat": "10.0", "type/interview": "10.0"}, {"catégories/magazine": "6.8", "envies/se cultiver": "7.5", "sujets/media et journalisme": "7.8", "sujets/politique": "9.8", "type/débat": "9.8"}, {"catégories/programme d'information": "10.0", "sujets/actualité": "3.0", "sujets/media et journalisme": "3.0", "sujets/politique": "4.0", "type/débat": "2.0"}, {"catégories/magazine": "8.0", "catégories/programme d'information": "10.0", "envies/se cultiver": "10.0", "sujets/politique": "10.0", "type/débat": "10.0", "type/interview": "8.0"}, {"sujets/media et journalisme": "5.3", "type/débat": "6.0", "type/interview": "6.0"}, {"sujets/media et journalisme": "7.0", "sujets/politique": "10.0", "type/débat": "7.0", "type/interview": "3.0"}, {"catégories/programme d'information": "5.3", "sujets/actualité": "9.9", "type/débat": "3.7", "type/interview": "3.5"}, {"sujets/politique": "7.7", "type/débat": "5.9", "type/interview": "6.0"}, {"catégories/programme d'information": "8.9", "envies/se cultiver": "8.7", "sujets/actualité": "10.0", "sujets/media et journalisme": "5.2", "sujets/politique": "8.7", "type/débat": "3.7"}, {"sujets/politique": "6.3", "type/débat": "9.3"}, {"envies/se cultiver": "3.0", "sujets/media et journalisme": "3.0", "sujets/politique": "3.0", "type/débat": "3.0", "type/interview": "3.0"}, {"envies/se cultiver": "5.1", "sujets/media et journalisme": "5.3", "sujets/politique": "5.7", "type/débat": "7.5"}, {"catégories/magazine": "5.0", "catégories/programme d'information": "3.0", "envies/se cultiver": "6.0", "sujets/actualité": "6.0", "sujets/politique": "10.0", "type/débat": "3.0", "type/interview": "3.0"}, {"catégories/magazine": "3.0", "sujets/media et journalisme": "10.0", "sujets/politique": "5.0", "type/débat": "5.0"}, {"catégories/magazine": "10.0", "sujets/actualité": "3.0", "sujets/politique": "2.0", "type/débat": "7.0"}, {"catégories/magazine": "7.3", "sujets/politique": "9.3", "type/débat": "3.1", "type/interview": "3.1"}, {"envies/se cultiver": "5.2", "sujets/actualité": "9.8", "sujets/politique": "5.5", "type/débat": "6.8", "type/interview": "5.3"}, {"sujets/actualité": "3.0", "sujets/media et journalisme": "9.7", "sujets/politique": "9.7", "type/débat": "8.0"}, {"catégories/programme d'information": "5.0", "sujets/actualité": "5.0", "sujets/media et journalisme": "5.0", "sujets/politique": "10.0", "type/débat": "4.0", "type/interview": "4.0"}, {"catégories/magazine": "3.0", "catégories/programme d'information": "10.0", "envies/se cultiver": "6.0", "sujets/actualité": "6.0", "sujets/politique": "10.0", "type/débat": "10.0"}, {"catégories/magazine": "5.0", "catégories/programme d'information": "4.7", "envies/se cultiver": "10.0", "sujets/actualité": "6.0", "sujets/politique": "5.6", "type/débat": "4.6", "type/interview": "4.7"}, {"catégories/magazine": "5.9", "sujets/actualité": "7.5", "sujets/politique": "8.7", "type/débat": "7.1"}, {"catégories/magazine": "3.7", "envies/se cultiver": "5.0", "sujets/actualité": "9.6", "type/débat": "4.5", "type/interview": "4.4"}, {"catégories/programme d'information": "4.8", "sujets/actualité": "10.0", "sujets/media et journalisme": "4.4", "type/débat": "4.2", "type/interview": "3.6"}, {"catégories/programme d'information": "5.0", "sujets/actualité": "5.0", "sujets/media et journalisme": "5.0", "sujets/politique": "10.0", "type/débat": "4.0"}, {"envies/se cultiver": "2.0", "sujets/actualité": "10.0", "sujets/politique": "3.0", "type/débat": "3.0", "type/interview": "6.0"}, {"envies/se cultiver": "2.4", "sujets/actualité": "4.8", "sujets/politique": "6.4", "type/débat": "5.2", "type/interview": "5.4"}, {"catégories/magazine": "10.0", "catégories/programme d'information": "10.0", "envies/se cultiver": "8.0", "sujets/politique": "2.0", "type/débat": "8.0"}, {"catégories/magazine": "7.7", "catégories/programme d'information": "10.0", "sujets/actualité": "4.0", "sujets/politique": "4.7", "type/débat": "10.0"}, {"type/débat": "7.3", "type/interview": "7.6"}, {"catégories/magazine": "9.0", "catégories/programme d'information": "3.0", "envies/se cultiver": "10.0", "type/débat": "3.0", "type/interview": "3.0"}, {"catégories/programme d'information": "10.0", "envies/se cultiver": "5.0", "sujets/actualité": "2.0", "sujets/media et journalisme": "5.0", "sujets/politique": "5.0", "type/débat": "5.0"}, {"catégories/programme d'information": "10.0", "envies/se cultiver": "4.0", "sujets/media et journalisme": "10.0", "sujets/politique": "5.0", "type/débat": "5.0"}, {"catégories/programme d'information": "10.0", "sujets/actualité": "10.0", "sujets/politique": "5.3", "type/débat": "5.3", "type/interview": "5.3"}, {"catégories/programme d'information": "3.7", "envies/se cultiver": "10.0", "sujets/actualité": "5.0", "type/débat": "3.7", "type/interview": "5.0"}, {"catégories/magazine": "5.0", "envies/se cultiver": "2.0", "sujets/actualité": "10.0", "sujets/politique": "4.0", "type/débat": "10.0", "type/interview": "10.0"}, {"catégories/programme d'information": "10.0", "sujets/actualité": "10.0", "sujets/politique": "2.0", "type/débat": "2.0", "type/interview": "2.0"}, {"catégories/programme d'information": "4.0", "sujets/actualité": "10.0", "sujets/politique": "9.0", "type/débat": "5.0", "type/interview": "5.0"}, {"catégories/magazine": "10.0", "envies/se cultiver": "2.0", "sujets/actualité": "6.0", "sujets/politique": "6.0", "type/débat": "6.0", "type/interview": "6.0"}, {"catégories/magazine": "6.6", "catégories/programme d'information": "6.6", "envies/se cultiver": "10.0", "sujets/actualité": "8.2", "sujets/politique": "6.6", "type/débat": "6.6", "type/interview": "2.6"}, {"catégories/magazine": "10.0", "envies/se cultiver": "2.0", "type/débat": "3.0", "type/interview": "3.0"}, {"envies/se cultiver": "3.2", "sujets/actualité": "10.0", "sujets/media et journalisme": "3.2", "sujets/politique": "3.2", "type/débat": "5.7"}, {"envies/se cultiver": "4.0", "sujets/actualité": "4.0", "sujets/politique": "4.0", "type/débat": "10.0"}, {"catégories/programme d'information": "4.0", "envies/se cultiver": "9.0", "sujets/actualité": "10.0", "sujets/media et journalisme": "4.0", "sujets/politique": "4.0", "type/débat": "5.0"}, {"catégories/magazine": "5.4", "sujets/actualité": "2.6", "sujets/politique": "10.0", "type/débat": "2.6", "type/interview": "2.6"}, {"catégories/magazine": "10.0", "catégories/programme d'information": "3.0", "envies/se cultiver": "7.0", "sujets/actualité": "3.0", "sujets/politique": "7.0", "type/débat": "3.0", "type/interview": "3.0"}, {"catégories/magazine": "7.5", "catégories/programme d'information": "6.3", "envies/se cultiver": "8.8", "sujets/politique": "6.9", "type/débat": "6.2"}, {"catégories/magazine": "5.0", "envies/se cultiver": "10.0", "sujets/actualité": "5.0", "type/débat": "5.0", "type/interview": "10.0"}, {"catégories/programme d'information": "8.7", "envies/se cultiver": "7.4", "sujets/actualité": "8.9", "sujets/politique": "7.4", "type/débat": "6.6"}, {"catégories/magazine": "6.0", "catégories/programme d'information": "6.0", "envies/se cultiver": "8.0", "sujets/actualité": "10.0", "sujets/politique": "10.0", "type/débat": "6.0", "type/interview": "3.0"}, {"sujets/media et journalisme": "5.0", "sujets/politique": "5.3", "type/débat": "6.7", "type/interview": "6.7"}, {"catégories/magazine": "10.0", "catégories/programme d'information": "4.0", "envies/se cultiver": "6.0", "type/débat": "4.0", "type/interview": "4.0"}, {"catégories/programme d'information": "8.1", "sujets/actualité": "3.9", "sujets/media et journalisme": "8.1", "type/débat": "10.0", "type/interview": "10.0"}, {"catégories/magazine": "9.0", "envies/se cultiver": "2.0", "sujets/actualité": "5.0", "sujets/media et journalisme": "10.0", "type/débat": "5.0", "type/interview": "5.0"}, {"catégories/programme d'information": "6.0", "sujets/actualité": "2.5", "sujets/media et journalisme": "2.5", "sujets/politique": "2.5", "type/débat": "10.0"}, {"catégories/magazine": "10.0", "envies/se cultiver": "6.0", "sujets/actualité": "6.0", "type/débat": "6.0", "type/interview": "6.0"}, {"catégories/programme d'information": "4.5", "envies/se cultiver": "4.5", "sujets/actualité": "10.0", "sujets/media et journalisme": "4.5", "sujets/politique": "4.5", "type/débat": "4.5"}, {"sujets/actualité": "5.0", "sujets/politique": "5.0", "type/débat": "5.0", "type/interview": "10.0"}, {"catégories/magazine": "8.0", "catégories/programme d'information": "4.8", "envies/se cultiver": "9.7", "sujets/actualité": "8.4", "type/débat": "4.8", "type/interview": "4.8"}, {"catégories/magazine": "7.0", "envies/se cultiver": "5.0", "sujets/actualité": "5.0", "sujets/politique": "5.0", "type/débat": "10.0"}, {"catégories/magazine": "10.0", "envies/se cultiver": "8.0", "sujets/media et journalisme": "3.0", "sujets/politique": "4.0", "type/débat": "4.0"}, {"catégories/magazine": "7.0", "catégories/programme d'information": "5.0", "envies/se cultiver": "5.0", "sujets/actualité": "5.0", "sujets/media et journalisme": "10.0", "sujets/politique": "5.0", "type/débat": "5.0"}, {"catégories/programme d'information": "9.4", "sujets/politique": "7.1", "type/débat": "9.4", "type/interview": "6.9"}, {"catégories/magazine": "4.5", "catégories/programme d'information": "7.1", "sujets/politique": "8.7", "type/débat": "7.1"}, {"catégories/magazine": "4.9", "catégories/programme d'information": "7.3", "type/débat": "4.9", "type/interview": "4.9"}, {"catégories/magazine": "2.0", "sujets/actualité": "10.0", "sujets/media et journalisme": "2.0", "sujets/politique": "2.0", "type/débat": "3.0"}, {"sujets/actualité": "5.0", "sujets/media et journalisme": "5.0", "type/débat": "10.0", "type/interview": "6.7"}, {"catégories/magazine": "3.7", "catégories/programme d'information": "10.0", "sujets/media et journalisme": "8.3", "sujets/politique": "10.0", "type/débat": "10.0"}, {"catégories/programme d'information": "4.0", "envies/se cultiver": "2.0", "sujets/actualité": "10.0", "type/débat": "2.0", "type/interview": "2.0"}, {"catégories/magazine": "2.0", "catégories/programme d'information": "10.0", "sujets/politique": "10.0", "type/débat": "2.0", "type/interview": "2.0"}, {"catégories/programme d'information": "10.0", "sujets/media et journalisme": "3.5", "sujets/politique": "7.5", "type/débat": "3.5"}, {"envies/se cultiver": "2.0", "sujets/actualité": "7.0", "sujets/politique": "2.0", "type/débat": "7.0", "type/interview": "7.0"}, {"catégories/programme d'information": "3.0", "sujets/actualité": "5.0", "sujets/politique": "10.0", "type/débat": "2.0", "type/interview": "2.0"}, {"catégories/programme d'information": "10.0", "envies/se cultiver": "5.0", "type/débat": "10.0", "type/interview": "7.0"}, {"catégories/magazine": "2.0", "catégories/programme d'information": "5.0", "sujets/actualité": "10.0", "type/débat": "5.0", "type/interview": "5.0"}, {"envies/se cultiver": "2.0", "sujets/actualité": "2.5", "sujets/politique": "10.0", "type/débat": "2.5", "type/interview": "2.5"}, {"catégories/magazine": "10.0", "catégories/programme d'information": "10.0", "envies/se cultiver": "10.0", "sujets/actualité": "5.0", "sujets/media et journalisme": "10.0", "sujets/politique": "10.0", "type/débat": "10.0"}, {"catégories/programme d'information": "10.0", "envies/se cultiver": "2.0", "sujets/actualité": "2.0", "sujets/politique": "3.0", "type/débat": "7.0"}, {"catégories/magazine": "4.5", "catégories/programme d'information": "10.0", "envies/se cultiver": "4.5", "sujets/media et journalisme": "4.5", "sujets/politique": "6.0", "type/débat": "4.5"}, {"catégories/programme d'information": "6.0", "envies/se cultiver": "9.7", "sujets/actualité": "6.1", "sujets/politique": "6.0", "type/débat": "6.0", "type/interview": "5.4"}, {"envies/se cultiver": "5.6", "sujets/politique": "6.6", "type/débat": "6.6", "type/interview": "6.0"}, {"catégories/magazine": "6.8", "sujets/media et journalisme": "3.8", "type/débat": "4.8", "type/interview": "4.8"}, {"catégories/programme d'information": "9.3", "envies/se cultiver": "3.0", "sujets/actualité": "3.3", "sujets/politique": "10.0", "type/débat": "4.7"}, {"catégories/programme d'information": "4.5", "envies/se cultiver": "10.0", "sujets/politique": "4.6", "type/débat": "4.5", "type/interview": "4.2"}, {"catégories/magazine": "5.0", "catégories/programme d'information": "6.6", "sujets/media et journalisme": "10.0", "sujets/politique": "6.6", "type/débat": "6.6"}, {"sujets/media et journalisme": "2.3", "sujets/politique": "10.0", "type/débat": "4.7", "type/interview": "5.0"}, {"catégories/magazine": "5.0", "catégories/programme d'information": "4.0", "sujets/actualité": "2.0", "sujets/politique": "10.0", "type/débat": "4.0"}, {"catégories/magazine": "8.0", "envies/se cultiver": "5.0", "sujets/media et journalisme": "6.0", "sujets/politique": "5.0", "type/débat": "10.0"}, {"sujets/actualité": "4.7", "sujets/media et journalisme": "3.3", "type/débat": "4.7", "type/interview": "10.0"}, {"catégories/magazine": "4.0", "envies/se cultiver": "5.0", "sujets/politique": "5.0", "type/débat": "4.0", "type/interview": "4.0"}, {"envies/se cultiver": "5.0", "sujets/actualité": "5.0", "sujets/media et journalisme": "5.0", "type/débat": "5.0", "type/interview": "10.0"}, {"catégories/magazine": "10.0", "catégories/programme d'information": "6.7", "envies/se cultiver": "3.0", "sujets/politique": "3.3", "type/débat": "6.7"}, {"catégories/magazine": "9.0", "sujets/actualité": "2.0", "sujets/media et journalisme": "2.0", "sujets/politique": "2.0", "type/débat": "10.0"}, {"catégories/programme d'information": "8.5", "sujets/media et journalisme": "7.5", "sujets/politique": "8.5", "type/débat": "10.0", "type/interview": "7.5"}, {"sujets/actualité": "10.0", "sujets/media et journalisme": "4.0", "type/débat": "5.0", "type/interview": "2.0"}, {"catégories/magazine": "2.0", "catégories/programme d'information": "5.0", "envies/se cultiver": "10.0", "sujets/actualité": "5.0", "sujets/politique": "5.0", "type/débat": "5.0", "type/interview": "5.0"}, {"catégories/magazine": "5.0", "envies/se cultiver": "10.0", "type/débat": "5.0", "type/interview": "5.0"}, {"sujets/actualité": "10.0", "sujets/politique": "4.0", "type/débat": "10.0", "type/interview": "6.0"}, {"catégories/magazine": "7.0", "envies/se cultiver": "4.0", "sujets/actualité": "5.0", "sujets/politique": "10.0", "type/débat": "5.0", "type/interview": "5.0"}, {"sujets/actualité": "2.0", "sujets/politique": "7.0", "type/débat": "7.0", "type/interview": "7.0"}, {"catégories/magazine": "3.0", "catégories/programme d'information": "5.0", "envies/se cultiver": "5.0", "sujets/actualité": "10.0", "sujets/media et journalisme": "5.0", "sujets/politique": "10.0", "type/débat": "5.0"}, {"catégories/programme d'information": "6.0", "sujets/actualité": "10.0", "sujets/politique": "2.0", "type/débat": "3.0", "type/interview": "3.0"}, {"catégories/magazine": "9.0", "sujets/actualité": "4.0", "type/débat": "10.0", "type/interview": "10.0"}, {"catégories/magazine": "6.7", "type/débat": "3.9", "type/interview": "3.9"}, {"catégories/programme d'information": "5.0", "sujets/actualité": "2.0", "sujets/media et journalisme": "5.0", "sujets/politique": "2.0", "type/débat": "10.0"}, {"sujets/actualité": "8.5", "sujets/media et journalisme": "5.4", "sujets/politique": "5.4", "type/débat": "5.7"}, {"catégories/magazine": "9.6", "envies/se cultiver": "4.4", "sujets/politique": "6.8", "type/débat": "4.6", "type/interview": "4.6"}, {"catégories/programme d'information": "10.0", "envies/se cultiver": "2.3", "sujets/actualité": "3.5", "sujets/media et journalisme": "3.5", "sujets/politique": "8.5", "type/débat": "3.7"}, {"catégories/magazine": "7.0", "sujets/media et journalisme": "10.0", "type/débat": "3.0", "type/interview": "3.0"}, {"catégories/programme d'information": "9.0", "sujets/media et journalisme": "9.0", "sujets/politique": "10.0", "type/débat": "5.0", "type/interview": "5.0"}, {"envies/se cultiver": "4.0", "sujets/actualité": "2.0", "type/débat": "10.0", "type/interview": "4.0"}, {"catégories/magazine": "10.0", "catégories/programme d'information": "4.0", "envies/se cultiver": "9.0", "sujets/politique": "4.0", "type/débat": "4.0", "type/interview": "4.0"}, {"catégories/programme d'information": "10.0", "sujets/actualité": "10.0", "sujets/media et journalisme": "6.0", "sujets/politique": "6.0", "type/débat": "6.0"}, {"catégories/programme d'information": "8.6", "sujets/actualité": "4.1", "type/débat": "8.8", "type/interview": "8.8"}, {"catégories/programme d'information": "9.6", "envies/se cultiver": "6.0", "sujets/politique": "6.8", "type/débat": "5.1"}, {"catégories/programme d'information": "9.8", "envies/se cultiver": "6.1", "sujets/media et journalisme": "3.2", "sujets/politique": "6.1", "type/débat": "5.6"}, {"catégories/programme d'information": "3.0", "sujets/media et journalisme": "3.0", "sujets/politique": "3.0", "type/débat": "10.0"}, {"envies/se cultiver": "2.0", "sujets/actualité": "2.0", "type/débat": "10.0", "type/interview": "10.0"}, {"catégories/magazine": "6.5", "catégories/programme d'information": "7.8", "envies/se cultiver": "9.9", "sujets/actualité": "7.8", "sujets/media et journalisme": "3.8", "sujets/politique": "7.8", "type/débat": "7.8"}, {"catégories/magazine": "9.5", "catégories/programme d'information": "9.9", "sujets/actualité": "4.6", "sujets/media et journalisme": "4.6", "sujets/politique": "9.5", "type/débat": "9.5"}, {"catégories/magazine": "7.0", "catégories/programme d'information": "5.5", "sujets/actualité": "8.9", "sujets/politique": "6.4", "type/débat": "5.6"}, {"catégories/magazine": "10.0", "sujets/media et journalisme": "8.0", "sujets/politique": "4.0", "type/débat": "9.0"}, {"envies/se cultiver": "5.0", "sujets/actualité": "9.0", "sujets/media et journalisme": "10.0", "type/débat": "5.0", "type/interview": "5.0"}, {"catégories/magazine": "3.0", "envies/se cultiver": "2.0", "sujets/media et journalisme": "2.0", "sujets/politique": "10.0", "type/débat": "5.0"}, {"catégories/programme d'information": "10.0", "sujets/politique": "8.0", "type/débat": "4.0", "type/interview": "4.0"}, {"catégories/magazine": "6.0", "envies/se cultiver": "6.0", "sujets/actualité": "4.0", "sujets/politique": "8.0", "type/débat": "10.0"}, {"catégories/magazine": "7.8", "envies/se cultiver": "9.9", "sujets/media et journalisme": "2.2", "sujets/politique": "4.2", "type/débat": "6.2"}, {"catégories/magazine": "3.4", "envies/se cultiver": "5.1", "sujets/actualité": "9.9", "type/débat": "9.9", "type/interview": "9.9"}, {"catégories/magazine": "10.0", "envies/se cultiver": "6.0", "sujets/politique": "5.0", "type/débat": "2.0", "type/interview": "2.0"}, {"catégories/programme d'information": "8.8", "sujets/media et journalisme": "8.9", "type/débat": "8.4", "type/interview": "8.4"}, {"catégories/magazine": "10.0", "envies/se cultiver": "4.0", "sujets/actualité": "3.0", "sujets/politique": "4.0", "type/débat": "7.0"}, {"catégories/programme d'information": "6.0", "sujets/actualité": "3.0", "sujets/media et journalisme": "3.0", "sujets/politique": "10.0", "type/débat": "6.0"}, {"catégories/magazine": "7.0", "sujets/actualité": "3.0", "sujets/media et journalisme": "10.0", "type/débat": "3.0", "type/interview": "3.0"}, {"catégories/magazine": "3.0", "catégories/programme d'information": "5.0", "sujets/actualité": "3.0", "sujets/politique": "10.0", "type/débat": "5.0"}, {"catégories/programme d'information": "6.9", "envies/se cultiver": "6.3", "type/débat": "6.2", "type/interview": "6.6"}, {"catégories/magazine": "5.6", "catégories/programme d'information": "4.1", "envies/se cultiver": "10.0", "sujets/politique": "7.0", "type/débat": "4.1", "type/interview": "4.3"}, {"catégories/programme d'information": "10.0", "sujets/media et journalisme": "4.6", "type/débat": "4.8", "type/interview": "4.8"}, {"catégories/magazine": "10.0", "catégories/programme d'information": "3.0", "envies/se cultiver": "6.0", "sujets/politique": "6.0", "type/débat": "3.0", "type/interview": "3.0"}, {"catégories/programme d'information": "10.0", "envies/se cultiver": "7.0", "sujets/actualité": "4.0", "sujets/media et journalisme": "4.0", "sujets/politique": "9.0", "type/débat": "9.0"}, {"catégories/programme d'information": "6.5", "sujets/actualité": "6.5", "sujets/media et journalisme": "10.0", "sujets/politique": "10.0", "type/débat": "10.0"}, {"catégories/programme d'information": "9.0", "envies/se cultiver": "4.0", "sujets/actualité": "4.0", "sujets/media et journalisme": "9.0", "type/débat": "10.0", "type/interview": "10.0"}, {"catégories/magazine": "5.8", "catégories/programme d'information": "4.2", "envies/se cultiver": "5.2", "sujets/media et journalisme": "4.8", "sujets/politique": "10.0", "type/débat": "4.2"}, {"catégories/magazine": "4.9", "catégories/programme d'information": "6.8", "envies/se cultiver": "9.9", "sujets/politique": "6.5", "type/débat": "4.8", "type/interview": "4.8"}, {"catégories/magazine": "3.0", "catégories/programme d'information": "3.0", "envies/se cultiver": "10.0", "sujets/actualité": "6.0", "sujets/politique": "3.0", "type/débat": "3.0"}, {"catégories/magazine": "4.0", "envies/se cultiver": "10.0", "sujets/actualité": "5.0", "sujets/politique": "3.0", "type/débat": "5.0"}, {"catégories/magazine": "10.0", "envies/se cultiver": "2.0", "sujets/media et journalisme": "8.0", "sujets/politique": "5.0", "type/débat": "4.0"}, {"catégories/magazine": "9.0", "envies/se cultiver": "2.0", "sujets/media et journalisme": "3.0", "sujets/politique": "10.0", "type/débat": "9.0"}, {"catégories/magazine": "2.0", "envies/se cultiver": "3.0", "sujets/actualité": "10.0", "sujets/politique": "2.0", "type/débat": "10.0"}, {"catégories/magazine": "10.0", "envies/se cultiver": "3.0", "sujets/media et journalisme": "4.0", "type/débat": "4.0", "type/interview": "4.0"}, {"catégories/magazine": "8.5", "catégories/programme d'information": "7.6", "envies/se cultiver": "8.5", "sujets/media et journalisme": "6.6", "sujets/politique": "7.6", "type/débat": "7.6"}, {"envies/se cultiver": "5.0", "sujets/politique": "10.0", "type/débat": "2.0", "type/interview": "2.0"}, {"catégories/magazine": "6.0", "catégories/programme d'information": "4.5", "envies/se cultiver": "9.5", "sujets/actualité": "10.0", "sujets/media et journalisme": "6.0", "sujets/politique": "10.0", "type/débat": "4.5"}, {"catégories/magazine": "4.0", "sujets/actualité": "8.0", "sujets/media et journalisme": "3.0", "sujets/politique": "3.0", "type/débat": "4.0"}, {"catégories/magazine": "7.0", "envies/se cultiver": "5.0", "type/débat": "10.0", "type/interview": "10.0"}, {"catégories/magazine": "10.0", "catégories/programme d'information": "3.0", "sujets/actualité": "3.0", "sujets/politique": "3.0", "type/débat": "3.0"}, {"catégories/programme d'information": "4.2", "sujets/politique": "10.0", "type/débat": "5.1", "type/interview": "4.7"}, {"catégories/programme d'information": "7.7", "envies/se cultiver": "8.5", "sujets/actualité": "7.7", "sujets/media et journalisme": "6.7", "sujets/politique": "7.7", "type/débat": "7.7"}, {"catégories/magazine": "6.0", "catégories/programme d'information": "10.0", "sujets/politique": "3.0", "type/débat": "6.0"}, {"catégories/programme d'information": "4.8", "sujets/actualité": "10.0", "sujets/media et journalisme": "3.9", "sujets/politique": "3.9", "type/débat": "4.0"}, {"catégories/magazine": "10.0", "sujets/actualité": "9.0", "sujets/politique": "8.0", "type/débat": "5.0", "type/interview": "5.0"}, {"catégories/magazine": "3.0", "sujets/media et journalisme": "5.5", "sujets/politique": "10.0", "type/débat": "4.5", "type/interview": "4.5"}, {"catégories/programme d'information": "7.0", "sujets/actualité": "10.0", "sujets/media et journalisme": "2.0", "type/débat": "2.0", "type/interview": "2.0"}, {"catégories/programme d'information": "9.0", "envies/se cultiver": "2.0", "sujets/actualité": "7.0", "sujets/politique": "10.0", "type/débat": "9.0"}, {"catégories/magazine": "6.0", "envies/se cultiver": "5.8", "sujets/politique": "10.0", "type/débat": "4.3", "type/interview": "4.3"}, {"envies/se cultiver": "2.0", "sujets/actualité": "5.0", "sujets/media et journalisme": "10.0", "type/débat": "5.0", "type/interview": "5.0"}, {"catégories/magazine": "3.0", "catégories/programme d'information": "5.0", "envies/se cultiver": "5.0", "sujets/actualité": "5.0", "sujets/media et journalisme": "5.0", "sujets/politique": "10.0", "type/débat": "5.0"}, {"catégories/programme d'information": "10.0", "envies/se cultiver": "5.0", "sujets/actualité": "5.0", "sujets/politique": "5.0", "type/débat": "10.0", "type/interview": "5.0"}, {"catégories/programme d'information": "3.8", "envies/se cultiver": "3.9", "sujets/media et journalisme": "5.1", "sujets/politique": "10.0", "type/débat": "3.8"}, {"catégories/magazine": "4.0", "catégories/programme d'information": "8.0", "envies/se cultiver": "10.0", "sujets/media et journalisme": "4.0", "sujets/politique": "8.0", "type/débat": "4.0"}, {"catégories/magazine": "8.0", "catégories/programme d'information": "5.0", "envies/se cultiver": "6.0", "sujets/media et journalisme": "10.0", "sujets/politique": "9.0", "type/débat": "5.0"}, {"catégories/programme d'information": "9.0", "envies/se cultiver": "8.9", "sujets/politique": "9.0", "type/débat": "9.0", "type/interview": "8.5"}, {"catégories/magazine": "8.0", "catégories/programme d'information": "7.0", "sujets/media et journalisme": "7.0", "type/débat": "8.0", "type/interview": "8.0"}, {"catégories/magazine": "2.5", "sujets/actualité": "2.5", "type/débat": "10.0", "type/interview": "10.0"}, {"catégories/programme d'information": "3.0", "envies/se cultiver": "2.0", "sujets/politique": "10.0", "type/débat": "6.0", "type/interview": "6.0"}, {"catégories/programme d'information": "6.3", "sujets/actualité": "6.3", "sujets/politique": "10.0", "type/débat": "10.0", "type/interview": "10.0"}, {"catégories/programme d'information": "5.0", "sujets/actualité": "4.0", "sujets/politique": "2.0", "type/débat": "10.0"}, {"catégories/programme d'information": "4.0", "sujets/actualité": "10.0", "sujets/politique": "4.4", "type/débat": "5.0", "type/interview": "4.0"}, {"catégories/magazine": "5.5", "sujets/actualité": "7.5", "sujets/politique": "9.6", "type/débat": "7.5", "type/interview": "7.5"}, {"catégories/magazine": "7.8", "envies/se cultiver": "3.9", "sujets/actualité": "8.3", "type/débat": "8.3", "type/interview": "8.3"}, {"catégories/magazine": "3.0", "envies/se cultiver": "2.0", "sujets/actualité": "10.0", "sujets/media et journalisme": "3.8", "type/débat": "10.0", "type/interview": "10.0"}, {"catégories/programme d'information": "5.0", "envies/se cultiver": "10.0", "sujets/actualité": "5.0", "sujets/media et journalisme": "5.0", "sujets/politique": "5.0", "type/débat": "10.0"}, {"catégories/magazine": "2.5", "catégories/programme d'information": "6.5", "sujets/actualité": "10.0", "sujets/media et journalisme": "3.5", "sujets/politique": "3.5", "type/débat": "2.5"}, {"catégories/programme d'information": "6.0", "envies/se cultiver": "10.0", "sujets/actualité": "10.0", "sujets/politique": "6.0", "type/débat": "6.0", "type/interview": "5.0"}, {"catégories/magazine": "7.0", "sujets/actualité": "10.0", "sujets/media et journalisme": "2.5", "sujets/politique": "2.5", "type/débat": "8.5"}, {"catégories/magazine": "4.0", "catégories/programme d'information": "10.0", "envies/se cultiver": "4.0", "sujets/actualité": "2.0", "sujets/politique": "4.0", "type/débat": "4.0"}, {"catégories/magazine": "2.0", "catégories/programme d'information": "10.0", "sujets/actualité": "5.0", "type/débat": "2.0", "type/interview": "2.0"}, {"catégories/magazine": "10.0", "envies/se cultiver": "6.0", "sujets/actualité": "2.0", "sujets/politique": "2.0", "type/débat": "7.0"}, {"catégories/magazine": "2.0", "catégories/programme d'information": "5.0", "envies/se cultiver": "2.0", "sujets/media et journalisme": "5.0", "sujets/politique": "10.0", "type/débat": "5.0"}, {"catégories/magazine": "4.0", "catégories/programme d'information": "10.0", "sujets/actualité": "5.0", "sujets/politique": "6.0", "type/débat": "4.0"}, {"envies/se cultiver": "3.0", "sujets/actualité": "3.0", "sujets/politique": "7.0", "type/débat": "10.0"}, {"envies/se cultiver": "10.0", "sujets/media et journalisme": "4.0", "sujets/politique": "4.0", "type/débat": "6.0", "type/interview": "5.0"}, {"catégories/magazine": "10.0", "catégories/programme d'information": "10.0", "envies/se cultiver": "5.3", "sujets/actualité": "10.0", "sujets/politique": "4.5", "type/débat": "4.3"}, {"catégories/magazine": "4.0", "catégories/programme d'information": "10.0", "sujets/actualité": "2.0", "sujets/politique": "5.0", "type/débat": "4.0"}, {"catégories/programme d'information": "10.0", "envies/se cultiver": "2.6", "sujets/actualité": "2.8", "sujets/media et journalisme": "2.8", "sujets/politique": "6.2", "type/débat": "6.4"}, {"catégories/programme d'information": "10.0", "sujets/actualité": "4.0", "sujets/media et journalisme": "4.0", "sujets/politique": "4.0", "type/débat": "9.0"}, {"envies/se cultiver": "10.0", "sujets/media et journalisme": "3.0", "sujets/politique": "3.0", "type/débat": "7.5"}, {"envies/se cultiver": "10.0", "sujets/actualité": "4.0", "sujets/media et journalisme": "4.0", "type/débat": "4.0", "type/interview": "4.0"}, {"sujets/media et journalisme": "2.0", "sujets/politique": "7.0", "type/débat": "10.0", "type/interview": "7.0"}, {"catégories/programme d'information": "5.5", "sujets/actualité": "6.0", "sujets/politique": "10.0", "type/débat": "5.5", "type/interview": "5.5"}, {"catégories/magazine": "5.0", "envies/se cultiver": "5.0", "sujets/actualité": "10.0", "sujets/politique": "5.0", "type/débat": "4.0", "type/interview": "5.0"}, {"catégories/magazine": "7.8", "envies/se cultiver": "9.0", "sujets/actualité": "7.4", "type/débat": "7.4", "type/interview": "7.4"}, {"sujets/media et journalisme": "7.5", "sujets/politique": "7.7", "type/débat": "7.8"}, {"catégories/programme d'information": "4.0", "envies/se cultiver": "2.0", "sujets/media et journalisme": "2.0", "sujets/politique": "10.0", "type/débat": "4.0"}, {"envies/se cultiver": "6.5", "sujets/politique": "6.9", "type/débat": "7.6"}, {"catégories/programme d'information": "8.0", "envies/se cultiver": "6.0", "sujets/actualité": "2.0", "sujets/politique": "4.0", "type/débat": "10.0"}, {"catégories/magazine": "6.7", "catégories/programme d'information": "5.0", "envies/se cultiver": "10.0", "sujets/actualité": "10.0", "sujets/politique": "5.0", "type/débat": "5.0", "type/interview": "5.0"}, {"catégories/programme d'information": "10.0", "sujets/actualité": "5.0", "sujets/media et journalisme": "10.0", "type/débat": "4.5", "type/interview": "4.5"}, {"catégories/programme d'information": "8.7", "sujets/politique": "8.2", "type/débat": "7.8"}, {"envies/se cultiver": "8.0", "sujets/actualité": "10.0", "sujets/media et journalisme": "4.0", "sujets/politique": "4.0", "type/débat": "5.0"}, {"catégories/magazine": "8.0", "catégories/programme d'information": "5.0", "sujets/media et journalisme": "10.0", "sujets/politique": "5.0", "type/débat": "5.0"}, {"catégories/magazine": "2.0", "envies/se cultiver": "5.0", "sujets/politique": "10.0", "type/débat": "4.0", "type/interview": "4.0"}, {"catégories/magazine": "2.0", "sujets/actualité": "10.0", "sujets/media et journalisme": "3.0", "type/débat": "10.0", "type/interview": "10.0"}, {"catégories/magazine": "7.0", "catégories/programme d'information": "5.0", "envies/se cultiver": "10.0", "sujets/actualité": "5.8", "sujets/politique": "10.0", "type/débat": "5.0", "type/interview": "5.0"}, {"envies/se cultiver": "6.0", "sujets/actualité": "8.8", "type/débat": "6.4", "type/interview": "6.3"}, {"catégories/magazine": "2.8", "catégories/programme d'information": "9.9", "sujets/actualité": "6.2", "sujets/politique": "2.8", "type/débat": "9.9"}, {"catégories/programme d'information": "5.0", "sujets/actualité": "10.0", "sujets/media et journalisme": "10.0", "type/débat": "5.0", "type/interview": "10.0"}, {"catégories/magazine": "5.0", "sujets/actualité": "7.0", "sujets/media et journalisme": "10.0", "type/débat": "7.0", "type/interview": "7.0"}, {"catégories/magazine": "10.0", "catégories/programme d'information": "4.5", "envies/se cultiver": "4.5", "sujets/media et journalisme": "8.5", "sujets/politique": "4.5", "type/débat": "4.5"}, {"catégories/magazine": "2.0", "catégories/programme d'information": "5.0", "sujets/politique": "10.0", "type/débat": "5.0", "type/interview": "5.0"}, {"catégories/magazine": "9.0", "sujets/actualité": "10.0", "sujets/politique": "4.0", "type/débat": "5.0", "type/interview": "9.0"}, {"catégories/magazine": "5.1", "sujets/actualité": "6.1", "type/débat": "5.5", "type/interview": "5.5"}, {"envies/se cultiver": "5.0", "sujets/actualité": "4.0", "sujets/media et journalisme": "4.0", "sujets/politique": "4.0", "type/débat": "5.0"}, {"catégories/magazine": "10.0", "envies/se cultiver": "6.0", "sujets/media et journalisme": "6.0", "sujets/politique": "7.3", "type/débat": "6.3"}, {"catégories/magazine": "5.0", "envies/se cultiver": "9.0", "sujets/actualité": "10.0", "sujets/media et journalisme": "4.0", "sujets/politique": "4.0", "type/débat": "5.0"}, {"catégories/magazine": "7.0", "envies/se cultiver": "10.0", "sujets/actualité": "2.0", "sujets/politique": "5.0", "type/débat": "6.0"}, {"catégories/magazine": "9.0", "catégories/programme d'information": "4.3", "envies/se cultiver": "5.0", "sujets/actualité": "4.3", "sujets/media et journalisme": "5.0", "sujets/politique": "4.3", "type/débat": "4.3"}, {"catégories/programme d'information": "5.1", "envies/se cultiver": "9.9", "sujets/actualité": "5.9", "sujets/politique": "9.9", "type/débat": "5.1", "type/interview": "6.3"}, {"catégories/programme d'information": "9.0", "envies/se cultiver": "9.1", "sujets/actualité": "9.1", "type/débat": "9.0", "type/interview": "9.3"}, {"catégories/magazine": "2.0", "catégories/programme d'information": "10.0", "sujets/actualité": "2.0", "sujets/media et journalisme": "2.0", "sujets/politique": "10.0", "type/débat": "10.0"}, {"envies/se cultiver": "8.0", "type/débat": "6.5", "type/interview": "7.7"}, {"catégories/magazine": "10.0", "catégories/programme d'information": "10.0", "envies/se cultiver": "5.0", "sujets/actualité": "10.0", "sujets/politique": "10.0", "type/débat": "5.0"}, {"catégories/magazine": "10.0", "catégories/programme d'information": "4.0", "envies/se cultiver": "9.0", "sujets/actualité": "7.0", "sujets/politique": "7.0", "type/débat": "4.0", "type/interview": "4.0"}, {"catégories/programme d'information": "10.0", "sujets/actualité": "5.0", "sujets/media et journalisme": "5.0", "sujets/politique": "4.3", "type/débat": "4.7"}], "29": [{"catégories/émissions": "5.2", "sujets/bien-être": "5.0", "sujets/santé": "2.1"}, {"catégories/émissions": "2.0", "sujets/bien-être": "10.0", "sujets/santé": "5.0"}, {"catégories/émissions": "4.0", "sujets/fitness": "2.0", "sujets/santé": "6.0"}, {"sujets/bien-être": "6.3", "sujets/santé": "6.3"}, {"catégories/émissions": "7.2", "sujets/cuisine et gastronomie": "4.3", "sujets/santé": "4.6"}, {"catégories/émissions": "4.0", "sujets/fitness": "2.4", "sujets/santé": "3.1"}, {"catégories/émissions": "4.0", "sujets/fitness": "5.0", "sujets/santé": "5.0"}, {"catégories/émissions": "5.4", "sujets/santé": "5.5"}], "30": [{"sujets/sciences et techniques": "8.1", "type/reportage": "6.3"}, {"sujets/sciences et techniques": "8.9"}], "31": [{"personnages/fantômes": "2.5", "personnages/monstres": "2.5", "sous-genres/science-fiction": "6.5"}, {"personnages/fantômes": "8.7", "sous-genres/science-fiction": "8.7"}, {"personnages/fantômes": "7.0", "personnages/monstres": "5.1", "sous-genres/science-fiction": "5.1"}], "34": [{"catégories/magazine": "3.0", "catégories/programme événementiel": "3.0", "catégories/sport": "10.0", "sujets/football": "2.0"}, {"catégories/documentaire tv": "8.0", "catégories/magazine": "10.0", "catégories/programme événementiel": "8.0", "sujets/football": "4.0", "sujets/sport": "4.0"}, {"catégories/magazine": "7.4", "catégories/programme événementiel": "9.6", "catégories/sport": "7.4", "sujets/football": "5.2", "sujets/sport": "4.3"}, {"catégories/documentaire tv": "7.9", "catégories/magazine": "7.6", "catégories/programme événementiel": "9.5", "catégories/sport": "7.5", "sujets/football": "6.2"}, {"catégories/programme événementiel": "8.5", "sujets/football": "5.4", "sujets/sport": "4.0"}, {"catégories/magazine": "7.7", "catégories/programme événementiel": "9.3", "catégories/sport": "7.7", "sujets/football": "6.6"}, {"catégories/documentaire tv": "10.0", "catégories/programme événementiel": "5.0", "catégories/sport": "5.0", "sujets/football": "4.0"}, {"catégories/programme événementiel": "8.1", "sujets/football": "5.8"}, {"catégories/documentaire tv": "2.0", "catégories/programme événementiel": "10.0", "sujets/football": "7.0"}, {"catégories/magazine": "9.0", "catégories/programme événementiel": "7.5", "sujets/football": "5.2"}, {"catégories/documentaire tv": "6.8", "catégories/magazine": "8.9", "catégories/programme événementiel": "6.7", "sujets/football": "5.1"}, {"catégories/magazine": "9.4", "catégories/programme événementiel": "8.1", "sujets/football": "4.2", "sujets/sport": "3.6"}, {"catégories/documentaire tv": "8.0", "catégories/magazine": "2.0", "catégories/sport": "2.0", "sujets/football": "5.0", "sujets/sport": "5.0"}, {"catégories/documentaire tv": "7.9", "catégories/sport": "9.0", "sujets/football": "4.3"}, {"catégories/documentaire tv": "8.2", "catégories/programme événementiel": "7.4", "sujets/football": "5.4"}, {"catégories/documentaire tv": "8.3", "catégories/sport": "9.3", "sujets/football": "3.5", "sujets/sport": "2.6"}, {"catégories/documentaire tv": "8.0", "catégories/magazine": "8.1", "catégories/programme événementiel": "10.0", "catégories/sport": "8.0", "sujets/football": "5.1", "sujets/sport": "4.6"}, {"catégories/magazine": "8.0", "catégories/sport": "9.4", "sujets/football": "2.9", "sujets/sport": "2.2"}, {"catégories/documentaire tv": "5.0", "catégories/magazine": "10.0", "catégories/programme événementiel": "5.0", "catégories/sport": "10.0", "sujets/football": "4.0"}, {"catégories/documentaire tv": "4.0", "catégories/magazine": "4.0", "catégories/programme événementiel": "10.0", "sujets/football": "7.0"}, {"catégories/documentaire tv": "9.0", "catégories/programme événementiel": "8.1", "sujets/football": "5.2", "sujets/sport": "3.6"}, {"catégories/programme événementiel": "9.9", "catégories/sport": "5.5", "sujets/football": "6.6", "sujets/sport": "4.2"}, {"catégories/magazine": "7.0", "catégories/programme événementiel": "10.0", "catégories/sport": "7.0", "sujets/football": "2.0", "sujets/sport": "2.0"}, {"catégories/documentaire tv": "9.0", "catégories/magazine": "2.5", "catégories/sport": "2.5", "sujets/football": "3.5", "sujets/sport": "3.5"}, {"catégories/documentaire tv": "8.0", "catégories/programme événementiel": "3.0", "sujets/football": "10.0", "sujets/sport": "4.0"}, {"catégories/magazine": "9.3", "catégories/sport": "8.7", "sujets/football": "3.5"}, {"catégories/sport": "9.0", "sujets/football": "3.3", "sujets/sport": "2.4"}, {"catégories/magazine": "5.0", "catégories/programme événementiel": "10.0", "catégories/sport": "10.0", "sujets/football": "5.0", "sujets/sport": "4.0"}, {"catégories/sport": "8.3", "sujets/football": "4.6"}, {"catégories/documentaire tv": "7.2", "catégories/programme événementiel": "10.0", "catégories/sport": "7.2", "sujets/football": "5.1", "sujets/sport": "4.7"}, {"catégories/documentaire tv": "7.8", "catégories/magazine": "6.4", "catégories/sport": "6.4", "sujets/football": "4.4", "sujets/sport": "4.3"}, {"catégories/documentaire tv": "7.3", "catégories/programme événementiel": "9.8", "catégories/sport": "7.2", "sujets/football": "5.9"}, {"catégories/documentaire tv": "7.6", "catégories/magazine": "7.5", "catégories/sport": "7.6", "sujets/football": "4.8"}, {"catégories/programme événementiel": "9.7", "catégories/sport": "7.1", "sujets/football": "6.2"}, {"catégories/documentaire tv": "8.0", "catégories/magazine": "7.0", "catégories/sport": "7.0", "sujets/football": "10.0", "sujets/sport": "4.0"}], "35": [{"genres/aventure": "9.0", "personnages/aventuriers": "4.5", "thèmes/parcours initiatique": "9.0"}, {"envies/montée d'adrénaline": "10.0", "genres/aventure": "10.0", "personnages/aventuriers": "5.0", "thèmes/survie": "10.0"}, {"envies/montée d'adrénaline": "4.1", "genres/aventure": "7.8", "personnages/aventuriers": "7.4", "thèmes/survie": "7.4"}, {"genres/aventure": "8.6", "personnages/aventuriers": "8.5", "thèmes/parcours initiatique": "8.6"}, {"envies/montée d'adrénaline": "7.5", "genres/aventure": "8.5", "personnages/aventuriers": "6.7"}, {"genres/aventure": "3.0", "personnages/aventuriers": "3.0", "sous-genres/peplum": "3.0"}, {"genres/aventure": "9.7", "personnages/aventuriers": "4.8", "sous-genres/cape et d'épée": "9.7"}, {"envies/montée d'adrénaline": "5.0", "genres/aventure": "5.0", "personnages/aventuriers": "5.0", "thèmes/chasse au trésor": "5.0", "thèmes/survie": "5.0"}, {"envies/montée d'adrénaline": "4.0", "genres/aventure": "10.0", "sous-genres/peplum": "5.0", "thèmes/survie": "5.0"}, {"envies/montée d'adrénaline": "3.5", "genres/aventure": "3.5", "sous-genres/peplum": "3.5", "sous-genres/western": "5.5"}, {"envies/montée d'adrénaline": "8.1", "genres/aventure": "7.9", "sous-genres/peplum": "7.7"}, {"genres/aventure": "9.7", "personnages/aventuriers": "4.8", "thèmes/chasse au trésor": "9.7", "thèmes/survie": "9.7"}, {"genres/aventure": "5.0", "personnages/aventuriers": "5.0", "thèmes/chasse au trésor": "5.0", "thèmes/survie": "10.0"}, {"envies/montée d'adrénaline": "4.0", "genres/aventure": "5.0", "sous-genres/peplum": "4.3", "thèmes/survie": "5.3"}, {"genres/aventure": "4.0", "personnages/aventuriers": "4.0", "thèmes/chasse au trésor": "4.0", "thèmes/parcours initiatique": "5.0"}, {"genres/aventure": "10.0", "personnages/aventuriers": "5.0", "thèmes/parcours initiatique": "10.0", "thèmes/survie": "10.0"}, {"envies/montée d'adrénaline": "5.0", "genres/aventure": "10.0", "personnages/aventuriers": "5.0", "sous-genres/western": "5.0"}, {"genres/aventure": "6.0", "personnages/aventuriers": "6.0", "sous-genres/peplum": "10.0"}, {"envies/montée d'adrénaline": "3.3", "genres/aventure": "10.0", "personnages/aventuriers": "5.0", "thèmes/chasse au trésor": "10.0"}, {"genres/aventure": "4.0", "personnages/aventuriers": "4.0", "sous-genres/peplum": "3.0", "thèmes/survie": "4.0"}, {"envies/montée d'adrénaline": "5.7", "genres/aventure": "5.6", "personnages/aventuriers": "5.1", "thèmes/chasse au trésor": "5.7"}, {"envies/montée d'adrénaline": "5.0", "genres/aventure": "10.0", "sous-genres/peplum": "5.0", "thèmes/parcours initiatique": "10.0"}, {"genres/aventure": "3.8", "sous-genres/cape et d'épée": "3.8", "sous-genres/peplum": "5.0"}, {"genres/aventure": "3.0", "sous-genres/peplum": "3.0", "sous-genres/western": "6.0", "thèmes/survie": "6.0"}, {"envies/montée d'adrénaline": "10.0", "genres/aventure": "5.0", "sous-genres/peplum": "5.0", "thèmes/parcours initiatique": "5.0"}, {"genres/aventure": "7.7", "personnages/aventuriers": "7.6", "thèmes/survie": "7.8"}, {"envies/montée d'adrénaline": "8.6", "genres/aventure": "8.6", "personnages/aventuriers": "8.6", "sous-genres/western": "8.6"}, {"envies/montée d'adrénaline": "10.0", "genres/aventure": "5.6", "personnages/aventuriers": "5.6", "thèmes/chasse au trésor": "10.0"}, {"genres/aventure": "6.2", "personnages/aventuriers": "5.7", "thèmes/chasse au trésor": "4.2", "thèmes/survie": "5.7"}, {"genres/aventure": "6.0", "sous-genres/peplum": "6.0", "thèmes/chasse au trésor": "3.0"}, {"genres/aventure": "4.0", "sous-genres/peplum": "4.0", "thèmes/parcours initiatique": "5.0"}, {"genres/aventure": "4.5", "sous-genres/peplum": "4.5", "sous-genres/western": "4.5", "thèmes/survie": "4.5"}, {"genres/aventure": "8.3", "sous-genres/peplum": "8.0", "thèmes/survie": "8.0"}, {"genres/aventure": "10.0", "sous-genres/peplum": "5.0", "thèmes/chasse au trésor": "9.0", "thèmes/survie": "5.0"}, {"genres/aventure": "7.0", "personnages/aventuriers": "6.7", "sous-genres/western": "7.0"}, {"envies/montée d'adrénaline": "3.3", "genres/aventure": "7.0", "sous-genres/peplum": "7.0"}, {"genres/aventure": "10.0", "personnages/aventuriers": "5.0", "thèmes/chasse au trésor": "10.0", "thèmes/survie": "4.7"}, {"envies/montée d'adrénaline": "8.0", "genres/aventure": "8.0", "personnages/aventuriers": "8.0", "sous-genres/western": "8.0", "thèmes/chasse au trésor": "9.0"}, {"envies/montée d'adrénaline": "9.1", "genres/aventure": "9.1", "sous-genres/peplum": "9.1", "thèmes/parcours initiatique": "9.1"}, {"genres/aventure": "6.2", "sous-genres/peplum": "5.7"}, {"genres/aventure": "5.0", "personnages/aventuriers": "5.0", "sous-genres/peplum": "2.0"}, {"envies/montée d'adrénaline": "8.7", "genres/aventure": "8.7", "sous-genres/peplum": "4.7", "thèmes/parcours initiatique": "4.7"}, {"envies/montée d'adrénaline": "9.0", "genres/aventure": "7.5", "personnages/aventuriers": "7.5", "thèmes/parcours initiatique": "7.5"}, {"envies/montée d'adrénaline": "8.0", "genres/aventure": "8.0", "sous-genres/peplum": "8.0", "thèmes/chasse au trésor": "10.0"}, {"envies/montée d'adrénaline": "5.0", "genres/aventure": "10.0", "sous-genres/peplum": "10.0", "thèmes/parcours initiatique": "5.0"}, {"genres/aventure": "4.1", "personnages/aventuriers": "5.1", "sous-genres/western": "4.9", "thèmes/chasse au trésor": "5.1"}, {"genres/aventure": "6.2", "personnages/aventuriers": "6.2", "sous-genres/western": "5.2", "thèmes/survie": "6.8"}, {"genres/aventure": "10.0", "personnages/aventuriers": "6.0", "thèmes/parcours initiatique": "8.0", "thèmes/survie": "6.0"}, {"genres/aventure": "5.3", "sous-genres/peplum": "5.2", "thèmes/chasse au trésor": "8.2"}, {"genres/aventure": "3.8", "sous-genres/peplum": "3.8", "sous-genres/western": "5.3"}, {"genres/aventure": "3.0", "personnages/aventuriers": "3.0", "sous-genres/western": "6.0", "thèmes/chasse au trésor": "3.0"}, {"envies/montée d'adrénaline": "3.5", "genres/aventure": "3.5", "sous-genres/peplum": "6.5"}, {"genres/aventure": "6.0", "sous-genres/peplum": "3.3", "thèmes/parcours initiatique": "4.8"}, {"genres/aventure": "7.2", "personnages/aventuriers": "6.6"}, {"envies/montée d'adrénaline": "4.0", "genres/aventure": "10.0", "personnages/aventuriers": "5.0", "thèmes/chasse au trésor": "4.0"}, {"genres/aventure": "8.1", "personnages/aventuriers": "8.1", "thèmes/parcours initiatique": "7.1", "thèmes/survie": "9.0"}, {"genres/aventure": "3.0", "personnages/aventuriers": "3.0", "thèmes/chasse au trésor": "5.0", "thèmes/survie": "5.0"}, {"genres/aventure": "9.4", "personnages/aventuriers": "5.0", "thèmes/chasse au trésor": "9.4"}, {"envies/montée d'adrénaline": "9.0", "genres/aventure": "5.0", "personnages/aventuriers": "5.0", "sous-genres/western": "9.0"}], "40": [{"temps/anticipation": "4.8", "temps/futur": "4.8"}], "41": [{"catégories/documentaire tv": "6.6", "genres/documentaire": "7.9", "thèmes/découverte": "8.0"}, {"catégories/documentaire tv": "8.7", "genres/documentaire": "5.7"}, {"catégories/documentaire tv": "6.6", "thèmes/découverte": "7.8"}]}