{"1": {"name": "série", "includes": [35, 20, 21, 6, 23, 8, 9, 10, 26, 27, 13, 31], "score": {"taxon/fake1": "4.0", "type/film": "-20.0", "lieu de l'action/Languedoc-Roussillon": "1.0", "lieu de l'action/Provence-Alpes-Côte d'Azur": "1.0", "personnages/ange": "1.0", "catégories/fiction": "4.0", "type/série": "6.0"}}, "2": {"name": "info-addict", "includes": [], "score": {"catégories/programme d'information": "4.0", "sujets/société": "4.0", "catégories/magazine": "4.0", "type/reportage": "2.0", "type/talk-show": "2.0", "thèmes/nature et vie sauvage": "-20.0", "sujets/télé-réalité": "-20.0"}}, "3": {"name": "sujet de société", "includes": [], "score": {"sujets/société": "4.0", "catégories/magazine": "4.0", "catégories/documentaire tv": "4.0", "catégories/programme d'information": "3.0", "catégories/fiction": "2.0", "catégories/émissions": "2.0", "sujets/actualité": "1.0", "type/adaptation d'une histoire vraie": "-4.0", "type/télé-réalité": "-4.0", "type/film": "-400.0"}}, "4": {"name": "fan de sport", "includes": [34, 36, 37], "score": {"catégories/sport": "4.0", "sujets/sport": "4.0", "catégories/programme événementiel": "3.0", "catégories/magazine": "2.0", "catégories/documentaire tv": "2.0", "sujets/pétanque": "1.0", "actions/relaxation": "-4.0", "sujets/fitness": "-4.0"}}, "5": {"name": "fan d'animation", "includes": [], "score": {"genres/animation": "9.0", "type/plébiscité par la critique": "5.0", "audience/en famille": "3.0", "sous-genres/anime & manga": "2.0", "type/blockbuster": "2.0", "origine/Pixar studio": "2.0", "origine/Dreamworks studio": "2.0", "type/série": "-100.0", "type/jeu télévisé": "-100.0", "origine/Disney studio": "-4.0"}}, "6": {"name": "kids", "includes": [], "score": {"catégories/programme jeunesse": "6.0", "taxon/fake1": "6.0", "taxon/fake2": "6.0", "type/plébiscité par le public": "5.0", "type/culte": "4.0", "genres/dessin animé": "3.0", "genres/animation": "3.0", "sous-genres/anime & manga": "-100.0", "ambiance et ton/humoristique": "1.0"}}, "7": {"name": "paroles et musique", "includes": [], "score": {"catégories/programme musical": "6.0", "sujets/musique": "6.0", "taxon/fake": "6.0", "type/concert": "3.0", "type/clip": "3.0", "catégories/documentaire tv": "3.0", "catégories/magazine": "3.0"}}, "8": {"name": "super-héros", "includes": [], "score": {"personnages/super-héros": "4.0", "genres/action": "2.0", "sous-genres/fantastique": "2.0", "sous-genres/science-fiction": "2.0", "personnages/super-pouvoir": "2.0", "personnages/méchant d'anthologie": "2.0", "envies/montée d'adrénaline": " 2.0", "catégories/fiction": "1.0", "ambiance et ton/horreur": "-4.0", "personnages/DC comics": "1.0"}}, "9": {"name": "rire et comédie", "includes": [13], "score": {"ambiance et ton/humoristique": "3.0", "envies/éclats de rire": "3.0", "genres/comédie": "4.0", "catégories/émissions": "-2.0", "genres/thriller": "-2.0", "genres/action": "-2.0", "genres/aventure": "-2.0"}}, "10": {"name": "suspense et frisson", "includes": [], "score": {"ambiance et ton/suspense": "9.0", "genres/thriller": "6.0", "sous-genres/policier": "4.0", "personnages/monstres": "-2.0", "sous-genres/science-fiction": "-4.0", "sous-genres/fantastique": "-4.0", "ambiance et ton/angoissant": "1.0"}}, "11": {"name": "horizons lointains", "includes": [], "score": {"thèmes/aux 4 coins du monde": "7.0", "catégories/documentaire tv": "5.0", "catégories/magazine": "6.0", "sujets/voyage et évasion": "5.0", "type/reportage": "6.0"}}, "12": {"name": "nature et vie sauvage", "includes": [], "score": {"thèmes/nature et vie sauvage": "4.0", "catégories/documentaire tv": "4.0", "taxon/fake": "4.0", "catégories/magazine": "3.0", "personnages/animaux": "3.0", "lieu de l'action/espace naturel": "3.0", "sujets/environnement": "3.0", "sujets/jeux vidéo": "-4.0", "catégories/fiction": "-4.0"}}, "14": {"name": "jeu tv", "includes": [], "score": {"type/jeu télévisé": "6.0", "type/télé-réalité": "3.0", "thèmes/énigmes et jeu de piste": "4.0", "audience/en famille": "3.0", "catégories/fiction": "-4.0"}}, "15": {"name": "film", "includes": [35, 20, 21, 6, 23, 8, 9, 10, 26, 27, 13, 31], "score": {"type/film": "6.0", "type/plébiscité par le public": "2.0", "type/plébiscité par la critique": "1.0", "catégories/fiction": "4.0", "type/grand classique": "-400.0", "format/court métrage": "-300.0"}}, "16": {"name": "Curiosité et soif de connaissances", "includes": [], "score": {"sujets/sciences et techniques": "3.0", "sujets/art et culture": "3.0", "catégories/magazine": "3.0", "sujets/voyage et évasion": "2.0", "thèmes/découverte": "2.0", "catégories/documentaire tv": "2.0", "type/reportage": "2.0", "thèmes/nature et vie sauvage": "-2.0", "sujets/histoire": "-2.0", "sujets/cuisine et gastronomie": "-2.0", "sujets/télé-achat": "-1000.0", "type/jeu télévisé": "-1000.0", "sujets/sport": "-1000.0"}}, "18": {"name": "art et culture", "includes": [], "score": {"envies/se cultiver": "5.0", "catégories/magazine": "1.0", "catégories/émissions": "1.0", "sujets/art et culture": "5.0", "sujets/littérature": "3.0", "sujets/beaux-arts": "3.0", "sujets/cinéma": "3.0", "type/film": "-2.0"}}, "20": {"name": "Vengeance et auto-justice", "includes": [], "score": {"thèmes/vengeance et auto-justice": "1.0"}}, "21": {"name": "Amour et séduction", "includes": [], "score": {"genres/comédie romantique": "6.0", "thèmes/amour et séduction": "4.0", "ambiance et ton/sentimental": "4.0", "envies/soirées entre filles": "2.0", "sujets/relation de couple": "2.0", "catégories/fiction": "2.0", "avertissements/scènes pouvant heurter la sensibilité du public": "-20.0", "avertissements/scènes de nature sexuelle": "-20.0", "catégories/documentaire tv": "-20.0", "catégories/magazine": "-20.0", "type/bide": "-20.0"}}, "22": {"name": "divertissement", "includes": [], "score": {"catégories/divertissement": "3.0", "type/télé-réalité": "2.0", "type/jeu télévisé": "2.0", "lieu de l'action/Poitou-Charentes": "2.0", "sujets/jeux vidéo": "2.0", "sujets/mariage": "1.0", "lieu de tournage/Maroc": "1.0", "catégories/programme d'information": "-8.0", "catégories/magazine": "-8.0"}}, "23": {"name": "gros bras", "includes": [], "score": {"catégories/fiction": "2.0", "genres/action": "4.0", "envies/montée d'adrénaline": "3.0", "actions/combat": "2.0", "thèmes/seul contre tous": "2.0", "sujets/arts martiaux": "2.0", "sous-genres/super-héros": "-1.0", "ambiance et ton/humoristique": "-1.0", "sous-genres/fantastique": "-1.0", "sous-genres/science-fiction": "-1.0"}}, "24": {"name": "histoire", "includes": [], "score": {"sujets/histoire": "1.0", "genres/historique": "1.0"}}, "25": {"name": "télé-réalité", "includes": [], "score": {"catégories/divertissement": "3.0", "type/télé-réalité": "6.0", "taxon/fake": "6.0", "personnages/idiot": "2.0", "personnages/jolie fille": "2.0", "type/jeu télévisé": "-2.0", "catégories/fiction": "-2.0"}}, "26": {"name": "science-fiction", "includes": [], "score": {"temps/anticipation": "2.0", "sous-genres/science-fiction": "1.0"}}, "27": {"name": "drame et tragédie", "includes": [], "score": {"genres/drame": "2.0", "genres/mélodrame": "2.0", "thèmes/mal de vivre": "2.0", "envies/à vos mouchoirs": "1.0", "ambiance et ton/émouvant": "1.0", "ambiance et ton/triste": "1.0", "type/happy end": "-8.0", "genres/comédie dramatique": "-4.0"}}, "28": {"name": "dé<PERSON> d'idée", "includes": [], "score": {"type/débat": "6.0", "type/interview": "4.0", "sujets/politique": "3.0", "catégories/programme d'information": "2.0", "catégories/magazine": "2.0", "sujets/actualité": "2.0", "sujets/media et journalisme": "2.0", "envies/se cultiver": "1.0", "catégories/fiction": "-4.0"}}, "29": {"name": "médecine", "includes": [], "score": {"catégories/émissions": "6.0", "sujets/santé": "6.0", "sujets/bien-être": "6.0", "sujets/cuisine et gastronomie": "4.0", "sujets/fitness": "4.0"}}, "30": {"name": "Sciences et techniques", "includes": [], "score": {"sujets/sciences et techniques": "2.0", "type/reportage": "1.0"}}, "31": {"name": "Monstres et créatures", "includes": [], "score": {"personnages/fantômes": "1.0", "sous-genres/science-fiction": "1.0", "personnages/vampires": "1.0", "personnages/monstres": "1.0"}}, "34": {"name": "Football", "includes": [], "score": {"sujets/football": "6.0", "catégories/programme événementiel": "5.0", "catégories/sport": "4.0", "catégories/magazine": "2.0", "catégories/documentaire tv": "2.0", "sujets/sport": "2.0"}}, "35": {"name": "Aventure", "includes": [], "score": {"genres/aventure": "6.0", "personnages/aventuriers": "4.0", "sous-genres/peplum": "3.0", "sous-genres/western": "2.0", "sous-genres/cape et d'épée": "2.0", "catégories/contes et légendes": "2.0", "thèmes/chasse au trésor": "2.0", "thèmes/survie": "1.0", "envies/montée d'adrénaline": "1.0", "thèmes/parcours initiatique": "1.0", "sujets/sport": "-4.0", "type/jeu télévisé": "-2.0", "sous-genres/science-fiction": "-2.0"}}, "40": {"name": "Anticipation", "includes": [], "score": {"temps/futur": "5.0", "temps/anticipation": "5.0", "sujets/contre-utopie": "1.0", "sujets/sciences et techniques": "1.0", "sujets/Technologies de l'information et de la communication": "1.0", "sujets/Intelligence artificielle": "1.0"}}, "41": {"name": "Documentaires", "includes": [], "score": {"catégories/documentaire tv": "2.0", "genres/documentaire": "1.0", "thèmes/découverte": "2.0", "personnages/anticonformiste": "1.0", "type/débat": "-100.0", "type/télé-réalité": "-100.0", "sujets/sport": "-1000.0"}}}