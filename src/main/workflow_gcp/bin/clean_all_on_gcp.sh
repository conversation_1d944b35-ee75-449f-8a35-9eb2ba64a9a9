#! /usr/bin/bash

START_DATE=`date +"%Y-%m-%d %H:00:00" --date="- 3hour"`

gcp_dags_destination="$DAG_COMPOSER_URI/dags/${PROJECT_GROUP}/${WORKFLOW_NAME}_${BRANCH_NAME}"
gcp_artifacts_destination="gs://$ARTIFACT_BUCKET_NAME/composer2/${WORKFLOW_NAME}/${BRANCH_NAME}"

echo $gcp_dags_destination
echo $gcp_artifacts_destination

gsutil -m rm "${gcp_artifacts_destination}/libs/*" || true
gsutil -m rm "${gcp_artifacts_destination}/conf/*" || true
gsutil -m rm "${gcp_artifacts_destination}/scripts/*" || true
gsutil -m rm "${gcp_artifacts_destination}/data/*" || true
gsutil -m rm -r "${gcp_dags_destination}/*" || true
