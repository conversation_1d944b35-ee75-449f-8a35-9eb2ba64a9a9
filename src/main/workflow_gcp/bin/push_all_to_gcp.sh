#! /usr/bin/bash


START_DATE=`date +"%Y-%m-%d %H:00:00" --date="- 1day"`
INSTALL_DATE=`date +"%Y-%m-%d %H:%M:%S"`


untar_and_install(){
  bundle_name=$1
  gcp_dir=$2
  untardir=/tmp/untarArtifacts/$bundle_name
  mkdir -p $untardir
  tar -xf target/workflowUte$PROJECT_NAME-$VERSION-$bundle_name-${BUILD_ENV}.tar.gz -C $untardir
  perl -p -i -e "s/START_DATE_TO_REPLACE/${START_DATE}/g" $untardir/*
  perl -p -i -e "s/INSTALL_DATE_TO_REPLACE/${INSTALL_DATE}/g" $untardir/*
  perl -p -i -e "s/BRANCH_NAME_TO_REPLACE/${BRANCH_NAME}/g" $untardir/*
  perl -p -i -e "s/DAG_ID_TO_REPLACE/${WORKFLOW_NAME}-${BRANCH_NAME}/g" $untardir/*
  gsutil -m cp -r $untardir/* $gcp_dir
  rm -rf $untardir
}

gcp_dags_destination="$DAG_COMPOSER_URI/dags/${PROJECT_GROUP}/${WORKFLOW_NAME}_${BRANCH_NAME}"
gcp_artifacts_destination="gs://$ARTIFACT_BUCKET_NAME/composer2/${WORKFLOW_NAME}/${BRANCH_NAME}"



untar_and_install gcp-libs "${gcp_artifacts_destination}/libs/"
untar_and_install gcp-configs "${gcp_artifacts_destination}/conf/"
untar_and_install gcp-scripts "${gcp_artifacts_destination}/scripts/"
untar_and_install gcp-data "${gcp_artifacts_destination}/data/"
untar_and_install gcp-dags "${gcp_dags_destination}/"
