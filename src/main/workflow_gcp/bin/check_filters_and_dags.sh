#!/bin/bash

function get_regex_matches() {
  file="$1"
  regex="$2"
  cat $file | grep -v EXTERNAL_DEPEND \
    | perl -ne 'my @matches = $_ =~ /'$regex'/g ; print "$_\n" for @matches;' | sort -u
}

echo "Checking json syntax for files in workflow_gcp/configs"
for file in src/main/workflow_gcp/configs/*.json; do
 echo "file: $file"
 cat  $file | jq empty
 if [ ! "$?" == 0 ]; then
   echo "$file: Bad json syntax"
   exit 1
 else
   echo "OK"
 fi
done

echo "Checking dags python syntax for files in workflow_gcp/dags"
version=$(python3 -V | cut -d ' ' -f 2 | cut -d '.' -f 1)
major=$(python3 -V | cut -d ' ' -f 2 | cut -d '.' -f 2)
if [ ${version} -eq 3 ] && [ ${major} -ge 8 ]; then
  for file in src/main/workflow_gcp/dags/*.py; do
    echo "dag: $file"
    python3 -m py_compile $file
    if [ ! "$?" == 0 ]; then
     echo "$file: Bad python syntax"
     exit 1
   else
     echo "OK"
   fi
  done
else
  echo "WARNING: python version must be equal or greater than 3.8 to check the syntax of the dag file!"
fi

echo "Checking variables substitutions in json"
for file in src/main/workflow_gcp/configs/*.json; do
  echo "file: $file"
  for variable in `cat  $file | perl -ne 'if(/@(\S+?)@/){print "$1\n"}' | sort -u`; do
    grep $variable src/main/filters/filter-gcp-dev.properties > /dev/null
    if [ ! "$?" == 0 ]; then
      echo "Variable $variable used in config file $file not found in filter!"
      exit 1
    fi
  done
done
echo "Ok"

echo "Checking variables substitutions in dag"
for file in src/main/workflow_gcp/dags/*.py; do
  echo "dag: $file"
  #line EXTERNAL_DEPENDENCIES_VERSIONS is not checked because jar variables are not remplaced by variables defined in jar files
  for variable in `cat  $file |grep -v EXTERNAL_DEPENDEN  | perl -ne 'if(/@(\S+?)@/){print "$1\n"}' | sort -u`; do
    grep $variable src/main/filters/filter-gcp-dev.properties > /dev/null
    if [ ! "$?" == 0 ]; then
      echo "Variable $variable| used in dag file $file not found in filter!"
      exit 1
    fi
  done
done
echo "Ok"

echo "Checking &{} substitutions in configs json files"
all_pre_init_config_keys=$(cat src/main/workflow_gcp/configs/config_*.json | jq -c -r 'keys')
for file in src/main/workflow_gcp/configs/config_*.json; do
  echo "file: $file"
  already_seen_variable=""
  for variable in $(get_regex_matches "$file" '&\{(\S+?)\}'); do
    result=$(echo "$all_pre_init_config_keys" | grep '"'$variable'"')
    if [ -z "$result" ]; then
      echo "ERROR: variable used in $file not found in config_default : $variable"
      exit 1
    fi
  done
done

echo "Checking pre_init[] substitutions in dag and dags_params"
echo "  (non bloquant : il faut verifier si les variables manquantes sont definies dans le extra_data_dic du pre_init) "
all_pre_init_config_keys=$(cat src/main/workflow_gcp/configs/config_*.json | jq -c -r 'keys')
dag_helper_standard_key=",processDate,processTime,yesterdayDate,dataprocClusterName,"
for file in src/main/workflow_gcp/dags/*.py src/main/workflow_gcp/configs/*params.json; do
  echo "file: $file"
  already_seen_variable=""
  for variable in $(get_regex_matches "$file" 'pre_init\[(\S+?)\]'); do
    result=$(echo "$all_pre_init_config_keys" | grep '"'$variable'"')
    if [ -z "$result" ]; then
      in_dag_helper=""
      res_dag_helper=$(echo "$dag_helper_standard_key" | grep ",$variable,")
      if [ -n "$res_dag_helper" ]; then
        in_dag_helper=" (standard dag_helper)"
      fi
      echo "WARNING: pre_init Variable used in $file not found in config_default : $variable $in_dag_helper"
    fi
  done
done

echo "Checking job_name in dag.py are in dag_params.json"
echo "  (non bloquant : certains job_names peuvent ne pas nécessiter de configuration (pre_init, delete_dataproc_cluster and check_dag_run are ignored)"
for dag_full_file in $(ls src/main/workflow_gcp/dags/dag_*.py | grep -v 'dag_config.py'); do
  dag_file=$(basename $dag_full_file)
  dag_params_file=$(echo $dag_file | sed -e 's/\.py$/_params.json/')
  echo " - Check job_name in $dag_file are present in $dag_params_file"
  tmpfile="tmp_${dag_params_file}"
  jq "keys" src/main/workflow_gcp/configs/$dag_params_file > $tmpfile
  echo '"pre_init"' >> $tmpfile
  echo '"delete_dataproc_cluster"' >> $tmpfile
  echo '"check_dag_run"' >> $tmpfile
  while read job_name; do
    match=$(cat $tmpfile | grep '"'"$job_name"'"')
    if [ -z "$match" ]; then
      echo "    + job_name in $dag_file, not found in $dag_params_file : $job_name"
    fi
  done < <(grep 'job_name=' $dag_full_file | cut -d '=' -f 2 | sed -e 's/"//g' | sed -e "s/'//g" | sed -e 's/,//g')
  rm -f "${tmpfile:?}" || true
done

echo "Check flake8 on src/main/workflow_gcp/dags"
flake8 src/main/workflow_gcp/dags
if [ $? -ne 0 ]; then
  echo "Problème de syntaxe ou de style python"
fi
echo "Ok"
