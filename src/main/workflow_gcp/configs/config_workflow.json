{"progbymac": {"description": "Progbymac : consommations live", "value": "&{wfInputs}/progbymac"}, "vod": {"description": "vod : consommations on demand (vod, replay, ...)", "value": "&{wfInputs}/vod"}, "filtered": {"description": "Sortie du filter and timeslot : consommations aggrégées de programmes par timeslot", "value": "&{wfOutputs}/ravenne/filtered"}, "filteredTransform": {"description": "Sortie du filter and timeslot : consommations aggrégées de programmes par timeslot", "value": "&{wfOutputs}/ravenne/filteredTransform"}, "ponderation": {"description": "Sortie du ponderation : aggregation des concepts et channel/provider par timeboxes", "value": "&{wfOutputs}/ravenne/ponderation"}, "countconcepts": {"description": "Sortie du calcul des statistiques par concepts (mode écriture)", "value": "&{wfOutputs}/ravenne/countconcepts"}, "countconceptsIn": {"description": "Sortie du calcul des statistiques par concepts (mode lecture)", "value": "&{wfInputs}/ravenne/countconcepts"}, "countbytimeslot": {"description": "Sortie du calcul des statistiques par timeslot", "value": "&{wfOutputs}/ravenne/countbytimeslot"}, "countbyprovider": {"description": "Sortie du calcul des statistiques par provider", "value": "&{wfOutputs}/ravenne/countbyprovider"}, "genetic": {"description": "Sortie des profils génétiques (mode écriture)", "value": "&{wfOutputs}/ravenne/genetic"}, "geneticOtt": {"description": "Sortie des profils génétiques OTT", "value": "&{wfOutputs}/ravenne/genetic_ott"}, "geneticIn": {"description": "Sortie des profils génétiques (mode lecture)", "value": "&{wfInputs}/ravenne/genetic"}, "pnscrocodilebucketPath": {"description": "Bucket PNS pour transfert des profils génétiques", "value": "gs://@pnscrocodilebucket@"}, "tastebox": {"description": "Sortie des tastebox pour les profils types : calcul des vecteurs de consommation", "value": "&{wfOutputs}/ravenne/tastebox"}, "tmpProfilType": {"description": "Sortie temporaire du calcul des profils types.", "value": "&{tmpOutput}/ravenne/profiltype"}, "tmpVerticalScore": {"description": "Sortie temporaire du calcul du score (ratio) pour chaque verticale (thématique).", "value": "&{tmpOutput}/ravenne/verticales"}, "scoring": {"description": "Répertoire de base du scoring (mode écriture)", "value": "&{wfOutputs}/scoring"}, "scoringIn": {"description": "Répertoire de base du scoring (mode lecture)", "value": "&{wfInputs}/scoring"}, "ravenneStatus": {"description": "Répertoire contenant le status du calcul des profils type", "value": "&{wfOutputs}/ravenne/status"}, "acceptedRejectedConcepts": {"description": "Contient la liste des concepts acceptés ou à exclure", "value": "&{workflowDataPath}/catalogue_concepts"}, "catalogProfils": {"description": "Contient la liste des profils types", "value": "&{workflowDataPath}/catalog/catalog_profils.csv"}, "catalogConceptFilterMappingTable": {"description": "Filtre de concepts", "value": "&{workflowDataPath}/catalog/conceptFilterMappingTable"}, "catalogProfilsVectors": {"description": "Vecteurs types pour le calcul des profils types", "value": "&{workflowDataPath}/catalog/catalog_profils_vector.json"}, "catalogCombiCoeurs": {"description": "Score de lignes thématiques Meta4U", "value": "&{workflowDataPath}/catalog/scoreLignesThematiques.json"}, "catalogConceptFamily": {"description": "Familles de concepts", "value": "&{workflowDataPath}/catalog/conceptfamily.csv"}, "filterLimit": {"description": "Nombre de semaines recalculées pour le filterandtimeslot", "value": "2"}, "topConcepts": {"description": "Nombre maximum de concepts pris en compte dans la ponderation", "value": "20"}, "topLiveChannels": {"description": "Nombre maximum de chaines live pris en compte dans la ponderation", "value": "5"}, "topOdProviders": {"description": "Nombre maximum de providers on demand pris en compte dans la ponderation", "value": "5"}, "deltaThreshold": {"description": "<PERSON><PERSON><PERSON> d<PERSON>nchant l'envoi des informations vers Kibana.", "value": "10"}, "genecticCounterDiffThreshold": {"description": "Seuils de variation du nombre de profils génétiques en dessous duquel on déclenche une alerte", "value": "-15"}, "typeCounterDiffThreshold": {"description": "Seuils de variation du nombre de profils types en dessous duquel on déclenche une alerte", "value": "-5"}, "markersProfilesIn": {"description": "Repertoire de stockage des marqueurs calcules a partir des profils genetiques", "value": "&{wfInputs}/markersprofiles"}, "markersProfilesOut": {"description": "Repertoire de stockage des marqueurs calcules a partir des profils genetiques", "value": "&{wfOutputs}/markersprofiles"}, "catalogOeuvresIn": {"description": "Catalogue oeuvres concepts", "value": "&{wfInputs}/entertainment/in"}, "aid_iseh_dataset": {"description": "", "value": "pns__keyring_prod__cred__views__&{environmentForInputs}"}, "aid_iseh_table": {"description": "", "value": "RAW_FACT_cred_isehash_ise_aid_v3_T"}, "profilingtv_project_id": {"description": "", "value": "ofr-pfg-profilingtv-&{environment}"}, "optin_dataset": {"description": "dataset contenant le fichier d'optin", "value": "optinoptout"}, "optinott_table": {"description": "Table contenant le fichier d'optin ott", "value": "optin_ott"}, "optincalculation_table": {"description": "Table contenant le fichier d'optin", "value": "optincalculation_latest"}, "bigqueryPnsKeyringProject": {"description": "", "value": "@bigqueryPnsKeyringProject@"}}