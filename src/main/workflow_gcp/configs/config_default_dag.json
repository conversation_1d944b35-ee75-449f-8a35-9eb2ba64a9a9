{"environment": {"description": "Environnement d'exécution", "value": "@environment@", "properties": ["nomodification"]}, "environmentForInputs": {"description": "Environnement d'exécution en lecture", "value": "@environmentForInputs@", "properties": ["nomodification"]}, "dagGroup": {"description": "Nom du groupe de dags", "value": "@dagGroup@", "properties": ["nomodification"]}, "workflowName": {"description": "Nom du workflow", "value": "@workflowName@", "properties": ["nomodification"]}, "workflowVersion": {"description": "Version du workflow", "value": "@workflowVersion@", "properties": ["nomodification"]}, "branchName": {"description": "Nom de la branche git", "value": "@branchName@", "properties": ["nomodification"]}, "gcpServiceAccount": {"description": "GCP Service Account used by profling treatment on dataproc", "value": "@gcpServiceAccount@"}, "gcpRegionId": {"description": "GCP region id", "value": "europe-west1"}, "gcpZoneId": {"description": "GCP zone id", "value": "europe-west1-b"}, "gcpProjectId": {"description": "GCP Profiling Project Id", "value": "@gcpProjectId@"}, "refStoreProjectId": {"description": "GCP Ref Store Project Id", "value": "@refStoreProjectId@"}, "profilingMailAlert": {"description": "Mail d'alerting", "value": "@profilingMailAlert@"}, "emailFrom": {"description": "Emetteur des mails de stats", "value": "@emailFrom@"}, "schedule": {"description": "If false, schedule information are erased in dag", "value": "@schedule@"}, "allowedToFail": {"description": "List of actions allowed to fail without failing the dag", "value": "delete_dataproc_cluster"}, "disableAfterRun": {"description": "Force to pause the dag after current dag run if value is 1", "value": "@disableAfterRun@"}, "visionnageRavenneDataset": {"description": "Dataset", "value": "dashboards_bi"}, "refStoreVisionnageRavenneDataset": {"description": "Dataset in refstore", "value": "ofr_0np_profiling_qd"}, "visionnageRavenneRawTable": {"description": "raw data", "value": "rawData_visionnages_ravenne"}, "visionnageRavenneTable": {"description": "table ", "value": "visionnages_ravenne"}}