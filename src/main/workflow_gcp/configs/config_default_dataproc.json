{"gcpSubnetworkId": {"description": "GCP Sub Network Id", "value": "projects/&{gcpProjectId}/regions/&{gcpRegionId}/subnetworks/&{gcpProjectId}-dataproc"}, "imageVersion": {"description": "Version of dataproc image", "value": "2.0"}, "sparkHistoryBucket": {"description": "Bucket dans lequel est placé l'historique spark après traitement pour investigation", "value": "gs://orange-arti-common-logs-spark-hs-@legacy_env@/spark2-history/"}, "idleDeleteTtl": {"description": "Dataproc cluster configuration : temps d'inactivité avant suppression en secondes", "value": "@idleDeleteTtl@"}, "autoDeleteTtl": {"description": "Dataproc cluster configuration : temps maximum de vie du cluster en secondes", "value": "@autoDeleteTtl@"}, "dataprocMasterType": {"description": "Dataproc cluster configuration : type de machine du master/driver", "value": "@masterType@"}, "dataprocWorkerType": {"description": "Dataproc cluster configuration : type de machine des workers", "value": "@workerType@"}, "numWorkers": {"description": "Dataproc cluster configuration : nombre de workers", "value": "@numWorkers@"}, "numPreemptibleWorkers": {"description": "Dataproc cluster configuration : nombre de workers preemptibles", "value": "@numPreemptibleWorkers@"}}