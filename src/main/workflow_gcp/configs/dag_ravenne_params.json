{"input_files_check": {"action_type": "input_files_check", "input_uri_paths": ["pre_init[progbymac]", "pre_init[optinPath]/LATEST/optincalculation-r-00000", "pre_init[geneticIn]/pre_init[oneWeekAgoDate]/fullAnonymized/_COUNTERS", "pre_init[acceptedRejectedConcepts]/acceptedExcludedConcepts.txt", "pre_init[catalogConceptFilterMappingTable]"]}, "create_dataproc_cluster": {"action_type": "dataproc", "action_data_properties": ["pre_init"], "imageVersion": "pre_init[imageVersion]", "idleDeleteTtl": "pre_init[idleDeleteTtl]", "autoDeleteTtl": "pre_init[autoDeleteTtl]", "dataprocMasterType": "pre_init[dataprocMasterType]", "dataprocWorkerType": "pre_init[dataprocWorkerType]", "numWorkers": "pre_init[numWorkers]", "numPreemptibleWorkers": "pre_init[numPreemptibleWorkers]", "initActionsUris": ["pre_init[workflowScriptsPath]/init_actions.sh"]}, "init": {"action_type": "hadoop", "main_class": "com.orange.profiling.ute.workflow.ravenne.Init", "args": ["--paramDate", "pre_init[processDate]", "--filterLimit", "pre_init[filterLimit]", "--gdpr<PERSON><PERSON><PERSON>ilePath", "pre_init[gdprFlagFile]", "--gcsPropertiesFile", "pre_init[workflowPropertiesFullPath]/init.properties"], "capture_output": true}, "bigquery-export-mappingOtt-to-gcs": {"action_type": "bq_to_gcs", "kwargs": {"bucket": "pre_init[bucketArtifacts]", "query_file_path": "pre_init[workflowRelScriptsPath]/query_export_mapping_ott.sql", "pns_project_id": "pre_init[bigqueryPnsKeyringProject]", "aid_iseh_dataset": "pre_init[aid_iseh_dataset]", "aid_iseh_table": "pre_init[aid_iseh_table]", "profilingtv_project_id": "pre_init[profilingtv_project_id]", "optin_dataset": "pre_init[optin_dataset]", "optinott_table": "pre_init[optinott_table]", "optincalculation_table": "pre_init[optincalculation_table]", "uri": "pre_init[tmpOutput]/pre_init[workflowName]/input/pre_init[processDate]/mappingOtt/part-*.csv"}}, "copy-mapping-ott-files": {"action_type": "bash", "bash_commands_def": [{"command": "gsutil cp", "args": ["pre_init[workflowScriptsPath]/copy-mapping-ott.sh", "."]}, {"command": "bash copy-mapping-ott.sh", "args": ["pre_init[tmpInput]/pre_init[workflowName]/input/pre_init[processDate]/mappingOtt/part-*", "pre_init[tmpOutput]/pre_init[workflowName]/input/pre_init[processDate]/mappingOttCombined/part-00000"]}], "capture_output": false}, "filterAndTimeslot": {"action_type": "hadoop", "main_class": "com.orange.profiling.ute.ravenne.filterandtimeslot.MainFilterAndTimeslot", "args": ["pre_init[progbymac]/*", "pre_init[vod]/*", "init[filterBeginYearAndWeek]", "init[endYearAndWeek]", "pre_init[filtered]"]}, "transformProfilingHashAddDomain": {"action_type": "hadoop", "main_class": "com.orange.profiling.ute.ravenne.transformer.MainTransformProfilingHash", "args": ["pre_init[filtered]/*/*/selected/*", "pre_init[tmpInput]/pre_init[workflowName]/input/pre_init[processDate]/mappingOttCombined/part-00000", "init[filterBeginYearAndWeek]", "init[endYearAndWeek]", "pre_init[filteredTransform]"]}, "ponderation-concepts": {"action_type": "hadoop", "properties": {"fs.gs.outputstream.upload.chunk.size": "16777216"}, "main_class": "com.orange.profiling.ute.ravenne.ponderation.MainPonderationConcepts", "args": ["pre_init[filteredTransform]/*/*/selected/*", "pre_init[processDate]", "init[beginYearAndWeek]", "init[endYearAndWeek]", "com.orange.profiling.ute.ravenne.util.DefaultPredicate", "pre_init[acceptedRejectedConcepts]/acceptedExcludedConcepts.txt", "pre_init[catalogConceptFilterMappingTable]", "pre_init[optinPath]/LATEST/optincalculation-r-00000", "init[ravenne.gdpr.pseudoanonymisation.enable]", "pre_init[ponderation]/init[endYearAndWeek]", "pre_init[topConcepts]", "pre_init[topLiveChannels]"]}, "bigquery-load-ravenne-visionnages": {"action_type": "bq_to_gcs", "kwargs": {"bucket": "pre_init[bucketArtifacts]", "process_date": "pre_init[processDate]", "query_file_path": "pre_init[workflowRelScriptsPath]/query_transformation_generatedRavenne.sql", "visionnageRavenneProject": "pre_init[gcpProjectId]", "visionnageRavenneDataset": "pre_init[visionnageRavenneDataset]", "visionnageRavenneRawTable": "pre_init[visionnageRavenneRawTable]", "visionnageRavenneTable": "pre_init[visionnageRavenneTable]", "uri": "pre_init[filtered]/init[endPrevYearAndWeek]/selected/part-r-*", "refStoreProject": "pre_init[refStoreProjectId]", "refStoreVisionnageRavenneDataset": "pre_init[refStoreVisionnageRavenneDataset]"}}, "stat-countconcepts": {"action_type": "hadoop", "main_class": "com.orange.profiling.ute.ravenne.stat.countconcepts.MainCountConcepts", "args": ["pre_init[ponderation]/init[endYearAndWeek]/conceptsweights/*", "pre_init[deltaThreshold]", "pre_init[countconcepts]/init[endYearAndWeek]", "pre_init[countconceptsIn]/init[endPrevYearAndWeek]", "pre_init[processDate]"]}, "stat-countbytimeslot": {"action_type": "hadoop", "main_class": "com.orange.profiling.ute.ravenne.stat.countbytimeslot.MainCountByTimeslot", "args": ["pre_init[ponderation]/init[endYearAndWeek]/conceptsweights/*", "pre_init[ponderation]/init[endYearAndWeek]/_COUNTERS", "pre_init[countbytimeslot]/init[endYearAndWeek]", "pre_init[processDate]"]}, "stat-countbyprovider": {"action_type": "hadoop", "main_class": "com.orange.profiling.ute.ravenne.stat.countbyprovider.MainCountByProvider", "args": ["pre_init[ponderation]/init[endYearAndWeek]/bestchannelsproviders/*/*", "pre_init[ponderation]/init[endYearAndWeek]/_COUNTERS", "pre_init[countbyprovider]/init[endYearAndWeek]", "pre_init[processDate]"]}, "genetic-concat": {"action_type": "hadoop", "main_class": "com.orange.profiling.ute.ravenne.genetic.concat.MainConcat", "args": ["pre_init[ponderation]/init[endYearAndWeek]/conceptsweights/*", "pre_init[ponderation]/init[endYearAndWeek]/bestchannelsproviders/LIVE/*", "pre_init[ponderation]/init[endYearAndWeek]/bestchannelsproviders/VOD/*", "pre_init[processDate]", "pre_init[genetic]/pre_init[processDate]/fullAnonymized", "pre_init[geneticOtt]/pre_init[processDate]/fullAnonymized", "pre_init[topConcepts]", "pre_init[topLiveChannels]", "pre_init[topOdProviders]"]}, "genetic-counterdiff": {"action_type": "hadoop", "main_class": "com.orange.profiling.ute.counters.check.CountersCheck", "args": ["pre_init[geneticIn]/init[oneWeekAgoDate]/fullAnonymized/_COUNTERS", "pre_init[genetic]/pre_init[processDate]/fullAnonymized/_COUNTERS", "ALL", "pre_init[workflowPropertiesFullPath]/genetic-counterdiff.properties"], "capture_output": true}, "de-anonymize-pns-sent": {"action_type": "hadoop", "main_class": "com.orange.profiling.ute.ravenne.genetic.pnssend.DesAnonymizeMain", "args": ["pre_init[genetic]/pre_init[processDate]/fullAnonymized", "pre_init[optinPath]/LATEST/optincalculation-r-00000", "init[ravenne.gdpr.pseudoanonymisation.enable]", "pre_init[genetic]/pre_init[processDate]/full"]}, "genetic-pnssend": {"action_type": "hadoop", "main_class": "com.orange.profiling.common.pns.MainPnsSend", "args": ["--pnsDirectory", "pre_init[genetic]/pre_init[processDate]", "--previousPnsSentDirectories", "pre_init[geneticIn]/*/sent", "--lastPnsDirectoryToCheck", "pre_init[genetic]/init[oneWeekAgoDate]", "--<PERSON><PERSON><PERSON>", "ravenne.genetic.pnssend", "--pnsFilePrefix", "PIG_PROFILTV", "--processDate", "pre_init[processDate]", "--expirationDelay", "4", "--numReducer", "100", "--fileFilterClass", "com.orange.profiling.ute.ravenne.genetic.pnssend.FileFilterRavenneGenetic", "--pigDiffClass", "com.orange.profiling.ute.ravenne.genetic.pnssend.PigDiffRavenneGenetic", "-D", "minDate=init[fiveWeekAgoDate]", "-D", "maxDate=pre_init[processDate]", "-D", "fileType=/sent/"]}, "genetic-sendtopnsbucket": {"action_type": "bash", "bash_commands_def": [{"command": "gsutil cp", "args": ["pre_init[genetic]/pre_init[processDate]/tosend/*", "pre_init[pnscrocodilebucketPath]/"]}]}, "genetic-createsuccess": {"action_type": "bash", "bash_commands_def": [{"command": "gsutil cp", "args": ["pre_init[workflowDataPath]/_SUCCESS", "pre_init[genetic]/pre_init[processDate]/_SUCCESS"]}]}, "genetic-countererror-mail": {"action_type": "mailer", "kwargs": {"gat_ape_api": "pre_init[url<PERSON><PERSON><PERSON>]", "secret_client_id": "pre_init[clientIdMail]_pwd", "secret_version": "pre_init[secretVersionMail]", "scope": "pre_init[serviceScopeMail]", "mail_api": "pre_init[mailApi]", "sender_name": "Orange-DataIA-Profiling", "subject": "UTE Ravenne anomaly detected", "txt_msg": "Number of genetic profiles for tomorrow has decreased : COUNTER_DIFF_IN_TARGET_DAY. less than -15%  Thank you for your collaboration.", "recipients": "pre_init[profilingMailAlert]", "reply_to": "pre_init[profilingMailAlert]"}}, "profiltype-tastebox": {"action_type": "hadoop", "main_class": "com.orange.profiling.ute.ravenne.profiltype.tastebox.MainTastebox", "args": ["pre_init[ponderation]/init[endYearAndWeek]/conceptsweights/dt*", "pre_init[catalogConceptFamily]", "pre_init[tastebox]/init[endYearAndWeek]"]}, "profiltype-profils-verticales": {"action_type": "hadoop", "main_class": "com.orange.profiling.ute.ravenne.profiltype.profils.MainProfils", "args": ["pre_init[tastebox]/init[endYearAndWeek]", "pre_init[catalogProfils]", "pre_init[catalogProfilsVectors]", "pre_init[catalogCombiCoeurs]", "pre_init[tmpProfilType]/init[endYearAndWeek]/", "pre_init[tmpVerticalScore]/init[endYearAndWeek]/"]}, "profiltype-counterdiff": {"action_type": "hadoop", "main_class": "com.orange.profiling.ute.counters.check.CountersCheck", "args": ["pre_init[scoringIn]/input/init[endPrevYearAndWeek]/add/ravenne/_COUNTERS", "pre_init[tmpProfilType]/init[endYearAndWeek]/_COUNTERS", "NB_USER_WITH_PROFIL|NB_PROFIL|NB_VECTOR|NO_PROFIL_FOUND|NB_AID_ALGO_TOPCONCEPTS|NB_AID_ALGO_VECTORTYPE|NB_PROFIL_ALGO_TOPCONCEPTS|NB_PROFIL_ALGO_VECTORTYPE|NB_FOYERS_HAVING_1_PROFILES|NB_FOYERS_HAVING_2_PROFILES|NB_FOYERS_HAVING_3_PROFILES|NB_FOYERS_HAVING_4_PROFILES|NB_FOYERS_HAVING_5_PROFILES", "pre_init[workflowPropertiesFullPath]/profiltype-counterdiff.properties"], "capture_output": true}, "profiltype-vertical-movefiles": {"action_type": "bash", "bash_commands_def": [{"command": "gsutil -m rm -rf", "args": ["pre_init[scoring]/input/init[endYearAndWeek]/add/ravenne", "pre_init[scoring]/input/init[endYearAndWeek]/add/ravenneVerticales", "pre_init[scoring]/input/init[endYearAndWeek]/add/ravenneOtt"]}, {"command": "gsutil -m mv -r", "args": ["pre_init[tmpProfilType]/init[endYearAndWeek]/profils", "pre_init[scoring]/input/init[endYearAndWeek]/add/ravenne"]}, {"command": "gsutil -m mv -r", "args": ["pre_init[tmpProfilType]/init[endYearAndWeek]/profilsOtt", "pre_init[scoring]/input/init[endYearAndWeek]/add/ravenneOtt"]}, {"command": "gsutil -m mv -r", "args": ["pre_init[tmpVerticalScore]/init[endYearAndWeek]/scoresVertical", "pre_init[scoring]/input/init[endYearAndWeek]/add/ravenneVerticales"]}, {"command": "gsutil mv", "args": ["pre_init[tmpProfilType]/init[endYearAndWeek]/_COUNTERS", "pre_init[scoring]/input/init[endYearAndWeek]/add/ravenne/"]}, {"command": "gsutil mv", "args": ["pre_init[tmpProfilType]/init[endYearAndWeek]/_SUCCESS", "pre_init[scoring]/input/init[endYearAndWeek]/add/ravenne/"]}, {"command": "gsutil cp", "args": ["pre_init[workflowDataPath]/_SUCCESS", "pre_init[ravenneStatus]/pre_init[processDate]/_SUCCESS"]}]}, "profiltype-countererror-mail": {"action_type": "mailer", "kwargs": {"gat_ape_api": "pre_init[url<PERSON><PERSON><PERSON>]", "secret_client_id": "pre_init[clientIdMail]_pwd", "secret_version": "pre_init[secretVersionMail]", "scope": "pre_init[serviceScopeMail]", "mail_api": "pre_init[mailApi]", "sender_name": "Orange-DataIA-Profiling", "subject": "(pre_init[environment]) Alert : UTE Ravenne anomaly detected on profils type", "txt_msg": "Anomaly detected in UTE Ravenne. Counter NB_USER_WITH_PROFIL has decreased since last week (or counter not defined) \n  COUNTER_DIFF_NB_USER_WITH_PROFIL is less than -5% \n  This means that there are less clients with profil type than previous week. \n If you want to ignore this anomaly you will need to move pre_init[tmpProfilType]/init[endYearAndWeek]/profils into \n pre_init[scoring]/input/init[endYearAndWeek]/add/ravenne/", "recipients": "pre_init[profilingMailAlert]", "reply_to": "pre_init[profilingMailAlert]"}}, "marker-profiles-join-catalog": {"action_type": "hadoop", "main_class": "com.orange.profiling.ute.ravenne.joinOeuvresConcepts.MainJoinOeuvresConcepts", "args": ["pre_init[catalogOeuvresIn]/pre_init[processDate]/vod-base-oeuvre", "pre_init[markersProfilesIn]/BO/pre_init[processDate]", "pre_init[markersProfilesOut]/concepts/pre_init[processDate]"]}, "marker-profiles-scores": {"action_type": "hadoop", "main_class": "com.orange.profiling.ute.ravenne.scoresmarkersprofiles.MainScoreMarkersProfiles", "args": ["pre_init[markersProfilesOut]/BO/pre_init[processDate]", "pre_init[geneticIn]/pre_init[processDate]/fullAnonymized", "pre_init[markersProfilesOut]/scores/pre_init[processDate]"]}, "marker-profiles-similarity": {"action_type": "hadoop", "main_class": "com.orange.profiling.ute.ravenne.markerprofilessimilarity.MainMarkerProfilesSimilarity", "args": ["pre_init[markersProfilesOut]/scores/pre_init[processDate]", "pre_init[markersProfilesOut]/markers/pre_init[processDate]"]}, "get-markers-profiles": {"action_type": "bash", "bash_commands_def": [{"command": "gsutil -m rm -rf", "args": ["pre_init[markersProfilesOut]/BO/pre_init[processDate] 2> /dev/null"]}, {"command": "gsutil cp", "args": ["pre_init[workflowScriptsPath]/getMarkersProfiles.sh", "."]}, {"command": "gsutil cp", "args": ["pre_init[workflowScriptsPath]/callokapi.sh", "."]}, {"command": "sh getMarkersProfiles.sh", "args": ["pre_init[urlBackOfficeRW]/exportMarkerProfile", "markersProfiles.csv", "pre_init[markersProfilesOut]/BO/pre_init[processDate]/markersProfiles.csv", "pre_init[url<PERSON><PERSON><PERSON>]", "pre_init[serviceScopeIhmRW]", "pre_init[clientIdIhmRW]", "pre_init[secretVersionIhmRW]"]}], "capture_output": false}, "markerprofile-movefiles": {"action_type": "bash", "bash_commands_def": [{"command": "gsutil -m rm -rf", "args": ["pre_init[scoring]/input/init[endYearAndWeek]/add/markerprofile/"]}, {"command": "gsutil cp", "args": ["pre_init[markersProfilesOut]/markers/pre_init[processDate]/*", "pre_init[scoring]/input/init[endYearAndWeek]/add/markerprofile/"]}]}, "check_success": {"action_type": "input_files_check", "input_uri_paths": ["pre_init[genetic]/pre_init[processDate]/_SUCCESS", "pre_init[ravenneStatus]/pre_init[processDate]/_SUCCESS"]}}