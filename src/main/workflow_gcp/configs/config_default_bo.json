{"urlOkapi": {"description": "Url d'accès à gat ape okapi pour récupérer le token d'authentification", "value": "@urlOkapi@"}, "urlBackOfficeRO": {"description": "Url de base de l'ihm BO Profiling (en lecture)", "value": "@urlBackOfficeRO@"}, "serviceScopeIhmRO": {"description": "Scope du service okapi pour accéder à l'ihm (en lecture)", "value": "@serviceScopeIhmRO@"}, "clientIdIhmRO": {"description": "Id client okapi pour ihm (en lecture)", "value": "@clientIdIhmRO@"}, "secretVersionIhmRO": {"description": "Secret version dans le secret manager pour l'ihm (en lecture)", "value": "@secretVersionIhmRO@"}, "urlBackOfficeRW": {"description": "Url de base de l'ihm BO Profiling (en écriture)", "value": "@urlBackOfficeRW@"}, "serviceScopeIhmRW": {"description": "Scope du service okapi pour accéder à l'ihm (en écriture)", "value": "@serviceScopeIhmRW@"}, "clientIdIhmRW": {"description": "Id client okapi pour ihm (en écriture)", "value": "@clientIdIhmRW@"}, "secretVersionIhmRW": {"description": "Secret version dans le secret manager pour l'ihm (en écriture)", "value": "@secretVersionIhmRW@"}}