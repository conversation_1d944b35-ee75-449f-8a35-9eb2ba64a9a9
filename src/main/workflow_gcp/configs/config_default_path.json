{"bucketArtifacts": {"description": "Bucket contenant les artefacts (libs, conf, ...) du workflow", "value": "@bucketArtifacts@"}, "bucketArtifactPath": {"description": "Chemin gcs complet du bucket contenant les artefacts", "value": "gs://&{bucketArtifacts}"}, "workflowRelPath": {"description": "Chemin relatif de cette branche du workflow sur composer 2", "value": "composer2/&{workflowName}/&{branchName}"}, "workflowRootPath": {"description": "Chemin gcs complet de l'emplacement sous lequel se trouvent les artefacts de cette version du workflow", "value": "&{bucketArtifactPath}/&{workflowRelPath}"}, "workflowConfPath": {"description": "Chemin gcs complet de la configuration du workflow", "value": "&{workflowRootPath}/conf"}, "workflowLibPath": {"description": "Chemin gcs complet des librairies du workflow", "value": "&{workflowRootPath}/libs"}, "workflowScriptsPath": {"description": "Chemin gcs complet des scripts du workflow", "value": "&{workflowRootPath}/scripts"}, "workflowRelScriptsPath": {"description": "Chemin gcs relatif des scripts du workflow", "value": "&{workflowRelPath}/scripts"}, "workflowDataPath": {"description": "Chemin gcs complet des données du workflow", "value": "&{workflowRootPath}/data"}, "bucketDataIn": {"description": "Bucket contenant les données générées par le workflow (accès en lecture)", "value": "@bucketDataIn@"}, "bucketDataInPath": {"description": "Chemin gcs complet du bucket contenant les data (accès en lecture)", "value": "gs://&{bucketDataIn}"}, "bucketData": {"description": "Bucket contenant les données générées par le workflow (accès en ecriture)", "value": "@bucketData@"}, "bucketDataPath": {"description": "Chemin gcs complet du bucket contenant les data (accès en ecriture)", "value": "gs://&{bucketData}"}, "tmpInput": {"description": "Input temporaires (en lecture)", "value": "&{bucketDataInPath}/tmp"}, "tmpOutput": {"description": "Output temporaires (en écriture)", "value": "&{bucketDataPath}/tmp"}, "wfRawInputs": {"description": "Inputs raw d'autres applications (en lecture)", "value": "&{bucketDataInPath}/raw"}, "wfRawOutputs": {"description": "Outputs raw de ce workflow (en écriture)", "value": "&{bucketDataPath}/raw"}, "wfInputs": {"description": "Inputs générés par d'autres workflow (en lecture)", "value": "&{bucketDataInPath}/generated"}, "wfInputsFastmarkers": {"description": "Inputs générés par d'autres workflow (en lecture)", "value": "&{bucketDataInPath}/generated/fastmarkers"}, "wfOutputs": {"description": "Outputs de ce workflow (en écriture)", "value": "&{bucketDataPath}/generated"}, "wfInputsShared": {"description": "Outputs partagés de ce workflow (en lecture)", "value": "&{bucketDataInPath}/shared"}, "wfOutputsShared": {"description": "Outputs partagés de ce workflow (en écriture)", "value": "&{bucketDataPath}/shared"}, "workflowPropertiesRelativePath": {"description": "Répertoire relatif pour les fichiers properties (utiliser dans dag.py)", "value": "tmp/&{workflowName}/&{branchName}/workflow"}, "workflowPropertiesFullPath": {"description": "Répertoire avec bucket pour les fichiers properties (utiliser dans dag_param.json)", "value": "&{bucketDataPath}/&{workflowPropertiesRelativePath}"}, "gdprFlagFile": {"description": "Fichier flag gdpr indiquant si on est en mode anonymisé ou non", "value": "&{wfOutputs}/gdpr-pseudoanonymized/conf/gdprFlagFile"}, "optinPath": {"description": "Path contenant les derniers optins calculés", "value": "&{wfInputs}/optinoptout"}}