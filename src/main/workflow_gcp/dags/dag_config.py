import os
from packages.arti_dags_common_package_ARTI_DAG_VERSION.profiling.dag_config import DagConfig


# Set timezone
os.environ['TZ'] = 'Europe/Paris'


def get_dag_config():
    return DagConfig(**{
        'dag_group': "@dagGroup@",
        'workflow_name': "@workflowName@",
        'branch_name': "@branchName@",
        'version': "@workflowVersion@",
        'dag_id': "DAG_ID_TO_REPLACE",
        'bucket_artifacts': "@bucketArtifacts@",
        'gcp_service_account': "@gcpServiceAccount@",
        'start_date': "@startDate@",
        'install_date': "INSTALL_DATE_TO_REPLACE",
        'email': "@profilingMailAlert@",
        'schedule': "@schedule@",
        'config_defaults': [
            'config_default_dag.json',
            'config_default_path.json',
            'config_default_bo.json',
            'config_default_mail.json',
            'config_default_dataproc.json',
            'config_workflow.json'
        ],
        'extra_dag_config': {
        'EXTERNAL_DEPENDENDENCIES_VERSIONS': ['@ute-mr-common-jar@','@ute-counters-check-jar@', '@joda-time@', '@guava@', '@commons-cli@',
                                                          '@json-simple@']  # EXTERNAL_DEPENDENDENCIES_VERSIONS
        }
    })
