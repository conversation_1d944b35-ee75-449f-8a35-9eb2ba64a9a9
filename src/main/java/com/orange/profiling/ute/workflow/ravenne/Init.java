package com.orange.profiling.ute.workflow.ravenne;

import com.orange.profiling.common.utils.FlagFileReader;
import org.apache.commons.cli.CommandLine;
import org.apache.commons.cli.CommandLineParser;
import org.apache.commons.cli.GnuParser;
import org.apache.commons.cli.OptionBuilder;
import org.apache.commons.cli.Options;
import org.apache.commons.cli.ParseException;
import org.apache.log4j.Logger;
import org.joda.time.DateTime;
import com.orange.profiling.common.utils.DatesUtils;
import com.orange.profiling.common.utils.oozie.OozieProperties;
import com.orange.profiling.ute.utils.PropertiesWriter;
import org.apache.hadoop.fs.Path;
import java.util.Properties;
import java.io.File;
import java.io.IOException;

public class Init {

    private static final String PARAM_DATE = "paramDate";
    private static final String FILTER_LIMIT = "filterLimit";
    private static final String GDPR_FLAG_FILE = "gdprFlagFilePath";
    private static final String GDPR_FLAG_ENABLE = "ravenne.gdpr.pseudoanonymisation.enable";
	private static final String OOZIE_ACTION_OUTPUT_PROPERTIES = "oozie.action.output.properties";

    /** Max number of weeks of progbymac and vod we use for filterAndTimeslot
     *
     */
    private static final int MAX_WEEKS = 12;

    /** Default number of weeks of progbymac and vod we use for filterAndTimeslot
     *
     */
    private static final int DEFAULT_WEEKS = 2;

    // Used for tests
    private static DateTime now = new DateTime();

    // private static String gdprFlagFilePath = "";

    private static final Logger LOGGER = Logger.getLogger(Init.class);
    /**
     * @param args
     * @throws ParseException
     */
    @SuppressWarnings("static-access")
    public static void main(String[] args) throws Exception {
        Options options = new Options();
        options.addOption("h", "help", false, "prints the help content");
        options.addOption(OptionBuilder.withArgName(PARAM_DATE).hasArg()
                .isRequired().withDescription("Parameter Date")
                .create(PARAM_DATE));
        options.addOption(OptionBuilder.withArgName(FILTER_LIMIT).hasArg()
                .isRequired().withDescription("Limit in weeks for filter")
                .create(FILTER_LIMIT));
        options.addOption(OptionBuilder.withArgName(GDPR_FLAG_FILE).hasArg()
                .isRequired().withDescription("gdpr Flag File Path")
                .create(GDPR_FLAG_FILE));
        options.addOption(OptionBuilder.withArgName("gcsPropertiesFile").hasArg()
				.withDescription("gcs path file to store properties generated by action (passed only when in gcs env)")
				.create("gcsPropertiesFile"));


        CommandLineParser parser = new GnuParser();
        CommandLine commandLine;

        // get params
        commandLine = parser.parse(options, args);
        String paramDate = commandLine.getOptionValue(PARAM_DATE);
        if (paramDate == null || paramDate.isEmpty()) {
            throw new ParseException(PARAM_DATE+" parameter not found");
        }
        String filterLimit = commandLine.getOptionValue(FILTER_LIMIT);
        Integer filterLimitInt = DEFAULT_WEEKS;
        if (filterLimit != null && !filterLimit.isEmpty()) {
            try {
                filterLimitInt = Integer.parseInt(filterLimit);
            }
            catch (NumberFormatException e) {
                filterLimitInt = DEFAULT_WEEKS;
            }
        }

        String gdprFlagFilePath = commandLine.getOptionValue(GDPR_FLAG_FILE);
        if (gdprFlagFilePath == null || gdprFlagFilePath.equals(""))
            throw new ParseException("Input path gdpr flag file is missing");

        // init working dates for oozie
        DateTime workingDate = getWorkingDate(paramDate);
        //setOozieDates(workingDate, filterLimitInt);
        Properties props = setAndGetWorkflowVariables(workingDate,filterLimitInt,gdprFlagFilePath);

		if (commandLine.getOptionValue("gcsPropertiesFile") != null)
        {   
            PropertiesWriter.writePropertiesToFs(props, new Path(commandLine.getOptionValue("gcsPropertiesFile")));
        }
        else {
            String oozieProp = System.getProperty(OOZIE_ACTION_OUTPUT_PROPERTIES);
            setOozieDates(workingDate, filterLimitInt,gdprFlagFilePath);
		    File propFile = new File(oozieProp);
            PropertiesWriter.writePropertiesToFile(props, propFile);
        }

    }

    public static void setOozieGdprFlag(String flagFilePath, OozieProperties props) throws IOException {
        FlagFileReader fr = new FlagFileReader();
        fr.read(flagFilePath);
        props.setProperty(GDPR_FLAG_ENABLE, String.valueOf(fr.getFlagValue(GDPR_FLAG_ENABLE, 0)));
    }
    public static void setGdprFlag(String flagFilePath, Properties props) throws IOException {
        FlagFileReader fr = new FlagFileReader();
        fr.read(flagFilePath);
        props.setProperty(GDPR_FLAG_ENABLE, String.valueOf(fr.getFlagValue(GDPR_FLAG_ENABLE, 0)));
    }

    /**
     * @param paramDate
     * @return DateTime
     */
    public static DateTime getWorkingDate(String paramDate) {
        return DatesUtils.DF_YYYYMMDD.parseDateTime(paramDate);
    }

    /**
     * @param workingDate
     * @throws Exception
     */
    public static void setOozieDates(DateTime workingDate, int filterLimit, String gdprFlagFilePath ) {
        OozieProperties props =  calculateOozieDates(workingDate, filterLimit);
        try {
            setOozieGdprFlag(gdprFlagFilePath, props);
        } catch (IOException e) {
            props.setProperty(GDPR_FLAG_ENABLE, String.valueOf(0));
            LOGGER.error(e.getMessage());
        }
        props.save();
    }

    protected static OozieProperties calculateOozieDates(DateTime workingDate, int filterLimit) {
        OozieProperties props = new OozieProperties();

        // for workflow monitoring
        DateTime now = getNow();
        props.setProperty("startTs", String.valueOf(now.getMillis() / DatesUtils.MILLISECONDS));
        // vstart is the virtual date of workflow start. to send to kibana.
        // The working date with hour of real workflow start date
        DateTime vstart = now.withDate(workingDate.getYear(),
                workingDate.getMonthOfYear(), workingDate.getDayOfMonth());
        props.setProperty("vStart", String.valueOf(vstart.getMillis() / DatesUtils.MILLISECONDS));

        // end week
        props.setProperty("endYearAndWeek", DatesUtils.DF_SLASHED_YEAR_WEEK.print(workingDate));
        // week - 1 as week number
        props.setProperty("endPrevYearAndWeek", DatesUtils.DF_SLASHED_YEAR_WEEK.print(workingDate.minusWeeks(1)));

        // week - 1 as day
        props.setProperty("oneWeekAgoDate", DatesUtils.DF_YYYYMMDD.print(workingDate.minusWeeks(1)));
        // week - 5 as day
        props.setProperty("fiveWeekAgoDate", DatesUtils.DF_YYYYMMDD.print(workingDate.minusWeeks(5)));

        // begin week : we compute 12 weeks from beginWeek.MONDAY to endYearAndWeek.SUNDAY, so we go back 11 weeks ago.
        props.setProperty("beginYearAndWeek",
                DatesUtils.DF_SLASHED_YEAR_WEEK.print(workingDate.minusWeeks(MAX_WEEKS-1)));

        // filter begin week
        // filterandtimeslot step is computed in delta
        // It computes progbymac and vod from filterBeginYearWeek.MONDAY to endYearAndWeek.SUNDAY
        // As we compute this endYearAndWeek, if we want to compute 2 weeks (filterLimit = 2) we should go back 1 week before
        // If filterLimit = 0, filterBeginYearAndWeek will be after endYearAndWeek and filterAndTimeslot will not compute any file
        // FilterLimit is max 12 as it's no use to compute files before beginYearAndWeek
        if (filterLimit > MAX_WEEKS) {
            filterLimit = MAX_WEEKS;
        }
        props.setProperty("filterBeginYearAndWeek",
                DatesUtils.DF_SLASHED_YEAR_WEEK.print(workingDate.minusWeeks(filterLimit-1)));

        return props;
    }

    public static DateTime getNow() {
        return now;
    }

    public static void setNow(DateTime fakenow) {
        now = fakenow;
    }
    public static Properties setAndGetWorkflowVariables(DateTime workingDate, int filterLimit, String flagFilePath) throws Exception {

		// String oozieProp = System.getProperty(OOZIE_ACTION_OUTPUT_PROPERTIES);
		Properties props = new Properties();

		//for workflow monitoring
        DateTime now = getNow();
        props.setProperty("startTs", String.valueOf(now.getMillis() / DatesUtils.MILLISECONDS));
        // vstart is the virtual date of workflow start. to send to kibana.
        // The working date with hour of real workflow start date
        DateTime vstart = now.withDate(workingDate.getYear(),
                workingDate.getMonthOfYear(), workingDate.getDayOfMonth());
        props.setProperty("vStart", String.valueOf(vstart.getMillis() / DatesUtils.MILLISECONDS));

        // end week
        props.setProperty("endYearAndWeek", DatesUtils.DF_SLASHED_YEAR_WEEK.print(workingDate));
        // week - 1 as week number
        props.setProperty("endPrevYearAndWeek", DatesUtils.DF_SLASHED_YEAR_WEEK.print(workingDate.minusWeeks(1)));

        // week - 1 as day
        props.setProperty("oneWeekAgoDate", DatesUtils.DF_YYYYMMDD.print(workingDate.minusWeeks(1)));
        // week - 5 as day
        props.setProperty("fiveWeekAgoDate", DatesUtils.DF_YYYYMMDD.print(workingDate.minusWeeks(5)));

        // begin week : we compute 12 weeks from beginWeek.MONDAY to endYearAndWeek.SUNDAY, so we go back 11 weeks ago.
        props.setProperty("beginYearAndWeek",
                DatesUtils.DF_SLASHED_YEAR_WEEK.print(workingDate.minusWeeks(MAX_WEEKS-1)));

        // filter begin week
        // filterandtimeslot step is computed in delta
        // It computes progbymac and vod from filterBeginYearWeek.MONDAY to endYearAndWeek.SUNDAY
        // As we compute this endYearAndWeek, if we want to compute 2 weeks (filterLimit = 2) we should go back 1 week before
        // If filterLimit = 0, filterBeginYearAndWeek will be after endYearAndWeek and filterAndTimeslot will not compute any file
        // FilterLimit is max 12 as it's no use to compute files before beginYearAndWeek
        if (filterLimit > MAX_WEEKS) {
            filterLimit = MAX_WEEKS;
        }
        props.setProperty("filterBeginYearAndWeek",
                DatesUtils.DF_SLASHED_YEAR_WEEK.print(workingDate.minusWeeks(filterLimit-1)));


        try {
                setGdprFlag(flagFilePath, props);
        } catch (IOException e) {
                props.setProperty(GDPR_FLAG_ENABLE, String.valueOf(0));
                LOGGER.error(e.getMessage());
        }
        

        return props;
    }

}
