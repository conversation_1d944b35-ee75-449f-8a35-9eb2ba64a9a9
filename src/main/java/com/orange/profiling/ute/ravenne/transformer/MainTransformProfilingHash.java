package com.orange.profiling.ute.ravenne.transformer;

import java.io.IOException;

import com.orange.profiling.common.utils.DatesUtils;
import com.orange.profiling.common.utils.FieldsUtils;
import com.orange.profiling.common.utils.FsUtils;
import com.orange.profiling.ute.ravenne.ponderation.FilteredInputFileFilter;
import org.apache.hadoop.fs.FileSystem;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Job;
import org.apache.hadoop.mapreduce.JobStatus;
import org.apache.hadoop.mapreduce.lib.input.FileInputFormat;
import org.apache.hadoop.mapreduce.lib.input.MultipleInputs;
import org.apache.hadoop.mapreduce.lib.input.TextInputFormat;
import org.apache.hadoop.mapreduce.lib.output.FileOutputFormat;
import org.apache.hadoop.mapreduce.lib.output.LazyOutputFormat;
import org.apache.hadoop.mapreduce.lib.output.MultipleOutputs;
import org.apache.hadoop.mapreduce.lib.output.TextOutputFormat;
import org.apache.log4j.Logger;

import com.orange.profiling.common.exceptions.FailedJobException;
import com.orange.profiling.common.mapred.UteConfiguration;
import com.orange.profiling.common.utils.HadoopGCSFileSystemFactory;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormatter;
import com.orange.profiling.common.mapred.UteCountersLogger;
import com.orange.profiling.common.mapred.UteCountersWriter;

public class MainTransformProfilingHash {

    private static final Logger LOGGER = Logger.getLogger(MainTransformProfilingHash.class);
    private static final DateTimeFormatter YEARWEEK_DATE_FORMATER = DatesUtils.DF_SLASHED_YEAR_WEEK;
    private static final int ARG_LENGTH = 5;
    private static final int INPUT_FILTERED_IDX = 0;
    private static final int INPUT_MAPPING_PROFILINGHASH_IDX = 1;
    private static final int PARAM_BEGIN_YEARWEEK_IDX = 2;
    private static final int PARAM_END_YEARWEEK_IDX = 3;
    private static final int OUTPUT_DIR_IDX = 4;
    private static final int NB_REDUCER = 250;
    private static final String JOB_NAME = "ute.ravenne.transformer.profilinghash";

    /** Configuration key for mapping profilingHash => AidHash */
    public static final String MAPPING_DATA = "mappingData";

    public static final String OUT_SELECTED_TRANSFORM = "selected";

    private String inputPathRavenneFiltered;
    private String inputPathMappingProfilingHash;
    private String outputDir;

    public MainTransformProfilingHash(String inputPathRavenneFiltered, String inputPathMappingProfilingHash, String outputDir) {
        super();
        this.inputPathRavenneFiltered = inputPathRavenneFiltered;
        this.inputPathMappingProfilingHash = inputPathMappingProfilingHash;
        this.outputDir = outputDir;
    }

    public static void main(final String[] args) throws IOException,
            InterruptedException, ClassNotFoundException, FailedJobException {

        if (args.length < ARG_LENGTH) {
            String mess = "Takes " + ARG_LENGTH
                    + " arguments : inputPathRavenneFiltered inputPathMappingProfilingHash beginYearWeek endYearWeek"
                    + " outputDir";
            LOGGER.error(mess);
            throw new IllegalArgumentException(mess);
        }

        String inputPathRavenneFiltered = args[INPUT_FILTERED_IDX];
        String inputPathMappingProfilingHash = args[INPUT_MAPPING_PROFILINGHASH_IDX];
        String beginYearSlashWeek = args[PARAM_BEGIN_YEARWEEK_IDX];
        String endYearSlashWeek = args[PARAM_END_YEARWEEK_IDX];

        String outputDir = args[OUTPUT_DIR_IDX];

        MainTransformProfilingHash transformProfilingHashMain =
                new MainTransformProfilingHash(inputPathRavenneFiltered, inputPathMappingProfilingHash, outputDir);

        DateTime yearWeekToFilter = YEARWEEK_DATE_FORMATER.parseDateTime(beginYearSlashWeek);
        DateTime lastYearWeekToFilter = YEARWEEK_DATE_FORMATER.parseDateTime(endYearSlashWeek);

        // Boucle pour chaque semaine
        while (!yearWeekToFilter.isAfter(lastYearWeekToFilter)) {
            transformProfilingHashMain.doJobTransform(yearWeekToFilter);
            yearWeekToFilter = yearWeekToFilter.plusWeeks(1);
        }
    }

    private void doJobTransform(DateTime yearWeekToFilter)
            throws IOException, ClassNotFoundException, InterruptedException, FailedJobException {

        UteConfiguration conf = new UteConfiguration();
        conf.set(FilteredInputFileFilter.MIN_DATE, YEARWEEK_DATE_FORMATER.print(yearWeekToFilter));
        conf.set(FilteredInputFileFilter.MAX_DATE, YEARWEEK_DATE_FORMATER.print(yearWeekToFilter));

        conf.set(FilteredInputFileFilter.OUTPUT_DIR_PATH,String.join(FieldsUtils.SLASH,
                outputDir, YEARWEEK_DATE_FORMATER.print(yearWeekToFilter)));

        conf.set(MAPPING_DATA, inputPathMappingProfilingHash);


        // Create job
        String yearweekLabel = DatesUtils.DF_YEARWEEK.print(yearWeekToFilter);
        Job job = Job.getInstance(conf, JOB_NAME + yearweekLabel);
        job.setJarByClass(MainTransformProfilingHash.class);
        job.setNumReduceTasks(NB_REDUCER);

        // Setup MapReduce
        job.setMapperClass(TransformProfilingHashMapper.class);
        job.setReducerClass(TransformProfilingHashReducer.class);

        // Specify key / value
        job.setOutputKeyClass(Text.class);
        job.setOutputValueClass(Text.class);

        // Input
        MultipleInputs.addInputPath(job, new Path(inputPathRavenneFiltered), TextInputFormat.class, TransformProfilingHashMapper.class);

        // Date filter
        FileInputFormat.setInputPathFilter(job, FilteredInputFileFilter.class);

        // Output
        Path weekOutputDirPath = new Path(String.join(FieldsUtils.SLASH,
                outputDir, YEARWEEK_DATE_FORMATER.print(yearWeekToFilter)));

        FileOutputFormat.setOutputPath(job, weekOutputDirPath);
        LazyOutputFormat.setOutputFormatClass(job, TextOutputFormat.class);
        MultipleOutputs.addNamedOutput(job, OUT_SELECTED_TRANSFORM, TextOutputFormat.class, Text.class, Text.class);

        // Delete output if exists
        FsUtils fsUtilsOutput = new FsUtils(weekOutputDirPath,conf);
        if (fsUtilsOutput.getFs().exists(weekOutputDirPath)) {
            fsUtilsOutput.getFs().delete(weekOutputDirPath, true);
        }

        // Execute job
        boolean succeeded = job.waitForCompletion(true);
        if (!succeeded) {
            throw new FailedJobException("Job " + JOB_NAME + yearweekLabel);
        }

        // Statistics
        UteCountersWriter.writeAlternateCounters(job, fsUtilsOutput.getFs(), weekOutputDirPath);

    }

}

