package com.orange.profiling.ute.ravenne.file;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;

import com.orange.profiling.common.file.AbstractProfilingHdfsFile;
import com.orange.profiling.common.utils.FieldsUtils;


/**
 * File /user/profiling-ute/tmp/ravenne/ponderation/[YYYY/WW]/conceptsweights/*-r-*
 * Generated by Ravenne
 * Ponderation of concepts by aid and daytimeslot with sum of live and vod durations
 *
 * Format
 *   aid TAB daytimeslot TAB ponderatedConcepts TAB liveDuration TAB vodDuration
 *
 *     - aid : aid of the client
 *     - timebox : dxty, dxpz, dx ou w
 *     - ponderatedConcepts : list of ponderated concepts
 *     - liveDuration : total duration of live view
 *     - vodDuration : total duration of vod view
 *
 * Pig :
 *     filtered = LOAD '/user/profiling-ute/tmp/ravenne/ponderation/[YYYY/WW]/conceptsweights/*-r-*'
 *                USING PigStorage('\t') AS (aid:chararray, timebox:chararray, ponderatedConcepts:chararray,
 *                      liveDuration:long, vodDuration:long);
 *
 */
public class ConceptsWeights extends AbstractProfilingHdfsFile {
    public static final int AID = 0;
    public static final int TIMEBOX = 1;
    public static final int PONDERATED_CONCEPTS = 2;
    public static final int LIVE_DURATION = 3;
    public static final int VOD_DURATION = 4;

    private static final Map<String, Integer> FIELDS = new HashMap<>();
    static {
        FIELDS.put("aid", AID);
        FIELDS.put("timebox", TIMEBOX);
        FIELDS.put("ponderatedConcepts", PONDERATED_CONCEPTS);
        FIELDS.put("liveDuration", LIVE_DURATION);
        FIELDS.put("vodDuration", VOD_DURATION);
    }

    private static final String SEPARATOR = FieldsUtils.TAB;
    private static final Pattern PATTERN = FieldsUtils.TAB_PATTERN;

    @Override
    public Map<String, Integer> getFields() {
        return FIELDS;
    }

    @Override
    public String getSeparator() {
        return SEPARATOR;
    }

    @Override
    public Pattern getSplitPattern() {
        return PATTERN;
    }

}
