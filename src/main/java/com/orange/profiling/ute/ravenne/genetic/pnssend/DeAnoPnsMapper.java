package com.orange.profiling.ute.ravenne.genetic.pnssend;

import java.io.IOException;

import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Mapper;

/**
 * DeAnoPnsMapper processes genetic profile records for the deanonymization process.
 * 
 * This mapper handles the genetic profile input stream of the deanonymization job.
 * It processes profile records that contain anonymized user IDs and their viewing
 * preferences. The mapper:
 * 
 * 1. Input Format:
 *    [anonymized_id];;[profile_data]
 *    Where profile_data contains:
 *    - Timeslot information
 *    - Weighted concepts (user interests)
 *    - Channel preferences
 *    - Viewing ratios
 *    - Expiration timestamps
 * 
 * 2. Processing:
 *    - Parses input records using ;; delimiter
 *    - Validates record format
 *    - Emits records with anonymized ID as key
 * 
 * Output Format:
 * Key: anonymized_id (Text)
 * Value: original record (Text)
 * 
 * Example Input:
 * 756619667;;d3t8|IA|TimeSlots|{"th_rav":[...]}|1614207600000|1611615600000
 * 
 * Note: This mapper is used in conjunction with OptinMapper to prepare
 * records for deanonymization in the reduce phase.
 */
public class DeAnoPnsMapper extends Mapper<Object, Text, Text, Text>{
	
	@Override
	public void map(Object key, Text value, Context context) throws IOException, InterruptedException {

				
		//756619667;;
		//d3t8|IA|TimeSlots|{"th_rav":[{"concept":"catégories/programme d\u0027information","weight":"565"},
		//{"concept":"catégories/programme divers","weight":"565"},
		//{"concept":"sujets/actualité","weight":"565"}],"th_ch":["481"],
		//"th_rvl":0}|1614207600000|1611615600000
		String[] bloc = value.toString().split(";;");
		if (bloc.length == 2)
			context.write(new Text(bloc[0]), value);
	}

}
