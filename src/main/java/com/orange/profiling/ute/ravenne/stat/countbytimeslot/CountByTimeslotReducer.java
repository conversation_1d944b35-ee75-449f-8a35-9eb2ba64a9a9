package com.orange.profiling.ute.ravenne.stat.countbytimeslot;

import java.io.IOException;
import java.util.regex.Pattern;

import org.apache.hadoop.io.NullWritable;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Reducer;

import com.orange.profiling.common.utils.FieldsUtils;
import com.orange.profiling.ute.ravenne.logger.RavenneKpiLogger;

public class CountByTimeslotReducer extends Reducer<Text, Text, NullWritable, Text> {

    public static final String PIPE = FieldsUtils.PIPE;
    public static final Pattern PIPE_PATTERN = FieldsUtils.PIPE_PATTERN;
    public static final String TAB = FieldsUtils.TAB;
    private static final double PERCENT = 100.0;

    private long totalNbAid = 1L;
    private long totalNbProfils = 0L;
    private RavenneKpiLogger kpiLogger;
    private Text outputValue = new Text();

    @Override
    public final void reduce(final Text key, final Iterable<Text> values, final Context context)
            throws IOException, InterruptedException {
        String[] keyParts = PIPE_PATTERN.split(key.toString());
        if (keyParts.length >= ConceptsWeightsMapper.KEY_LENGTH) {

            String timebox = keyParts[ConceptsWeightsMapper.KEY_TIMEBOX_IDX];
            String nbConcept = keyParts[ConceptsWeightsMapper.KEY_NBCONCEPTS_IDX];

            totalNbProfils = 0L;
            readValuesToUpdateTotalCount(values);

            if (totalNbProfils > 0) {
                writeOutput(context, timebox, nbConcept);
            }
        }
    }

    private void readValuesToUpdateTotalCount(final Iterable<Text> values) {
        for(Text value: values) {
            long num = FieldsUtils.getSafelyLong(value.toString(), 0L);
            totalNbProfils += num;
        }
    }

    private void writeOutput(final Context context, String timebox, String nbConcept)
            throws IOException, InterruptedException {
        long profilsPercent = Math.round(PERCENT*totalNbProfils/totalNbAid);

        outputValue.set(String.join(TAB, timebox, nbConcept,
                Long.toString(totalNbProfils), Long.toString(profilsPercent)));
        context.write(NullWritable.get(), outputValue);

        kpiLogger.sendDeltaCountByTimeslot(timebox, nbConcept, totalNbProfils, profilsPercent);
    }

    @Override
    protected void setup(Context context) throws IOException, InterruptedException {
        String totalNbAidStr = context.getConfiguration().get(MainCountByTimeslot.CONF_NB_AID);
        totalNbAid = FieldsUtils.getSafelyLong(totalNbAidStr, 1L);
        String processDate = context.getConfiguration().get(MainCountByTimeslot.CONF_PROCESS_DATE);
        buildKpiLogger(processDate);
    }

    private void buildKpiLogger(String processDate) {
        if (kpiLogger == null) {
            kpiLogger = new RavenneKpiLogger(processDate);
        }
    }

    /** For test purpose only : set kpi logger with mockLogger
     *
     * @param mockLogger
     */
    public void setKpiLogger(RavenneKpiLogger mockLogger) {
        kpiLogger = mockLogger;
    }

}
