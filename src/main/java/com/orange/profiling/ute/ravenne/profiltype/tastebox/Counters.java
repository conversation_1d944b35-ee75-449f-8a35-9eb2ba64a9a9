package com.orange.profiling.ute.ravenne.profiltype.tastebox;

/**
 * Enumeration of counters used to track various metrics and error conditions
 * in the Ravenne Tastebox Processing System.
 * 
 * Counters:
 * - BAD_FORMAT: Number of input records that failed format validation
 * - CONCEPT_WITHOUT_FAMILY: Number of concepts that couldn't be mapped to a family
 * - CONCEPT_WITH_NULL_WEIGHT: Number of concepts encountered with zero weight
 * 
 * These counters are used for:
 * 1. Quality monitoring of input data
 * 2. Tracking concept family mapping coverage
 * 3. Identifying potential data issues
 * 
 * Counter values are written to the job output directory and can be
 * accessed via the Hadoop job interface.
 */
public enum Counters {
	BAD_FORMAT, CONCEPT_WITHOUT_FAMILY, CONCEPT_WITH_NULL_WEIGHT

}