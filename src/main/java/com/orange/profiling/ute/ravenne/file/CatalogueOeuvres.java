package com.orange.profiling.ute.ravenne.file;

import com.orange.profiling.common.file.AbstractProfilingHdfsFile;
import com.orange.profiling.common.utils.FieldsUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;


public class CatalogueOeuvres extends AbstractProfilingHdfsFile {
    public static final int ID_OEUVRE = 0;
    public static final int METADATA_EXTERNAL_ID = 1;
    public static final int SAISON_NAME = 2;
    public static final int SERIE_NAME = 3;
    public static final int METADATA_TYPE = 4;
    public static final int TYPE = 5;
    public static final int CONCEPTS = 6;

    private static final Map<String, Integer> FIELDS = new HashMap<>();
    static {
        FIELDS.put("idOeuvre", ID_OEUVRE);
        FIELDS.put("metaDataExternalId", METADATA_EXTERNAL_ID);
        FIELDS.put("saisonName", SAISON_NAME);
        FIELDS.put("serieName", SERIE_NAME);
        FIELDS.put("metaDataType", METADATA_TYPE);
        FIELDS.put("type", TYPE);
        FIELDS.put("concepts", CONCEPTS);
    }

    private static final String SEPARATOR = FieldsUtils.TAB;
    private static final Pattern PATTERN = FieldsUtils.TAB_PATTERN;

    @Override
    public Map<String, Integer> getFields() {
        return FIELDS;
    }

    @Override
    public String getSeparator() {
        return SEPARATOR;
    }

    @Override
    public Pattern getSplitPattern() {
        return PATTERN;
    }

}