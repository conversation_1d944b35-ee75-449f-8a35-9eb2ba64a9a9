package com.orange.profiling.ute.ravenne.genetic.pnssend;

import java.io.IOException;
import java.util.ArrayList;

import org.apache.hadoop.io.NullWritable;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Reducer;

/**
 * DeAnonymizeReducer performs the final deanonymization of genetic profiles.
 * 
 * This reducer combines optin data with genetic profiles to create deanonymized
 * user profiles while ensuring GDPR compliance. For each anonymized ID, it:
 * 
 * 1. Processes Input:
 *    - Optin records (from OptinMapper)
 *      Format: "OPTIN\t[real_aid]\t[flags]"
 *    - Genetic profiles (from DeAnoPnsMapper)
 *      Format: "[anon_aid];;<profile_data>"
 * 
 * 2. Processing Logic:
 *    - Identifies optin records by "OPTIN" prefix
 *    - Extracts real user ID from optin data
 *    - Validates user consent
 *    - Special handling for opt-out aggregate (AID: 999999999)
 * 
 * 3. Output Generation:
 *    - Only outputs profiles for users with valid consent
 *    - Replaces anonymized IDs with real user IDs
 *    - Preserves opt-out aggregate profiles
 * 
 * Key Features:
 * - GDPR compliance through optin validation
 * - Support for opt-out aggregates
 * - Clean separation of profile and consent data
 * 
 * Output Format:
 * [real_aid];;<profile_data>
 * 
 * Note: The reducer ensures that only profiles with valid consent
 * are deanonymized and forwarded to the PNS service.
 */
public class DeAnonymizeReducer extends Reducer<Text, Text, Text, NullWritable> {
	private static final String FIELD_NAME_OPTIN = "OPTIN";
	public static final String OPTOUT_AID = "999999999";
	/**
	 * @param key     AID
	 * @param values
	 * @param context
	 * @throws IOException
	 * @throws InterruptedException
	 * @retrun TUPLES = (Aid\t;score\tValue)
	 */
	@Override
	public void reduce(Text key, Iterable<Text> values, Context context) throws IOException, InterruptedException {
		
		
		Boolean optinOk = false;
		String[] line = null;
		ArrayList<String> lines = new ArrayList<String>();
		String aid = "";

		for (Text value : values) {
			if (value.toString().startsWith(FIELD_NAME_OPTIN)) {
				line = value.toString().split("\\t");
				optinOk = true;
				aid = line[1];
			} else {
				lines.add(value.toString());
			}
		}

		// Re-integrate OptoutAID 999999999 since it is not in optin file
		if (OPTOUT_AID.equals(key.toString())){
			optinOk = true;
			aid = key.toString();
		}

		if (optinOk && !lines.isEmpty() ) {
			for (String el : lines) {
				String[] lineToWrite = el.split("\\;;");				
				Text toWrite = new Text(aid + ";;" + lineToWrite[1]);
				context.write(toWrite, NullWritable.get());
			}
		}
	}
}
