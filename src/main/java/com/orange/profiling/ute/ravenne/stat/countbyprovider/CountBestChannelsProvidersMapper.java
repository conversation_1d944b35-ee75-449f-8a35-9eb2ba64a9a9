package com.orange.profiling.ute.ravenne.stat.countbyprovider;

import java.io.IOException;
import java.util.Map;

import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Mapper;

import com.orange.profiling.common.utils.FieldsUtils;
import com.orange.profiling.common.utils.OrangeTimeslotUtils;
import com.orange.profiling.ute.ravenne.file.BestChannelsProviders;
import com.orange.profiling.ute.ravenne.util.ValuedKeyUtils;

public class CountBestChannelsProvidersMapper extends Mapper<Object, Text, Text, Text> {

    public static final String KEY_SEPARATOR = FieldsUtils.PIPE;
    public static final int KEY_LENGTH = 2;
    public static final int KEY_TIMEBOX_IDX = 0;
    public static final int KEY_NBCHANNEL_IDX = 1;

    private NbProfilCounter nbProfilCounter;
    private BestChannelsProviders bestChannelsProviders = new BestChannelsProviders();
    private Text outputKey = new Text();
    private static final Text OUTPUT_VALUE = new Text("1");

    @Override
    public final void setup(final Context context) throws InterruptedException {
        nbProfilCounter = new NbProfilCounter(context);
    }

    @Override
    public final void map(final Object key, final Text value, final Context context)
            throws IOException, InterruptedException {

        bestChannelsProviders.setValue(value.toString());
        if (bestChannelsProviders.checkFormat()) {
            String timebox = bestChannelsProviders.getField(BestChannelsProviders.TIMEBOX);
            if (OrangeTimeslotUtils.isValidTimebox(timebox)) {
                String channels = bestChannelsProviders.getField(BestChannelsProviders.PROVIDERS_DURATIONS);
                Map<String,Long> channelsMap = ValuedKeyUtils.parseStringToKeyValue(channels);
                int nbChannel = channelsMap.size();
                outputKey.set(String.join(KEY_SEPARATOR, timebox, Integer.toString(nbChannel)));
                context.write(outputKey, OUTPUT_VALUE);

                String timeboxType = OrangeTimeslotUtils.getTimeboxType(timebox);
                nbProfilCounter.countNbProfilsByTimeboxTypeAndnbChannels(timeboxType, nbChannel);
            }
        }

    }

}
