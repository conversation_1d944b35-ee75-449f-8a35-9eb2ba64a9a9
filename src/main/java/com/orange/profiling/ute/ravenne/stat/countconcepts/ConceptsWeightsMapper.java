package com.orange.profiling.ute.ravenne.stat.countconcepts;

import java.io.IOException;
import java.util.Map;

import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Mapper;
import com.orange.profiling.common.utils.FieldsUtils;
import com.orange.profiling.common.utils.OrangeTimeslotUtils;
import com.orange.profiling.ute.ravenne.file.ConceptsWeights;
import com.orange.profiling.ute.ravenne.stat.Counters;
import com.orange.profiling.ute.ravenne.util.ValuedKeyUtils;

/**
 * <AUTHOR>
 *
 */
public class ConceptsWeightsMapper extends Mapper<Object, Text, Text, Text> {

    private static final String TAB = FieldsUtils.TAB;

    public static final String OUT_ID_TODAY = "T";
    public static final String KEY_SEPARATOR = FieldsUtils.PIPE;
    public static final int KEY_LENGTH = 2;
    public static final int KEY_TIMEBOX_IDX = 0;
    public static final int KEY_CONCEPT_IDX = 1;

    private ConceptsWeights conceptsWeights = new ConceptsWeights();
    private Text outputKey = new Text();
    private static final Text OUTPUT_VALUE = new Text(OUT_ID_TODAY+TAB+"1");


    /*
     * (non-Javadoc)
     *
     * @see org.apache.hadoop.mapreduce.Mapper#map(KEYIN, VALUEIN,
     * org.apache.hadoop.mapreduce.Mapper.Context)
     */
    @Override
    public final void map(final Object key, final Text value,
            final Context context) throws IOException, InterruptedException {

        conceptsWeights.setValue(value.toString());
        if (conceptsWeights.checkFormat()) {
            String timebox = conceptsWeights.getField(ConceptsWeights.TIMEBOX);
            String timeboxType = OrangeTimeslotUtils.getTimeboxType(timebox);

            if (!timeboxType.isEmpty()) {

                String weightedConcepts = conceptsWeights.getField(ConceptsWeights.PONDERATED_CONCEPTS);
                Map<String,Long> conceptsMap = ValuedKeyUtils.parseStringToKeyValue(weightedConcepts);
                for(String concept: conceptsMap.keySet()) {
                    outputKey.set(String.join(KEY_SEPARATOR, timebox, concept));
                    context.write(outputKey, OUTPUT_VALUE);

                    // if timebox = "w", timeboxtype is also w : no need to write twice...
                    if (!timeboxType.equals(timebox)) {
                        outputKey.set(String.join(KEY_SEPARATOR, timeboxType, concept));
                        context.write(outputKey, OUTPUT_VALUE);
                    }
                }
            }
        }
        else {
            context.getCounter(Counters.BAD_FORMAT).increment(1);
        }
    }

}