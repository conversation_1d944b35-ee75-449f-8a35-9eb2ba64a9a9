package com.orange.profiling.ute.ravenne.markerprofilessimilarity;

import com.orange.profiling.common.exceptions.FailedJobException;
import com.orange.profiling.common.mapred.UteConfiguration;
import com.orange.profiling.common.utils.FsUtils;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Job;
import org.apache.hadoop.mapreduce.lib.input.FileInputFormat;
import org.apache.hadoop.mapreduce.lib.output.FileOutputFormat;

import java.io.IOException;

public class MainMarkerProfilesSimilarity {

    private static final String JOB_MARKER_PROFILES_SIMILARITY = "ute:marker-profiles-similarity";
    private static final int EXPECTED_ARGS = 2;
    private static final String USAGE = "Takes 2 arguments: inputPath outputPath";

    public static void main(String[] args)
            throws ClassNotFoundException, FailedJobException, InterruptedException, IOException {

        // Validation des arguments
        if (args.length != EXPECTED_ARGS) {
            throw new IllegalArgumentException(USAGE);
        }

        // Chemins en tant que constantes
        final Path inputPath = new Path(args[0]);
        final Path outputPath = new Path(args[1]);

        // Création et configuration du job
        Job job = configureJob(new UteConfiguration(true),
                inputPath,
                outputPath);

        // Exécution du job
        if (!job.waitForCompletion(true)) {
            throw new FailedJobException(JOB_MARKER_PROFILES_SIMILARITY);
        }
    }

    private static Job configureJob(UteConfiguration conf,
                                    Path inputPath,
                                    Path outputPath)
            throws IOException {

        // Nettoyage du répertoire de sortie
        cleanOutputDir(conf, outputPath);

        // Configuration du job
        Job job = Job.getInstance(conf, JOB_MARKER_PROFILES_SIMILARITY);
        job.setJarByClass(MainMarkerProfilesSimilarity.class);

        // Configuration des types de sortie
        job.setMapOutputKeyClass(Text.class);
        job.setMapOutputValueClass(Text.class);
        job.setOutputKeyClass(Text.class);
        job.setOutputValueClass(Text.class);

        // Configuration du mapper et reducer
        job.setMapperClass(ScoreMapper.class);
        job.setReducerClass(MarkerProfilesSimilarityReducer.class);
        job.setNumReduceTasks(150);

        // Configuration des entrées
        FileInputFormat.addInputPath(job, inputPath);

        // Configuration de la sortie
        FileOutputFormat.setOutputPath(job, outputPath);

        return job;
    }

    private static void cleanOutputDir(UteConfiguration conf, Path outputDir)
            throws IOException {
        FsUtils fsUtils = new FsUtils(outputDir, conf);
        if (fsUtils.getFs().exists(outputDir)) {
            fsUtils.getFs().delete(outputDir, true);
        }
    }
}
