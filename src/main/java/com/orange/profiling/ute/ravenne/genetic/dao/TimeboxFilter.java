package com.orange.profiling.ute.ravenne.genetic.dao;

import com.orange.profiling.common.utils.OrangeTimeslotUtils;

public class TimeboxFilter {

    private String processDay;

    public TimeboxFilter(String processDay) {
        this.processDay = processDay;
    }

    public boolean isAllowed(String timebox, boolean isOptoutAid) {

        boolean isAllowed = false;
        if (timebox.startsWith(OrangeTimeslotUtils.WT)) {
            isAllowed = isOptoutAid;
        }
        else if (timebox.startsWith(OrangeTimeslotUtils.WP)) {
            isAllowed = isOptoutAid;
        }
        else if (timebox.startsWith(OrangeTimeslotUtils.WEEK)) {
            isAllowed = true;
        }
        else if (timebox.startsWith(OrangeTimeslotUtils.DAY)) {
            String timeboxDay = OrangeTimeslotUtils.getDayIndexFromTimeslot(timebox);
            isAllowed = processDay.equals(timeboxDay);
        }
        return isAllowed;
    }
}
