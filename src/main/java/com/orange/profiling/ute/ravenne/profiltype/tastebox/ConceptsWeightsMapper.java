package com.orange.profiling.ute.ravenne.profiltype.tastebox;

import java.io.IOException;

import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Mapper;

import com.orange.profiling.common.utils.FieldsUtils;
import com.orange.profiling.ute.ravenne.file.ConceptsWeights;

/**
 * Mapper implementation for processing user viewing data with weighted concepts.
 * This class processes input records containing user viewing information and
 * transforms them into a format suitable for concept weight aggregation.
 * 
 * Input Format:
 * Two supported formats:
 * 1. Legacy Format:
 *    [aid] [concepts] [timeslot] [live_duration] [vod_duration] [type] [duration]
 *    Example: "100000287 sujets/actualité,catégories/information,sujets/presse 5 2 2 LIVE 314"
 * 
 * 2. New Format:
 *    [aid] [timeslot] [weighted_concepts] [duration]
 *    Example: "123456789 d4t2 sujets/sujets=500,sujets/sport=750,sujets/rugby=1000 35"
 * 
 * Output Format:
 * - Key: aid-timeslot
 * - Value: weighted_concepts TAB total_duration
 * 
 * Key Features:
 * - Supports multiple input formats
 * - Combines LIVE and VOD durations
 * - Validates input format
 * - Tracks malformed inputs via counters
 * 
 * <AUTHOR>
 */
public class ConceptsWeightsMapper extends Mapper<Object, Text, Text, Text> {

    private static final String TAB = FieldsUtils.TAB;
    private static final String DASH = FieldsUtils.DASH;
    private static final String PIPE = FieldsUtils.PIPE;

    private Text outputKey = new Text();
    private Text outputValue = new Text();
    private ConceptsWeights conceptsWeights = new ConceptsWeights();

    /**
     * Processes each input record to extract user viewing data and concept weights.
     * The processing flow:
     * 1. Parses input line into ConceptsWeights object
     * 2. Validates format
     * 3. Extracts user ID, timeslot, concepts, and durations
     * 4. Combines live and VOD durations
     * 5. Outputs in format suitable for reducer aggregation
     * 
     * @param key Input key (unused)
     * @param value Input line containing user viewing data
     * @param context Context for output and counter tracking
     * @throws IOException If there are issues writing output
     * @throws InterruptedException If the mapping process is interrupted
     */
    @Override
    public final void map(final Object key, final Text value,
            final Context context) throws IOException, InterruptedException {
        // old Example : 100000287 sujets/actualité,catégories/information,sujets/presse
        // 5 2 2 LIVE 314
        // new Example : 123456789 d4t2
        // sujets/sujets=500,sujets/sport=750,sujets/rugby=1000 35
        conceptsWeights.setValue(value.toString());

        if (conceptsWeights.checkFormat()) {

            String aid = conceptsWeights.getField(ConceptsWeights.AID);
            String timeslot = conceptsWeights.getField(ConceptsWeights.TIMEBOX);
            String concepts = conceptsWeights.getField(ConceptsWeights.PONDERATED_CONCEPTS);
            Integer liveDuration = Integer.parseInt(conceptsWeights.getField(ConceptsWeights.LIVE_DURATION));
            Integer vodDuration = Integer.parseInt(conceptsWeights.getField(ConceptsWeights.VOD_DURATION));
            Integer totalDuration = liveDuration + vodDuration;

            outputKey.set(aid + PIPE + timeslot);
            outputValue.set(concepts + TAB + totalDuration);
            context.write(outputKey, outputValue);
        }
        else {
            context.getCounter(Counters.BAD_FORMAT).increment(1);
        }
    }
}
