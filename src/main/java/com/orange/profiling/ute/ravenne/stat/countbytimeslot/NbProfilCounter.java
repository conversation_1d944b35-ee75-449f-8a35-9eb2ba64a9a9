package com.orange.profiling.ute.ravenne.stat.countbytimeslot;

import java.io.IOException;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;

import org.apache.hadoop.mapreduce.Counter;
import org.apache.hadoop.mapreduce.Job;
import org.apache.hadoop.mapreduce.Mapper.Context;

import com.orange.profiling.common.mapred.UteCountersLogger;
import com.orange.profiling.common.utils.FieldsUtils;

public class NbProfilCounter {
    protected static final String COUNTER_NB_GENETIC_PROFILES_GROUP = "NB_GENETIC_PROFILES";
    protected static final String COUNTER_NB_GENETIC_PROFILES_FORMAT = "NB_GENETIC_PROFILES_%s";

    protected static final String COUNTER_NB_BY_CONCEPTS_GROUP = "NB_GENETIC_PROFILES_BY_NB_CONCEPTS";
    protected static final String COUNTER_NB_BY_CONCEPTS_FORMAT = "NB_GENETIC_PROFILES_BY_NB_CONCEPTS%s_%02d";

    protected static String[] KIBANA_GROUPS = {
            COUNTER_NB_GENETIC_PROFILES_GROUP,
            COUNTER_NB_BY_CONCEPTS_GROUP
    };

    private Context context;

    public NbProfilCounter(Context context) {
        this.context = context;
    }

    /** COUNTER : NB_GENETIC_PROFILS_TYPE
     * nombre de profils génétiques par type de timebox (w, , dp ou dt)
     *
     * @param context
     * @param timeBoxType
     */
    public void countNbProfilsByTimeboxType(String timeBoxType) {
        String counterNbProfils = String.format(COUNTER_NB_GENETIC_PROFILES_FORMAT, timeBoxType);
        context.getCounter(COUNTER_NB_GENETIC_PROFILES_GROUP, counterNbProfils).increment(1);
    }

    /** COUNTER : NB_GENETIC_PROFILES_BY_NB_CONCEPTS_TYPE_NB
     * nombre de profils génétiques par type de timebox (w, d, dp ou dt)
     * et par nombres de concepts (de 1 à 20)
     *
     * @param context
     * @param timeBoxType
     * @param nbConcepts
     */
    public void countNbProfilsByTimeboxTypeAndNbConcepts(String timeBoxType, int nbConcepts) {
        String counterNbConcepts = String.format(COUNTER_NB_BY_CONCEPTS_FORMAT, timeBoxType, nbConcepts);

        //on ne fait le décompte que pour les timeboxtype dt et dod, plus pour dp, d, w et wod
        if(timeBoxType.equals("dt") || timeBoxType.equals("dod") ){
            context.getCounter(COUNTER_NB_BY_CONCEPTS_GROUP, counterNbConcepts).increment(1);
        }
    }

    /** sending counters by nb concepts as syslog message.
     *  The nb concepts will be given as counter name to kibana
     */
    public static void sendKibanaCounterNbByConcepts(Job job)
            throws IOException {

        String counterGroup = COUNTER_NB_BY_CONCEPTS_GROUP;
        Iterator<Counter> iteCounters = job.getCounters()
                .getGroup(counterGroup).iterator();

        while (iteCounters.hasNext()) {
            Counter counter = iteCounters.next();
            UteCountersLogger logger = new UteCountersLogger();

            String timeboxTypeAndNbConcepts = counter.getName().replace(counterGroup, "");
            String[] splittedTimeboxTypeAndNbConcepts =
                    FieldsUtils.UNDERSCORE_PATTERN.split(timeboxTypeAndNbConcepts,2);
            if (splittedTimeboxTypeAndNbConcepts.length>1) {
                String timeboxType = splittedTimeboxTypeAndNbConcepts[0];
                String nbConcepts = splittedTimeboxTypeAndNbConcepts[1];

                logger.addType(counterGroup);
                logger.addName(nbConcepts);
                logger.addValueToJson("timeboxtype", timeboxType);
                logger.addCounter("value", counter.getValue());
                //a log for each counter. to make dashboards dynamically in kibana
                logger.sendJsonLog();
            }
        }
    }

    public static List<String> getGroups() {
        return Arrays.asList(KIBANA_GROUPS);
    }

}
