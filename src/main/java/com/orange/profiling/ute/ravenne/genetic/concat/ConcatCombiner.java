package com.orange.profiling.ute.ravenne.genetic.concat;

import java.io.IOException;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Reducer;
import com.orange.profiling.common.utils.FieldsUtils;
import com.orange.profiling.ute.ravenne.dao.TimeboxZapAggregation;

/**
 * <PERSON>cat<PERSON>omb<PERSON> optimizes the genetic concatenation job by performing partial aggregation.
 * 
 * This combiner serves two main purposes:
 * 1. For regular users (non-opt-out):
 *    - Passes through individual records without modification
 *    - Maintains data granularity for final processing
 * 
 * 2. For opt-out aggregation:
 *    - Performs partial aggregation of:
 *      * Concept weights and viewing ratios
 *      * Live channel viewing durations
 *      * VOD provider viewing durations
 *    - Reduces data volume before the reduce phase
 * 
 * Key Features:
 * - Selective aggregation based on user type
 * - Maintains counters for number of records
 * - Preserves data structure for final reduction
 * 
 * Output Format:
 * For opt-out users:
 * - Aggregated concept weights with counts
 * - Aggregated channel/provider durations with counts
 * For regular users:
 * - Original mapper output passed through
 * 
 * <AUTHOR>
 */
public class Concat<PERSON>omb<PERSON> extends Reducer<Text, Text, Text, Text> {
    public static final int KEY_AID_IDX = 0;
    public static final int KEY_TIMEBOX_IDX = 1;

    private ConcatHelper concatHelper = new ConcatHelper();
    private Text outValue = new Text();

    /*
     * (non-Javadoc)
     *
     * @see org.apache.hadoop.mapreduce.Reducer#reduce(KEYIN, java.lang.Iterable,
     * org.apache.hadoop.mapreduce.Reducer.Context)
     */
    @Override
    public final void reduce(final Text key, final Iterable<Text> values, final Context context)
            throws IOException, InterruptedException {

        concatHelper.initHelper(key);

        // do aggregation only for optout as there's should be only one value per aid-timebox...
        if (MainConcat.OPTOUT_AID.equals(concatHelper.getAid())) {
            concatHelper.aggregateProfilValues(values);
            writeAggregatedOptoutValues(key, context);
        }
        else {
            // Writes directly all value with standard aid
            for(Text value : values) {
                context.write(key, value);
            }
        }
    }

    private void writeAggregatedOptoutValues(final Text key, final Context context)
                    throws IOException, InterruptedException {

        writeConceptsAndRatio(key, context);
        writeLiveChannelDurations(key, context);
        writeOdProviderDurations(key, context);
    }

    private void writeConceptsAndRatio(final Text key, final Context context)
                    throws IOException, InterruptedException {

        TimeboxZapAggregation zapAggregation = concatHelper.getZapAggregation();
        if (concatHelper.getNbValues() > 0) {
            outValue.set(String.join(FieldsUtils.TAB, ConceptsWeightsMapper.CONCEPTS_MARK,
                    zapAggregation.getWeightedConceptsString(),
                    Long.toString(zapAggregation.getLiveDuration()),
                    Integer.toString(concatHelper.getNbValues())));
            context.write(key, outValue);
        }
    }

    private void writeLiveChannelDurations(final Text key, final Context context)
                    throws IOException, InterruptedException {

        TimeboxZapAggregation zapAggregation = concatHelper.getZapAggregation();
        if (concatHelper.getNbLives() > 0) {
            outValue.set(String.join(FieldsUtils.TAB, TimeboxZapAggregation.LIVE,
                    zapAggregation.getLiveChannelDurationString(),
                    Integer.toString(concatHelper.getNbLives())));
            context.write(key, outValue);
        }
    }

    private void writeOdProviderDurations(final Text key, final Context context)
                    throws IOException, InterruptedException {

        TimeboxZapAggregation zapAggregation = concatHelper.getZapAggregation();
        if (concatHelper.getNbOds() > 0) {
            outValue.set(String.join(FieldsUtils.TAB, TimeboxZapAggregation.VOD,
                    zapAggregation.getOdProviderDurationString(),
                    Integer.toString(concatHelper.getNbOds())));
            context.write(key, outValue);
        }
    }

}