package com.orange.profiling.ute.ravenne.stat.countbyprovider;

import java.io.IOException;
import java.util.regex.Pattern;

import org.apache.hadoop.io.NullWritable;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Reducer;

import com.orange.profiling.common.utils.FieldsUtils;
import com.orange.profiling.ute.ravenne.logger.RavenneKpiLogger;

public class CountByProviderReducer extends Reducer<Text, Text, NullWritable, Text> {

    public static final String PIPE = FieldsUtils.PIPE;
    public static final Pattern PIPE_PATTERN = FieldsUtils.PIPE_PATTERN;
    public static final String TAB = FieldsUtils.TAB;
    private static final double PERCENT = 100.0;

    private long totalNbAid = 1L;
    private long totalNbProfils = 0L;
    private RavenneKpiLogger kpiLogger;
    private Text outputValue = new Text();

    @Override
    public final void reduce(final Text key, final Iterable<Text> values, final Context context)
            throws IOException, InterruptedException {
        String[] keyParts = PIPE_PATTERN.split(key.toString());
        if (keyParts.length >= CountBestChannelsProvidersMapper.KEY_LENGTH) {

            String timebox = keyParts[CountBestChannelsProvidersMapper.KEY_TIMEBOX_IDX];
            String nbChannels = keyParts[CountBestChannelsProvidersMapper.KEY_NBCHANNEL_IDX];

            totalNbProfils = 0L;
            readValuesToUpdateTotalCount(values);

            if (totalNbProfils > 0) {
                writeOutput(context, timebox, nbChannels);
            }
        }
    }

    private void readValuesToUpdateTotalCount(final Iterable<Text> values) {
        for(Text value: values) {
            long num = FieldsUtils.getSafelyLong(value.toString(), 0L);
            totalNbProfils += num;
        }
    }

    private void writeOutput(final Context context, String timebox, String nbChannels)
            throws IOException, InterruptedException {
        long profilsPercent = Math.round(PERCENT*totalNbProfils/totalNbAid);

        outputValue.set(String.join(TAB, timebox, nbChannels,
                Long.toString(totalNbProfils), Long.toString(profilsPercent)));
        context.write(NullWritable.get(), outputValue);

        kpiLogger.sendDeltaCountByProvider(timebox, nbChannels, totalNbProfils, profilsPercent);
    }

    @Override
    protected void setup(Context context) throws IOException, InterruptedException {
        String totalNbAidStr = context.getConfiguration().get(MainCountByProvider.CONF_NB_AID);
        totalNbAid = FieldsUtils.getSafelyLong(totalNbAidStr, 1L);
        String processDate = context.getConfiguration().get(MainCountByProvider.CONF_PROCESS_DATE);
        buildKpiLogger(processDate);
    }

    private void buildKpiLogger(String processDate) {
        if (kpiLogger == null) {
            kpiLogger = new RavenneKpiLogger(processDate);
        }
    }

    /** For test purpose only : set kpi logger with mockLogger
     *
     * @param mockLogger
     */
    public void setKpiLogger(RavenneKpiLogger mockLogger) {
        kpiLogger = mockLogger;
    }

}
