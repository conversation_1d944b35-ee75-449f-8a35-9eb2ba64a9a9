package com.orange.profiling.ute.ravenne.stat.countconcepts;

import java.io.IOException;
import java.util.regex.Pattern;

import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Reducer;

import com.orange.profiling.common.utils.FieldsUtils;

public class CountConcept<PERSON>ombiner  extends Reducer<Text, Text, Text, Text> {

    private static final String TAB = FieldsUtils.TAB;
    private static final Pattern TAB_PATTERN = FieldsUtils.TAB_PATTERN;

    private Text outputValue = new Text();

    @Override
    protected void reduce(Text key, Iterable<Text> values, Context context) throws IOException, InterruptedException {
        long totalToday = 0L;
        String yesterdayCount = "";

        for(Text value: values) {
            String[] valueParts = TAB_PATTERN.split(value.toString());
            if (valueParts.length > 1) {
                String outId = valueParts[0];
                String outNum = valueParts[1];
                if (ConceptsWeightsMapper.OUT_ID_TODAY.equals(outId)) {
                    long num = 0L;
                    try {
                        num = Long.parseLong(outNum);
                    }
                    catch(Exception e) {
                        // not important and should not happen
                    }
                    totalToday += num;
                }
                else if (PreviousCountConceptsMapper.OUT_ID_YESTERDAY.equals(outId)) {
                    // Yesterday value comes from one line in one file
                    // ence we should not have several values for the same key
                    yesterdayCount = outNum;
                }
            }
        }

        if (totalToday > 0) {
            outputValue.set(ConceptsWeightsMapper.OUT_ID_TODAY+TAB+Long.toString(totalToday));
            context.write(key, outputValue);
        }

        if (!yesterdayCount.isEmpty()) {
            outputValue.set(PreviousCountConceptsMapper.OUT_ID_YESTERDAY+TAB+yesterdayCount);
            context.write(key, new Text(outputValue));
        }
    }
}
