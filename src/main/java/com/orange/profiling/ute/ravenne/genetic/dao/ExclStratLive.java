package com.orange.profiling.ute.ravenne.genetic.dao;

import com.google.gson.ExclusionStrategy;
import com.google.gson.FieldAttributes;

public class ExclStratLive implements ExclusionStrategy {

    public boolean shouldSkipClass(Class<?> arg0) {
        return false;
    }

    public boolean shouldSkipField(FieldAttributes f) {

        return (f.getDeclaringClass() == ProfileJson.class && f.getName().equals("thChCatchup"));
    }

}