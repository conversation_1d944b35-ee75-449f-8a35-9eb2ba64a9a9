package com.orange.profiling.ute.ravenne.profiltype.profils.dao;

import java.io.Serializable;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.Set;
import java.util.stream.Collectors;

public class LigneThematique implements Serializable {
    private static final long serialVersionUID = 7550548725269249708L;
    private static final int HASH_PRIME = 31;
    private static final int MAX_NB_TOP_CONCEPTS = 3;

    private int index;
    private String name;
    private Set<Integer> includes = new HashSet<>();
    private ConceptsVector score;
    private LinkedHashSet<String> topConcepts = new LinkedHashSet<>();

    public LigneThematique(int index, String name) {
        this.index = index;
        this.name = name;
    }

    /**
     * @return the index
     */
    public int getIndex() {
        return index;
    }

    /**
     * @param index the index to set
     */
    public void setIndex(int index) {
        this.index = index;
    }

    /**
     * @return the name
     */
    public String getName() {
        return name;
    }

    /**
     * @param name the name to set
     */
    public void setName(String name) {
        this.name = name;
    }

    public void setScoreVector(ConceptsVector vector) {
        this.score = vector;
        setTopConcepts(vector);
    }

    private void setTopConcepts(ConceptsVector vector) {

        int nbTopConcepts = getNbConceptsWithPostiveHeights(vector)-1;
        if (nbTopConcepts > MAX_NB_TOP_CONCEPTS) {
            nbTopConcepts = MAX_NB_TOP_CONCEPTS;
        }
        if (nbTopConcepts < 1) {
            nbTopConcepts = 1;
        }

        this.topConcepts = vector.getConcepts()
                    .stream()
                    // compare c2 weight to c1 weight means sort by descendant weight (reverse natural order)
                    .sorted((c1,c2) -> vector.getConceptWeight(c2).compareTo(vector.getConceptWeight(c1)))
                    .limit(nbTopConcepts)
                    .collect(Collectors.toCollection(LinkedHashSet::new));
    }

    private int getNbConceptsWithPostiveHeights(ConceptsVector vector) {
        int nb = 0;
        for(String concept: vector.getConcepts()) {
            if (vector.getConceptWeight(concept)>0) {
                nb++;
            }
        }
        return nb;
    }

    public LinkedHashSet<String> getTopConcepts() {
        return topConcepts;
    }

    public ConceptsVector getScoreVector() {
        return score;
    }

    public void addInclude(LigneThematique lt) {
        includes.add(lt.getIndex());
    }

    public void addInclude(Integer index) {
        includes.add(index);
    }

    public boolean includes(LigneThematique lt) {
        if (lt == null) {
            return false;
        }
        return includes.contains(lt.getIndex());
    }

    @Override
    public String toString() {
        return index+":"+name+" ; "
                + "score = "+(score != null ? score.toString() : "none")+" ; "
                + "includes = "+ includes.toString();
    }

    /* (non-Javadoc)
     * @see java.lang.Object#hashCode()
     */
    @Override
    public int hashCode() {
        int result = 1;
        result = HASH_PRIME * result + ((includes == null) ? 0 : includes.hashCode());
        result = HASH_PRIME * result + index;
        result = HASH_PRIME * result + ((name == null) ? 0 : name.hashCode());
        result = HASH_PRIME * result + ((score == null) ? 0 : score.hashCode());
        return result;
    }

    /* (non-Javadoc)
     * @see java.lang.Object#equals(java.lang.Object)
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (!(obj.getClass() == this.getClass())) {
            return false;
        }
        LigneThematique other = (LigneThematique) obj;
        if (includes == null) {
            if (other.includes != null) {
                return false;
            }
        }
        else if (!includes.equals(other.includes)) {
            return false;
        }
        if (index != other.index) {
            return false;
        }
        if (name == null) {
            if (other.name != null) {
                return false;
            }
        }
        else if (!name.equals(other.name)) {
            return false;
        }
        if (score == null) {
            if (other.score != null) {
                return false;
            }
        }
        else if (!score.equals(other.score)) {
            return false;
        }
        return true;
    }

}
