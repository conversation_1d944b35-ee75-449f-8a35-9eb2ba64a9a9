package com.orange.profiling.ute.ravenne.profiltype.profils.dao;

import static java.util.stream.Collectors.joining;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import com.orange.profiling.common.utils.FieldsUtils;
import com.orange.profiling.common.utils.SimilarityCosinus;

/**
 * <AUTHOR>
 */
public class ConceptsVector implements Serializable {

    private static final long serialVersionUID = -825575713176463389L;

    private Map<String, Double> conceptsWeights;

    public ConceptsVector() {
        conceptsWeights = new HashMap<>();
    }

    public ConceptsVector(Map<String, Double> conceptsWeights) {
        this.conceptsWeights = conceptsWeights;
    }

    public void addConceptWeight(String key, Double value) {
        conceptsWeights.put(key, value);
    }

    public Map<String, Double> getPatternList() {
        return conceptsWeights;
    }

    @Override
    public String toString() {
        return conceptsWeights.entrySet()
                .stream()
                .map(e -> e.getKey() + FieldsUtils.EQUAL + (e.getValue()) )
                .collect(joining(FieldsUtils.COMMA));
    }

    public Double computeSimilarity(Map<String, Double> weightedConcepts) {

        HashMap<String, Double> compareMap = new HashMap<>();

        // On ne retient des concepts du client que ceux qui sont en commun avec le
        // vector a tester.
        for (Map.Entry<String, Double> entry : weightedConcepts.entrySet()) {
            String concept = entry.getKey();
            Double value = entry.getValue();
            if (conceptsWeights.containsKey(concept)) {
                compareMap.put(concept, value);
            }
        }

        if (compareMap.isEmpty()) {
            return 0D;
        }

        // Calcul de la similarite cosinus
        return SimilarityCosinus.cosineSimilarity(conceptsWeights, compareMap);
    }

    public Set<String> getConcepts() {
        return conceptsWeights.keySet();
    }

    public Double getConceptWeight(String concept) {
        if (conceptsWeights.containsKey(concept)) {
            return conceptsWeights.get(concept);
        }
        return 0.0;
    }

}
