package com.orange.profiling.ute.ravenne.joinOeuvresConcepts;

import java.io.IOException;
import java.util.Arrays;
import java.util.stream.Collectors;

import com.orange.profiling.common.exceptions.FailedJobException;
import com.orange.profiling.common.utils.FileFilterVodProgbymac;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Reducer;
import org.joda.time.DateTime;

import com.orange.profiling.common.mapred.MosWriter;
import com.orange.profiling.common.utils.DatesUtils;
import com.orange.profiling.common.utils.FieldsUtils;
import com.orange.profiling.common.utils.OrangeTimeslotUtils;
import com.orange.profiling.ute.ravenne.dao.TimeboxZapAggregation;
import com.orange.profiling.ute.ravenne.file.Filtered;


public class JoinOeuvresConceptsReducer extends Reducer<Text, Text, Text, Text> {

    private Text outputKey = new Text();  // Nouveau
    private Text outputValue = new Text();

    public void reduce(Text key, Iterable<Text> values, Context context)
            throws IOException, InterruptedException {

        String markerName = "";
        String concepts = "";

        for (Text value : values) {
            String val = value.toString();
            if (val.startsWith("CONCEPTS:")) {
                concepts = val.substring(9); // Enlève "CONCEPTS:"
            } else if (val.startsWith("MARKER:")) {
                markerName = val.substring(7); // Enlève "MARKER:"
            }
        }

        if (!markerName.isEmpty() && !concepts.isEmpty()) {
            outputKey.set(markerName);
            outputValue.set(concepts);
            context.write(outputKey, outputValue);
        }
    }
}
