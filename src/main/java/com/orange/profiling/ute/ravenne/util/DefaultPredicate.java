package com.orange.profiling.ute.ravenne.util;

import java.io.BufferedReader;
import java.io.IOException;
import java.util.HashSet;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.hadoop.conf.Configuration;
import org.apache.log4j.Logger;

import com.orange.profiling.common.utils.FsUtils;

public class DefaultPredicate implements MyPredicate {
    private static final long serialVersionUID = 1L;
    private static final Logger LOGGER = Logger.getLogger(DefaultPredicate.class);

    /**
     * words of type ACCEPTED, format ACCEPTED,path
     */
    private Set<String> authorizedConcepts = new HashSet<>();

    /**
     * words of type EXCLUDED, format EXCLUDED,path
     */
    private Set<String> excludedConcepts = new HashSet<>();

    private static final String ACCEPTED_REGEX = "ACCEPTED,(.*?)";
    private static final Pattern ACCEPTED_PATTERN = Pattern.compile(ACCEPTED_REGEX);

    private static final String EXCLUDED_REGEX = "EXCLUDED,(.*?)";
    private static final Pattern EXCLUDED_PATTERN = Pattern.compile(EXCLUDED_REGEX);

    @Override
    public boolean test(String concept) {
        String[] taxoTokenConcepts = concept.split("/");
        String taxoFirst = taxoTokenConcepts[0];

        if (taxoFirst.isEmpty()) {
            return false;
        }

        String taxoSecond = "";
        if (taxoTokenConcepts.length > 1) {
            taxoSecond = taxoTokenConcepts[1];
        }
        String toSearch = taxoFirst+":"+taxoSecond+":";
        boolean isInAccepted = false;
        boolean isInExcluded = false;

        if (authorizedConcepts.contains(taxoFirst + ":") || authorizedConcepts.contains(toSearch)) {
            isInAccepted = true;
        }
        if (excludedConcepts.contains(taxoFirst + ":") || excludedConcepts.contains(toSearch)) {
            isInExcluded = true;
        }
        return (isInAccepted && !isInExcluded);
    }

    @Override
    public void setUp(Configuration cfg) {
        authorizedConcepts = new HashSet<>();
        excludedConcepts = new HashSet<>();

        FsUtils fsUtils = null;
        try {
            fsUtils = new FsUtils(cfg);
        }
        catch(IOException e) {
            LOGGER.error("Error reading accepted/excluded concepts : can't get file system", e);
            return;
        }

        String catalogConceptsPath = cfg.get(MyPredicate.PREDICATE_CLASS_ARG);
        try (BufferedReader fb = fsUtils.getReaderForFile(catalogConceptsPath)) {

            readCatalogConcepts(fb);

        }
        catch (IOException e) {
            LOGGER.error("Error reading accepted/excluded concepts : can't read file "+catalogConceptsPath, e);
        }
    }

    private void readCatalogConcepts(BufferedReader fb) throws IOException {
        Matcher acceptedMatcher;
        Matcher excludedMatcher;

        String s = fb.readLine();
        while (s != null) {

            acceptedMatcher = ACCEPTED_PATTERN.matcher(s);
            if (acceptedMatcher.matches()) {
                addAuthorizedPattern(acceptedMatcher.group(1));
            }
            else {
                excludedMatcher = EXCLUDED_PATTERN.matcher(s);
                if (excludedMatcher.matches()) {
                    addExcludedPattern(excludedMatcher.group(1));
                }
            }

            s = fb.readLine();
        }
    }

    protected void addAuthorizedPattern(String pattern) {
        authorizedConcepts.add(pattern);
    }

    protected void addExcludedPattern(String pattern) {
        excludedConcepts.add(pattern);
    }
}