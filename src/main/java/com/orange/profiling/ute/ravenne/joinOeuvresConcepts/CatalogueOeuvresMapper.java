package com.orange.profiling.ute.ravenne.joinOeuvresConcepts;

import java.io.IOException;

import com.orange.profiling.ute.ravenne.file.CatalogueOeuvres;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Mapper;

import com.orange.profiling.common.file.generated.Progbymac;
import com.orange.profiling.ute.ravenne.dao.TimeboxZapAggregation;

public class CatalogueOeuvresMapper extends Mapper<Object, Text, Text, Text> {

    private CatalogueOeuvres catalogueOeuvre = new CatalogueOeuvres();
    private Text outputKey = new Text();
    private Text outputValue = new Text();

    @Override
    public final void map(final Object key, final Text value,
                          final Context context) throws IOException, InterruptedException {

        catalogueOeuvre.setValue(value.toString());
        String idOeuvre = catalogueOeuvre.getField(CatalogueOeuvres.ID_OEUVRE);
        String concepts = catalogueOeuvre.getField(CatalogueOeuvres.CONCEPTS);

        outputKey.set(idOeuvre);
        outputValue.set("CONCEPTS:" + concepts);
        context.write(outputKey, outputValue);
    }
}
