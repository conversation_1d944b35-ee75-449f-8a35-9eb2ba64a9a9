package com.orange.profiling.ute.ravenne.util;

import org.apache.hadoop.conf.Configuration;

public class PlacePredicate implements MyPredicate {

    /**
     *
     */
    private static final long serialVersionUID = 1L;

    @Override
    public boolean test(String concept) {
        return concept.contains("lieu de l'action");
    }

    @Override
    public void setUp(Configuration cfg) {
        // Nothing to do

    }

}
