package com.orange.profiling.ute.ravenne.stat.countbyprovider;

import java.io.IOException;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Reducer;

public class CountByProviderCombiner extends Reducer<Text, Text, Text, Text> {

    private Text outputValue = new Text();

    @Override
    protected void reduce(Text key, Iterable<Text> values, Context context) throws IOException, InterruptedException {
        long totalNbProfils = 0L;
        for (Text value : values) {
            long num = 0L;
            try {
                num = Long.parseLong(value.toString());
            }
            catch (Exception e) {
                // not important and should not happen
            }
            totalNbProfils += num;
        }

        if (totalNbProfils > 0) {
            outputValue.set(Long.toString(totalNbProfils));
            context.write(key, outputValue);
        }
    }
}
