package com.orange.profiling.ute.ravenne.util;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.Collectors;

public class TopValuedKeySelector {

    private int topN;

    public TopValuedKeySelector(int topN) {
        this.topN = topN;
    }

    public Map<String, Long> getTopKeys(Map<String, Long> keyValueMap) {
        if (keyValueMap.isEmpty()) {
            return keyValueMap;
        }

        Map<String, Long> keyValueMapSorted = sorKeysByValue(keyValueMap);

        return keyValueMapSorted.entrySet().stream()
                .limit(topN)
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue,
                            (oldValue, newValue) -> oldValue+newValue, LinkedHashMap::new));
    }

    public Map<String,Long> sorKeysByValue(Map<String, Long> keyValueMap) {

        return keyValueMap.entrySet()
                .stream()
                .sorted((e1, e2) -> compareByValueReverseOrderThenByKey(e1,e2))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (e1, e2) -> e1, LinkedHashMap::new));
    }

    public int compareByValueReverseOrderThenByKey(Map.Entry<String, Long> a, Map.Entry<String, Long> b) {
        // First compare value in reverse order (b compare to a instead of a compare to b)
        int compareValueReverseOrder = b.getValue().compareTo(a.getValue());
        if (compareValueReverseOrder != 0) {
            return compareValueReverseOrder;
        }
        else {
            // If value are equals, compare key in alphabetical order
            return a.getKey().compareTo(b.getKey());
        }
    }

}
