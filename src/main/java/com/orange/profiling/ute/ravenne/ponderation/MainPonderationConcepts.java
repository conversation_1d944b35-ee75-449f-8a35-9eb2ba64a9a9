package com.orange.profiling.ute.ravenne.ponderation;

import java.io.IOException;
import java.util.Arrays;
import java.util.Iterator;

import org.apache.hadoop.fs.FileSystem;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Counter;
import org.apache.hadoop.mapreduce.Job;
import org.apache.hadoop.mapreduce.JobStatus;
import org.apache.hadoop.mapreduce.lib.input.FileInputFormat;
import org.apache.hadoop.mapreduce.lib.input.MultipleInputs;
import org.apache.hadoop.mapreduce.lib.input.TextInputFormat;
import org.apache.hadoop.mapreduce.lib.output.FileOutputFormat;
import org.apache.hadoop.mapreduce.lib.output.LazyOutputFormat;
import org.apache.hadoop.mapreduce.lib.output.MultipleOutputs;
import org.apache.hadoop.mapreduce.lib.output.TextOutputFormat;
import org.apache.log4j.Logger;

import com.orange.profiling.common.exceptions.FailedJobException;
import com.orange.profiling.common.file.generated.Optin;
import com.orange.profiling.common.mapred.UteConfiguration;
import com.orange.profiling.common.mapred.UteCountersLogger;
import com.orange.profiling.common.mapred.UteCountersWriter;
import com.orange.profiling.common.optin.OptinMapper;
import com.orange.profiling.common.utils.FieldsUtils;
import com.orange.profiling.common.utils.HadoopGCSFileSystemFactory;
import com.orange.profiling.ute.ravenne.util.MyPredicate;

/**
 * The Ponderation component is responsible for analyzing and weighting user viewing data
 * by aggregating views based on aid (Account ID) and timeslots. This component is part of
 * the Ravenne profiling system for Orange's UTE platform.
 *
 * <h2>Key Features:</h2>
 * <ul>
 *   <li>Concept Weighting: Aggregates and weights concepts from viewed programs based on:
 *     <ul>
 *       <li>View duration</li>
 *       <li>Days since the view occurred</li>
 *       <li>Hierarchical concept relationships (parent-child)</li>
 *     </ul>
 *   </li>
 *   <li>Duration Tracking:
 *     <ul>
 *       <li>Aggregates LIVE and VOD viewing durations</li>
 *       <li>Tracks duration by live channels and VOD providers</li>
 *       <li>Computes VOD/LIVE ratio</li>
 *     </ul>
 *   </li>
 *   <li>Program Statistics: Maintains count of viewed programs</li>
 * </ul>
 *
 <h2>Weighting Algorithm:</h2>
 * The component implements a hierarchical weighting system where:
 * <ol>
 *   <li>Initial weights are assigned only to leaf concepts</li>
 *   <li>Each leaf concept shares a portion of its weight with its parent</li>
 *   <li>Parent concepts accumulate weights from all their children</li>
 *   <li>The process continues up the hierarchy until reaching the root</li>
 * </ol>
 *
 * <h2>Output Structure:</h2>
 * The component generates several output files organized in different directories:
 * <ul>
 *   <li><b>nbprogram/</b> - Program count statistics
 *     <ul>
 *       <li>Format: aid => dxty TAB nbprogram</li>
 *     </ul>
 *   </li>
 *   <li><b>concepts/</b> - Weighted concept data for different time periods
 *     <ul>
 *       <li>dt-r-nnnnn: Daily timeslot data (dxty)</li>
 *       <li>dp-r-nnnnn: Period data (dxpz)</li>
 *       <li>d-r-nnnnn: Daily data (dx)</li>
 *       <li>w-r-nnnnn: Weekly data (w)</li>
 *       <li>Format: aid => timeperiod TAB ponderatedConcepts TAB ratioVodLive</li>
 *     </ul>
 *   </li>
 *   <li><b>bestchannelsproviders/</b> - Channel and provider statistics
 *     <ul>
 *       <li>LIVE/: Channel viewing data</li>
 *       <li>VOD/: Provider viewing data</li>
 *       <li>Format: aid => timeperiod TAB channel/provider TAB zapDuration TAB LIVE/VOD</li>
 *     </ul>
 *   </li>
 * </ul>
 *
 * <h2>Usage:</h2>
 * The job requires the following arguments:
 * <ol>
 *   <li>Input filtered data path</li>
 *   <li>Process date</li>
 *   <li>Begin yearweek</li>
 *   <li>End yearweek</li>
 *   <li>Predicate class</li>
 *   <li>Catalog concept filter</li>
 *   <li>Concept filter mapping table path</li>
 *   <li>Optin calculation input</li>
 *   <li>GDPR flag value</li>
 *   <li>Output directory</li>
 * </ol>
 *
 * @see ConceptPonderator For the concept weighting implementation
 * @see PonderationConceptsReducer For the main aggregation and processing logic
 * @see FilteredInputMapper For the initial data processing
 */
public class MainPonderationConcepts {
    private static final Logger LOGGER = Logger.getLogger(MainPonderationConcepts.class);

    private static final int ARG_LENGTH = 10;
    private static final int INPUT_FILTERED_IDX = 0;
    private static final int PARAM_PROCESS_DATE_IDX = 1;
    private static final int PARAM_BEGIN_YEARWEEK_IDX = 2;
    private static final int PARAM_END_YEARWEEK_IDX = 3;
    private static final int INPUT_PREDICATE_CLASS_IDX = 4;
    private static final int INPUT_CATALOG_CONCEPT_FILTER_IDX = 5;
    private static final int INPUT_CONCEPT_FILTER_MAPPING_TABLE_PATH = 6;
    private static final int INPUT_OPTIN_CALCULATION = 7;
    private static final int INPUT_GDPR_FLAG_VALUE = 8;
    private static final int OUTPUT_DIR_IDX = 9;
    private static final int TOP_CONCEPTS_IDX = 10;
    private static final int TOP_LIVE_CHANNELS_IDX = 11;
    private static final int TOP_OD_PROVIDERS_IDX = 12;


    private static final int NB_REDUCER = 250;

    private static final String JOB_NAME = "ute.ravenne.ponderation.concepts";

    private static final String[] KIBANA_GROUPS = {
            PonderationConceptsReducer.NB_PROGRAM_COUNTER_GROUP,
            PonderationConceptsReducer.NB_PROGRAM_COUNTER_TIMESLOT
    };

    public static final String OUT_CONCEPTS_WEIGHTS = "conceptsweights";
    public static final String OUT_NB_PROGRAM = "nbprogram";
    public static final String OUT_BEST_CHANNELS_PROVIDERS = "bestchannelsproviders";

    /**
     * @param args
     *            args
     * @throws IOException
     *             IOException
     * @throws InterruptedException
     *             InterruptedException
     * @throws ClassNotFoundException
     *             ClassNotFoundException
     */
    public static void main(final String[] args) throws IOException,
            InterruptedException, ClassNotFoundException, FailedJobException {

        if (args.length < ARG_LENGTH) {
            String mess = "Takes " + ARG_LENGTH
                    + " arguments : inputPathRavenneFiltered processDate beginYearWeek endYearWeek"
                    + " predicateClass catalogConcepts filterMappingTable " +
                    " inputOptinCalculation gdprFlagValue outputDir"
                    + " [topConcepts topLiveChannels topVodProviders]";
            LOGGER.error(mess);
            throw new IllegalArgumentException(mess);
        }

        Path inputPathRavenneFiltered = new Path(args[INPUT_FILTERED_IDX]);

        String processDate = args[PARAM_PROCESS_DATE_IDX];
        String beginYearSlashWeek = args[PARAM_BEGIN_YEARWEEK_IDX];
        String endYearSlashWeek = args[PARAM_END_YEARWEEK_IDX];

        String predicateClass = args[INPUT_PREDICATE_CLASS_IDX];
        String catalogPath = args[INPUT_CATALOG_CONCEPT_FILTER_IDX];
        String conceptFilterMappingTablePath = args[INPUT_CONCEPT_FILTER_MAPPING_TABLE_PATH];

        Path outputDir = new Path(args[OUTPUT_DIR_IDX]);

        Path inputOptinCalculation =  new Path(args[INPUT_OPTIN_CALCULATION]);
        String inputGdprFlagValue = args[INPUT_GDPR_FLAG_VALUE];

        UteConfiguration conf = new UteConfiguration();
        conf.set(FilteredInputFileFilter.MIN_DATE, beginYearSlashWeek);
        conf.set(FilteredInputFileFilter.MAX_DATE, endYearSlashWeek);
        conf.set(FilteredInputFileFilter.OUTPUT_DIR_PATH,args[OUTPUT_DIR_IDX]);

        conf.set(FilteredInputMapper.PROCESS_DATE, processDate);
        conf.set(FilteredInputMapper.PREDICATE_CLASS, predicateClass);
        conf.set(MyPredicate.PREDICATE_CLASS_ARG, catalogPath);
        conf.set(FilteredInputMapper.CONCEPT_FILTER_MAPPING_TABLE, conceptFilterMappingTablePath);

        // Configure OptinTVMapper
        conf.set(OptinPonderationMapper.KEY_TYPE, Integer.toString(Optin.AID_HASH));
        conf.set(OptinPonderationMapper.FLAG_VALUE, inputGdprFlagValue);

        setOptionalTopConf(conf, PonderationConceptsReducer.CONF_TOP_CONCEPTS, TOP_CONCEPTS_IDX, args);
        setOptionalTopConf(conf, PonderationConceptsReducer.CONF_TOP_LIVE_CHANNELS, TOP_LIVE_CHANNELS_IDX, args);
        setOptionalTopConf(conf, PonderationConceptsReducer.CONF_TOP_OD_PROVIDERS, TOP_OD_PROVIDERS_IDX, args);

        // Create job
        Job job = Job.getInstance(conf, JOB_NAME);
        job.setJarByClass(MainPonderationConcepts.class);
        job.setNumReduceTasks(NB_REDUCER);

        // Setup MapReduce
        job.setMapperClass(FilteredInputMapper.class);
        job.setReducerClass(PonderationConceptsReducer.class);

        // Specify key / value
        job.setOutputKeyClass(Text.class);
        job.setOutputValueClass(Text.class);

        // Input
        MultipleInputs.addInputPath(job, inputPathRavenneFiltered, TextInputFormat.class, FilteredInputMapper.class);
        MultipleInputs.addInputPath(job, inputOptinCalculation, TextInputFormat.class, OptinPonderationMapper.class);

        // Date filter
        FileInputFormat.setInputPathFilter(job, FilteredInputFileFilter.class);

        // Output
        FileOutputFormat.setOutputPath(job, outputDir);
        LazyOutputFormat.setOutputFormatClass(job, TextOutputFormat.class);
        MultipleOutputs.addNamedOutput(job, OUT_CONCEPTS_WEIGHTS, TextOutputFormat.class, Text.class, Text.class);
        MultipleOutputs.addNamedOutput(job, OUT_NB_PROGRAM, TextOutputFormat.class, Text.class, Text.class);
        MultipleOutputs.addNamedOutput(job, OUT_BEST_CHANNELS_PROVIDERS, TextOutputFormat.class, Text.class, Text.class);

        // Delete output if exists
        HadoopGCSFileSystemFactory hdfsFactory = new HadoopGCSFileSystemFactory();
        FileSystem hdfs = hdfsFactory.get(outputDir,conf);
        if (hdfs.exists(outputDir)) {
            hdfs.delete(outputDir, true);
        }

        // Execute job
        boolean succeeded = job.waitForCompletion(true);
        if (!succeeded) {
            JobStatus st = job.getStatus();
            throw new FailedJobException(JOB_NAME + " : " + st.getFailureInfo());
        }

        // Statistics
        UteCountersWriter.writeAlternateCounters(job, hdfs, outputDir, Arrays.asList(KIBANA_GROUPS));
        sendKibanaCounterNbPrograms(PonderationConceptsReducer.NB_PROGRAM_COUNTER_TIMESLOT, job);
    }

    private static void setOptionalTopConf(UteConfiguration conf, String confname, int index, String[] args) {
        if (args.length > index) {
            conf.set(confname, args[index]);
        }
    }

    /** sending counters nb program by timeslot and categories.
     *  The timeslot is given in timeslot field, the category in name field and the nbprogram is the value
     */
    private static void sendKibanaCounterNbPrograms(String counterGroup, Job job)
            throws IOException {
        Iterator<Counter> iteCounters = job.getCounters()
                .getGroup(counterGroup).iterator();

        while (iteCounters.hasNext()) {
            Counter counter = iteCounters.next();
            UteCountersLogger logger = new UteCountersLogger();

            String timeslotAndCategory = counter.getName().replace(counterGroup, "");
            String[] splittedTimeslotAndCategory =
                    FieldsUtils.UNDERSCORE_PATTERN.split(timeslotAndCategory,2);
            if (splittedTimeslotAndCategory.length>1) {
                String timeslot = splittedTimeslotAndCategory[0];
                String category = splittedTimeslotAndCategory[1];
                logger.addType(counterGroup);
                logger.addName(category);
                logger.addValueToJson("timeslot", timeslot);
                logger.addCounter("value", counter.getValue());
                //a log for each counter. to make dashboards dynamically in kibana
                logger.sendJsonLog();
            }
        }
    }


}
