package com.orange.profiling.ute.ravenne.genetic.pnssend;

import java.io.IOException;

import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.conf.Configured;
import org.apache.hadoop.fs.FileSystem;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.fs.PathFilter;
import org.apache.log4j.Logger;
import org.joda.time.DateTime;
import com.orange.profiling.common.utils.DatesUtils;
import com.orange.profiling.common.utils.FieldsUtils;
import com.orange.profiling.common.utils.FsUtils;

/**
 * FileFilterRavenneGenetic implements a sophisticated path filtering mechanism for Ravenne genetic profiles.
 * 
 * This filter is designed to select input files for genetic profile processing based on:
 * 1. File Path Structure:
 *    private/generated/ute/ravenne/filtered/[YearOfWeek]/[WeekOfYear]/selected/part-r-NNNNN
 * 
 * 2. Filtering Criteria:
 *    - Date Range: Files must fall within configured min/max dates
 *    - Day of Week: Files must match the target processing day
 *    - File Type: Must match configured file type pattern
 * 
 * Configuration Parameters:
 * - minDate: Start of the date range (YYYYMMDD format)
 * - maxDate: End of the date range (YYYYMMDD format)
 * - fileType: Pattern to match in file paths
 * 
 * Key Features:
 * - Directory traversal support
 * - Date-based filtering using JodaTime
 * - Configurable date ranges and file types
 * - Robust error handling and logging
 * 
 * Usage Example:
 * Configuration conf = new Configuration();
 * conf.set(MIN_DATE, "20210101");
 * conf.set(MAX_DATE, "20210131");
 * conf.set(FILE_TYPE, "genetic");
 * FileFilterRavenneGenetic filter = new FileFilterRavenneGenetic();
 * filter.setConf(conf);
 * 
 * Note: The filter automatically extracts the day of week from the maxDate
 * parameter and uses it for filtering. This ensures consistency in weekly
 * processing schedules.
 */
public class FileFilterRavenneGenetic extends Configured implements PathFilter {

    private static final Logger LOGGER = Logger.getLogger(FileFilterRavenneGenetic.class);

    private static final int DATE_INDEX_FROM_END = 3;

    public static final String MIN_DATE = "minDate";
    public static final String MAX_DATE = "maxDate";
    public static final String FILE_TYPE = "fileType";

    private DateTime beginRange;
    private DateTime endRange;
    private int weekDay;
    private String fileType;
    private FileSystem fs;
    Configuration conf;

    /*
     * (non-Javadoc)
     *
     * @see org.apache.hadoop.fs.PathFilter#accept(org.apache.hadoop.fs.Path)
     */
    @Override
    public final boolean accept(final Path path) {

        try {
            this.fs = new FsUtils(path, this.conf).getFs();
            if (fs != null && fs.isDirectory(path)) {
                return true;
            }
        }
        catch (IOException e) {
            LOGGER.error(e.getMessage(), e);
            return false;
        }

        if (path.toString().contains(fileType)) {

            try {
                String[] csv = FieldsUtils.SLASH_PATTERN.split(path.toString());
                String date = csv[csv.length - DATE_INDEX_FROM_END];

                DateTime fileDate = DatesUtils.DF_YYYYMMDD.parseDateTime(date);

                if ( weekDay == fileDate.getDayOfWeek()
                        && DatesUtils.isBetweenInclusive(fileDate, beginRange, endRange) ) {

                    LOGGER.info("Process " + path.toString());
                    return true;
                }
                else {
                    return false;
                }
            }
            catch (Exception e) {
                return false;
            }
        }
        else {
            LOGGER.info("Process " + path.toString());
            return true;
        }
    }

    @Override
    public void setConf(final Configuration config) {
        if (config != null) {
            // begin
            String minDate = config.get(MIN_DATE);
            beginRange = DatesUtils.DF_YYYYMMDD.parseDateTime(minDate);

            // end
            String maxDate = config.get(MAX_DATE);
            endRange = DatesUtils.DF_YYYYMMDD.parseDateTime(maxDate);

            weekDay = endRange.getDayOfWeek();

            // file type
            fileType = config.get(FILE_TYPE);

            try {
                // File system
                fs = FileSystem.get(config);
            }
            catch(IOException e) {
                LOGGER.error("Could not get fs with config",e);
            }
        }
    }
}
