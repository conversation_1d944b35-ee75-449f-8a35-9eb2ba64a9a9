package com.orange.profiling.ute.ravenne.logger;

import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;

import com.orange.profiling.common.mapred.UteLogger;

/** Sends Ravenne specific Kpi to kibana
 */
public class RavenneKpiLogger  extends UteLogger {
    private static final String CSTART_FIELD = "cStart";
    private String cStart;

    public static final String FORMAT_YYYYMMDD = "yyyyMMdd";
    public static final DateTimeFormatter DF_YYYYMMDD = DateTimeFormat.forPattern(FORMAT_YYYYMMDD);

    public RavenneKpiLogger(String processDate) {
        super();
        DateTime date = DF_YYYYMMDD.parseDateTime(processDate);
        cStart = String.valueOf(date.getMillis() / 1000);
    }

    /** Send countConcept delta.
     * These delta are computed in countconcept step.
     * The number of occurrence of concepts in genetic profile is computed by timebox.
     * The delta are calculated between the count calculated the previous week and count calculated this week
     * We send to kibana the delta percent and the delta count.
     * If POSITIVE, it means the concept has more occurrence than the week before.
     * If NEGATIVE, it means the concept has less occurrence than the week before
     *
     * @param timeboxType
     * @param concept
     * @param deltaCount
     * @param deltaPercent
     */
    public void sendDeltaCountConcept(String timeboxType, String concept, double deltaCount, double deltaPercent)
    {
        super.initJson();
        addValueToJson(CSTART_FIELD, cStart);
        addValueToJson("type", "RaveneGeneticProfilCountConcept");
        addValueToJson("timeboxtype", timeboxType);
        addValueToJson("concept", concept);
        addValueToJson("deltaCount", deltaCount);
        addValueToJson("deltaPercent", deltaPercent);
        String deltaType = "POSITIVE";
        if (deltaCount < 0) {
            deltaType = "NEGATIVE";
        }
        addValueToJson("deltaType",deltaType);
        super.sendJsonLog();

    }

    public void sendDeltaCountByTimeslot(String timebox, String nbConcept, long totalNbProfils, long profilsPercent) {
        super.initJson();
        addValueToJson(CSTART_FIELD, cStart);
        addValueToJson("type", "RaveneGeneticProfilCountByTimeslot");
        addValueToJson("timebox", timebox);
        addValueToJson("nbConcept", nbConcept);
        addValueToJson("nbProfils", totalNbProfils);
        addValueToJson("profilsPercent", profilsPercent);
        super.sendJsonLog();

    }
    
    public void sendDeltaCountByProvider(String timebox, String nbChannels, long totalNbProfils, long profilsPercent) {
        super.initJson();
        addValueToJson(CSTART_FIELD, cStart);
        addValueToJson("type", "RaveneGeneticProfilCountByProvider");
        addValueToJson("timebox", timebox);
        addValueToJson("nbChannels", nbChannels);
        addValueToJson("nbProfils", totalNbProfils);
        addValueToJson("profilsPercent", profilsPercent);
        super.sendJsonLog();

    }
}
