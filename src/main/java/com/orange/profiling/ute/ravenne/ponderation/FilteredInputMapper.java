package com.orange.profiling.ute.ravenne.ponderation;

import java.io.IOException;

import com.orange.profiling.ute.ravenne.dao.TimeboxZapAggregation;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Mapper;
import org.joda.time.DateTime;
import org.joda.time.Days;

import com.orange.profiling.common.utils.DatesUtils;
import com.orange.profiling.common.utils.FieldsUtils;
import com.orange.profiling.ute.ravenne.file.Filtered;
import com.orange.profiling.ute.ravenne.util.MyPredicate;

/**
 * A Hadoop Mapper implementation that processes filtered viewing data and applies concept filtering
 * and time-based weighting. This mapper processes tab-separated input records containing viewing
 * information and outputs weighted, filtered records for further processing.
 * 
 * Input format example:
 * AID    Concepts    Day    Timeslot    Duration    Type    ZapDuration    ZapTime    Provider    OfferName    Title
 * 
 * The mapper performs the following operations:
 * 1. Filters concepts based on a predicate class
 * 2. Applies conditional concept filtering
 * 3. Calculates time-based weights for viewing records
 * 4. Formats output with relevant fields for downstream processing
 */
public class FilteredInputMapper extends Mapper<Object, Text, Text, Text> {

    /** Default predicate class for concept filtering */
    private static final String DEFAULT_PREDICATE = "com.orange.profiling.ute.ravenne.util.DefaultPredicate";
    /** Error message for invalid predicate class */
    private static final String VALID_CLASS = "The class is not Recognized, valid classes : " + DEFAULT_PREDICATE;

    /** Configuration key for the predicate class */
    public static final String PREDICATE_CLASS = "myPredicateClass";
    /** Configuration key for the processing date */
    public static final String PROCESS_DATE = "processDate";
    /** Configuration key for the concept filter mapping table path */
    public static final String CONCEPT_FILTER_MAPPING_TABLE = "conceptFilterMappingTable";

    private Text outputKey = new Text();
    private Text outputValue = new Text();
    /** Helper class to parse and validate input lines */
    private Filtered filteredLine = new Filtered();
    /** Predicate for filtering concepts */
    private MyPredicate predicate = null;
    /** Reference date for calculating viewing age */
    private DateTime processDate = null;
    /** Filter for applying conditional concept rules */
    private ConditionalConceptsFilter conditionalConceptsFilter;

    /**
     * Maps input records to weighted, filtered output records.
     * Each input record is processed to:
     * 1. Extract and filter relevant concepts
     * 2. Calculate viewing age and weight
     * 3. Format output with required fields
     * 
     * Output format:
     * Key: AID
     * Value: [concepts]\t[daytimeslot]\t[type]\t[duration]\t[weight]\t[provider]\t[offerName]\t[viewAge]
     *
     * @param key Input key (unused)
     * @param value Input record text
     * @param context Hadoop context for output and counters
     */
    @Override
    public final void map(final Object key, final Text value, final Context context)
            throws IOException, InterruptedException {

        // Example :
        // 110250182	Genre Orange/série/autre/série,catégories/fiction,type/série	2	3	5	VOD	4974	**********	TVODM6REP-M6|catchuptv_m6_umts	M6CU0094425S_S_TVODM6REP_1	L'île aux mystères : quand le passé nous rattrape
        // 100002671	Genre Orange/émission/météo/météo,catégories/programme divers,sujets/actualité/météo,catégories/programme d'information	2	4	8	LIVE	600	**********	192	231959354	Météo

        filteredLine.setValue(value.toString());
        if (filteredLine.checkFormat()) {
            String aid = filteredLine.getField(Filtered.AID);

            String concepts = filterConcepts(filteredLine.getField(Filtered.CONCEPTS));
            concepts = conditionalConceptsFilter.filter(concepts);

            if (concepts.isEmpty()) {
                context.getCounter(Counters.ALL_CONCEPTS_FILTERED).increment(1);
            }
            String day = filteredLine.getField(Filtered.DAY);
            String timeslot = filteredLine.getField(Filtered.TIMESLOT);
            String daytimeslot = "d"+day+"t"+timeslot;
            String liveOrVod = filteredLine.getField(Filtered.VODORLIVE);
            String zapDuration = filteredLine.getField(Filtered.ZAPDURATION);
            String zapTime = filteredLine.getField(Filtered.ZAPTIME);
            String provider = filteredLine.getField(Filtered.PROVIDER);
            String offerName = filteredLine.getField(Filtered.OFFERNAME);
            if (zapDuration.isEmpty()) {
                zapDuration = "0";
            }

            int nbDays = daysSinceView(zapTime);
            long weight = PonderationFunction.weight(Integer.parseInt(zapDuration), nbDays);

            outputKey.set(aid);
            outputValue.set(String.join(FieldsUtils.TAB, concepts, daytimeslot, liveOrVod,
                    zapDuration, Long.toString(weight), provider, offerName, Integer.toString(nbDays)));

            if(liveOrVod.equals(TimeboxZapAggregation.VOD)){
                String[] parts = FieldsUtils.PIPE_PATTERN.split(provider);
                if(parts.length < 2 || parts[1].isEmpty()) {
                    context.getCounter(Counters.NB_VOD_WITHOUT_CATCHUP_CHANNEL_ID).increment(1);
                }
            }
            context.write(outputKey, outputValue);
        }
        else {
            context.getCounter(Counters.BAD_FORMAT).increment(1);
        }
    }

    /**
     * Filters concepts based on the configured predicate.
     * Applies the predicate test to each concept and builds a comma-separated
     * string of accepted concepts.
     *
     * @param allConcepts Comma-separated string of input concepts
     * @return Filtered comma-separated string of accepted concepts
     */
    private String filterConcepts(String allConcepts) {
        String[] conceptsList = FieldsUtils.COMMA_PATTERN.split(allConcepts);

        StringBuilder filteredConcepts = new StringBuilder();
        for(String concept: conceptsList) {

            if(predicate.test(concept)) {
                if (filteredConcepts.length()>0) {
                    filteredConcepts.append(FieldsUtils.COMMA);
                }
                filteredConcepts.append(concept);
            }
        }
        return filteredConcepts.toString();
    }


    /**
     * Calculates the number of days between a viewing timestamp and the process date.
     * The minimum returned value is 1 day.
     *
     * @param zapTimeStr Unix timestamp of the viewing event
     * @return Number of days since viewing, or -1 if parsing fails
     */
    private int daysSinceView(String zapTimeStr) {
        int nbDays = -1;
        try {
            long zapTime = Long.parseLong(zapTimeStr);
            DateTime zapDateTime = new DateTime(zapTime*DatesUtils.MILLISECONDS).withTime(12, 0, 0, 0);
            nbDays = Days.daysBetween(zapDateTime, processDate).getDays();
            if (nbDays < 1) {
                nbDays = 1;
            }
        }
        catch(NumberFormatException e) {
            // Nothing to do : we return -1
        }
        return nbDays;
    }

    /**
     * Initializes the mapper with configuration parameters.
     * Sets up:
     * 1. Process date for age calculations
     * 2. Predicate class for concept filtering
     * 3. Conditional concept filter
     *
     * @param context Hadoop context containing configuration
     * @throws InterruptedException if predicate class loading fails
     */
    @Override
    public final void setup(final Context context) throws InterruptedException {
        String processDateStr = context.getConfiguration().get(PROCESS_DATE);
        processDate = DatesUtils.DF_YYYYMMDD.parseDateTime(processDateStr).withTime(12, 0, 0, 0);

        loadPredicateClass(context);

        String conceptFilterMappingTablePath = context.getConfiguration()
                .get(CONCEPT_FILTER_MAPPING_TABLE);
        conditionalConceptsFilter = new ConditionalConceptsFilter(conceptFilterMappingTablePath);
    }


    /**
     * Loads and initializes the predicate class for concept filtering.
     * Falls back to the default predicate if none is specified.
     *
     * @param context Hadoop context containing configuration
     * @throws InterruptedException if class loading or initialization fails
     */
    private void loadPredicateClass(final Context context) throws InterruptedException {
        try {
            String pClass = context.getConfiguration().get(PREDICATE_CLASS);
            if (pClass == null || pClass.isEmpty()) {
                pClass = DEFAULT_PREDICATE;
            }
            predicate = (MyPredicate) Class.forName(pClass).newInstance();
            predicate.setUp(context.getConfiguration());
        }
        catch (ClassNotFoundException | InstantiationException |
                IllegalAccessException | NullPointerException e) {
            context.getCounter(Counters.CONCEPT_FILTER_ERROR).increment(1);
            throw new InterruptedException(e.getClass().getName() + " - " + VALID_CLASS);
        }
    }
}
