package com.orange.profiling.ute.ravenne.filterandtimeslot;

import java.io.IOException;

import org.apache.hadoop.fs.Path;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Job;
import org.apache.hadoop.mapreduce.lib.input.FileInputFormat;
import org.apache.hadoop.mapreduce.lib.input.MultipleInputs;
import org.apache.hadoop.mapreduce.lib.input.TextInputFormat;
import org.apache.hadoop.mapreduce.lib.output.FileOutputFormat;
import org.apache.hadoop.mapreduce.lib.output.LazyOutputFormat;
import org.apache.hadoop.mapreduce.lib.output.MultipleOutputs;
import org.apache.hadoop.mapreduce.lib.output.TextOutputFormat;
import org.apache.log4j.BasicConfigurator;
import org.apache.log4j.Logger;
import org.joda.time.DateTime;
import org.joda.time.DateTimeConstants;
import org.joda.time.format.DateTimeFormatter;

import com.orange.profiling.common.exceptions.FailedJobException;
import com.orange.profiling.common.mapred.UteConfiguration;
import com.orange.profiling.common.mapred.UteCountersWriter;
import com.orange.profiling.common.utils.DatesUtils;
import com.orange.profiling.common.utils.FieldsUtils;
import com.orange.profiling.common.utils.FileFilterVodProgbymac;
import com.orange.profiling.common.utils.FsUtils;

/**
 * Reads all zaps in progbymac and vod.
 * Aggregates zap over program id.
 * For live, program id is concatenation of show duration, channel Id, programid, title and day of program (day of zap minus 4h).
 * For vod, program id is concatenation of show duration, provider, content id, title and a dayPlaceHolder (0)
 *
 * Filters out zap too short.
 * For live, too short is zapDuration < showDuration * 50%.
 * For vod, too short is zapDuration < showDuration * 80%.
 *
 * Filters in delta mode : one week at a time
 * from filterBeginYearSlashWeek to endYearAndWeek
 * Take tooshort zaps from previous week in case they were tooshort beacause they continue on this week
 *
 * Outputs in selected/part-r-NNNNN for each aggregated zap :
 * aid TAB concepts TAB dayOfWeek TAB period TAB timeslot TAB vodLive TAB zapDuration
 * TAB averageTime TAB providerId TAB contentID TAB title
 *
 * With
 *   concepts : list of concepts separated by ,
 *   dayOfWeek : from 1 to 7
 *   period : from 1 to 6
 *   timeslot : from 1 to 10
 *   vodLive : "LIVE" or "VOD"
 *   zapDuration : in seconds
 *   averageTime : time at the middle of the view
 *   providerId : channel for LIVE and provider/servicecode for VOD
 *   contentId : programId for LIVE, external asset id for VOD
 *   title : title of program
 *
 * Outputs in tooshort/part-r-00000 for zap that are too short :
 * aid TAB VOD|LIVE TAB broadcastDuration TAB providerId TAB contentId TAB title
 * TAB dayOfProgram TAB beginZap TAB zapDuration TAB concepts
 *
 * These tooshort lines are read by PreviousTooShortMapper the next week.
 *
 */
public class MainFilterAndTimeslot {
    private static final Logger LOGGER = Logger.getLogger(MainFilterAndTimeslot.class);

    private static final DateTimeFormatter YEARWEEK_DATE_FORMATER = DatesUtils.DF_SLASHED_YEAR_WEEK;
    private static final DateTimeFormatter YYYYMMDD_DATE_FORMATER = DatesUtils.DF_YYYYMMDD;

    private static final int ARG_LENGTH = 5;

    private static final int INPUT_PROGBYMAC_IDX = 0;
    private static final int INPUT_VOD_IDX = 1;
    private static final int PARAM_BEGIN_YEARWEEK_IDX = 2;
    private static final int PARAM_END_YEARWEEK_IDX = 3;
    private static final int OUTPUT_DIR = 4;

    private static final String JOB_NAME = "ute:ravenne.filterAndTimeslot:";
    private static final int NB_REDUCER = 100;

    public static final String CONF_FIRSTDATE = "firstDate";

    public static final String OUT_SELECTED = "selected";
    public static final String OUT_TOOSHORT = "tooshort";

    private String inputPathProgByMac;
    private String inputPathVod;
    private String outputDir;

    public MainFilterAndTimeslot(String inputPathProgByMac, String inputPathVod, String outputDir) {
        super();
        this.inputPathProgByMac = inputPathProgByMac;
        this.inputPathVod = inputPathVod;
        this.outputDir = outputDir;

    }

    /**
     * @param args
     *            args
     * @throws IOException
     *             IOException
     * @throws InterruptedException
     *             InterruptedException
     * @throws ClassNotFoundException
     *             ClassNotFoundException
     */
    public static void main(final String[] args) throws IOException,
    InterruptedException, ClassNotFoundException, FailedJobException {

        BasicConfigurator.configure();

        // get param
        if (args.length != ARG_LENGTH) {
            String mess = "Takes "
                    + ARG_LENGTH
                    + " arguments : inputPathProgByMac inputPathVod beginYearSlashWeek endYearSlashWeek outputDir";
            LOGGER.error(mess);
            throw new IllegalArgumentException(mess);
        }

        String inputPathProgByMac = args[INPUT_PROGBYMAC_IDX];
        String inputPathVod = args[INPUT_VOD_IDX];
        String filterBeginYearSlashWeek = args[PARAM_BEGIN_YEARWEEK_IDX];
        String endYearAndWeek = args[PARAM_END_YEARWEEK_IDX];
        String outputDir = args[OUTPUT_DIR];

        MainFilterAndTimeslot filterAndTimeslotMain =
                new MainFilterAndTimeslot(inputPathProgByMac, inputPathVod, outputDir);

        DateTime yearWeekToFilter = DatesUtils.getWeekDayDateTime(filterBeginYearSlashWeek, DateTimeConstants.MONDAY);
        DateTime lastYearWeekToFilter = DatesUtils.getWeekDayDateTime(endYearAndWeek, DateTimeConstants.SUNDAY);
        while (yearWeekToFilter.isBefore(lastYearWeekToFilter)) {
            filterAndTimeslotMain.doJobFilterAndTimeslot(yearWeekToFilter);
            yearWeekToFilter = yearWeekToFilter.plusWeeks(1);
        }
    }

    private void doJobFilterAndTimeslot(DateTime yearWeekToFilter)
            throws IOException, ClassNotFoundException, InterruptedException, FailedJobException {

        // Create configuration
        UteConfiguration conf = new UteConfiguration();
        String monday = YYYYMMDD_DATE_FORMATER.print(yearWeekToFilter);
        String sunday = YYYYMMDD_DATE_FORMATER.print(yearWeekToFilter.withDayOfWeek(DateTimeConstants.SUNDAY));
        conf.set(FileFilterVodProgbymac.MIN_DATE, monday);
        conf.set(FileFilterVodProgbymac.MAX_DATE, sunday);

        // Create job
        String yearweekLabel = DatesUtils.DF_YEARWEEK.print(yearWeekToFilter);
        Job job = Job.getInstance(conf, JOB_NAME + yearweekLabel);
        job.setJarByClass(MainFilterAndTimeslot.class);

        // Setup MapReduce
        job.setMapperClass(ProgbymacMapper.class);
        job.setReducerClass(FilterAndTimeslotReducer.class);

        job.setNumReduceTasks(NB_REDUCER);

        // Specify key / value between mapper and reducer
        job.setOutputKeyClass(Text.class);
        job.setOutputValueClass(Text.class);

        // Input
        MultipleInputs.addInputPath(job, new Path(inputPathProgByMac), TextInputFormat.class, ProgbymacMapper.class);
        MultipleInputs.addInputPath(job, new Path(inputPathVod), TextInputFormat.class, VodMapper.class);

        String previousWeek = YEARWEEK_DATE_FORMATER.print(yearWeekToFilter.minusDays(1));
        String previousWeekOutputDir = String.join(FieldsUtils.SLASH,
                outputDir, previousWeek, OUT_TOOSHORT);

        FsUtils fsUtilsInput = new FsUtils(new Path(previousWeekOutputDir),conf);
        if (fsUtilsInput.getFs().exists(new Path(previousWeekOutputDir))) {
            MultipleInputs.addInputPath(job, new Path(previousWeekOutputDir + "/part*"),
                    TextInputFormat.class, PreviousTooShortMapper.class);
        }

        // Date filter
        FileInputFormat.setInputPathFilter(job, FileFilterVodProgbymac.class);

        // Output
        Path weekOutputDirPath = new Path(String.join(FieldsUtils.SLASH,
                outputDir, YEARWEEK_DATE_FORMATER.print(yearWeekToFilter)));
        FileOutputFormat.setOutputPath(job, weekOutputDirPath);
        LazyOutputFormat.setOutputFormatClass(job, TextOutputFormat.class);
        MultipleOutputs.addNamedOutput(job, OUT_SELECTED, TextOutputFormat.class, Text.class, Text.class);
        MultipleOutputs.addNamedOutput(job, OUT_TOOSHORT, TextOutputFormat.class, Text.class, Text.class);

        // Delete output if exists
        FsUtils fsUtilsOutput = new FsUtils(new Path(outputDir),conf);
        if (fsUtilsOutput.getFs().exists(weekOutputDirPath)) {
            fsUtilsOutput.getFs().delete(weekOutputDirPath, true);
        }

        // Execute job
        boolean succeeded = job.waitForCompletion(true);
        if (!succeeded) {
            throw new FailedJobException("Job " + JOB_NAME + yearweekLabel);
        }
        // Statistics
        UteCountersWriter.writeAlternateCounters(job, fsUtilsOutput.getFs(), weekOutputDirPath);
    }
}
