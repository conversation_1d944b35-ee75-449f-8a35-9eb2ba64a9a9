package com.orange.profiling.ute.ravenne.profiltype.profils;

import java.io.BufferedReader;
import java.io.IOException;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import com.orange.profiling.common.utils.FsUtils;
import com.orange.profiling.ute.ravenne.profiltype.profils.dao.Profil;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Reducer;
import com.orange.profiling.common.mapred.MosWriter;
import com.orange.profiling.common.utils.FieldsUtils;
import org.apache.log4j.Logger;

/**
 * Reducer implementation for the Ravenne Profile Processing System.
 * This class processes two types of inputs from the TasteboxMapper:
 * 1. Vector-based outputs that track which vectors triggered specific profile types
 * 2. User (AID) based outputs that determine user profile types
 *
 * Key Features:
 * - Processes and aggregates viewing data by user and vector
 * - Implements two different profiling algorithms (VECTORTYPE and TOPCONCEPTS)
 * - Generates profile assignments for users
 * - Tracks vector distribution across profiles
 * - Maintains usage statistics and counters
 *
 * Output Types:
 * 1. User Profiles:
 *    - Format: aid -> th_profiltype0X TAB profilId
 *    - Limited to top N profiles per user
 *
 * 2. Vector Statistics:
 *    - Format: vector -> profilId TAB count
 *    - Tracks which vectors contribute to each profile
 *
 * <AUTHOR>
 */
public class ProfilsReducer extends Reducer<Text, Text, Text, Text> {

    /**
     * Maximum number of profile types to assign to each user
     */
    private static final int PARAM_NB_PROFIL_TYPE_MAX = 5;
    private static final int KEY_ID_PROFIL_IDX = 0;
    private static final int KEY_LABEL_PROFIL_IDX = 1;
    private static final int KEY_VARNAME_PROFIL_IDX = 2;
    public static final String PROFIL_ID = "profileId";
    public static final String STB_TAG = "stb";
    private static final int KEY_AID_IDX = 0;
    private static final int KEY_DEVICE_IDX = 1;

    /**
     * Field separator for output formatting
     */
    private static final String TAB = FieldsUtils.TAB;

    /**
     * Writer for multiple output streams
     */
    private MosWriter mos;

    Map<String, Profil> profilCatalog = new HashMap<>();

    private static final Logger LOGGER = Logger.getLogger(ProfilsReducer.class);

    /**
     * define new output directory name : verticalScore
     */
    private String outputDirVertical;

    /**
     * define new output directory name : profilsOtt
     */
    private String outputDirProfilsOtt;


    /**
     * Initializes the reducer's resources.
     * Sets up the MosWriter for handling multiple output streams.
     *
     * @param context The reducer context
     * @throws IOException If there are errors initializing the writer
     * @throws InterruptedException If the setup is interrupted
     */
    @Override
    public final void setup(final Context context) throws IOException, InterruptedException {
        getMosWriter().open(context);
        FsUtils fsUtils = new FsUtils(context.getConfiguration());
        Configuration conf = context.getConfiguration();
        outputDirVertical = conf.get(MainProfils.OUTPUT_VERTICAL);
        //outputDirProfilsOtt = conf.get(MainProfils.OUTPUT_PROFILS_OTT);

        // Charger le catalogue de profils
        String catalogPath = context.getConfiguration().get(MainProfils.CATALOG_PROFILS);
        profilCatalog = loadProfilCatalog(fsUtils, catalogPath);

    }

    /**
     * Main reduce function that processes mapper outputs.
     * Handles two types of inputs based on the key:
     * 1. VECTOR_OUT: Processes vector-based profile triggers
     * 2. Other: Processes user (AID) profile assignments
     *
     * @param key     The input key (either VECTOR_OUT or user AID)
     * @param values  The values associated with the key
     * @param context The reducer context
     * @throws IOException          If there are errors writing output
     * @throws InterruptedException If the reduction is interrupted
     */
    @Override
    public final void reduce(final Text key, final Iterable<Text> values,
                             final Context context) throws IOException, InterruptedException {

        String keyString = key.toString();

        // Two types of mappers output
        if (TasteboxMapper.VECTOR_OUT.equals(keyString)) {
            // Output with vector that triggered profilid
            reduceVectorTypeCount(values, context);
        } else {
            // Output with profil for given aid
            // and compute Vertical score for all thematic
            reduceAidProfilTypeAndScoreVertical(keyString, values, context);
        }
    }

    /**
     * Processes user profile type assignments.
     * Algorithm selection priority:
     * 1. VECTORTYPE algorithm (preferred)
     * 2. TOPCONCEPTS algorithm (fallback)
     * <p>
     * For each user:
     * 1. Aggregates viewing durations by algorithm and profile
     * 2. Selects the appropriate algorithm based on availability
     * 3. Sorts profiles by duration
     * 4. Assigns top N profiles (limited by PARAM_NB_PROFIL_TYPE_MAX)
     * 5. Updates relevant counters
     *
     * @param aid     User identifier
     * @param values  Iterable of profile assignments with durations
     * @param context Reducer context for output and counters
     * @throws IOException          If there are errors writing output
     * @throws InterruptedException If the processing is interrupted
     */
    private void reduceAidProfilTypeAndScoreVertical(String aid, Iterable<Text> values,
                                     Reducer<Text, Text, Text, Text>.Context context)
            throws IOException, InterruptedException {

        Map<String, Map<String, Integer>> algoProfilWeights = new HashMap<>();

        // TotalDuration By User
        int userTotalDuration = 0;

        // parse values
        for (Text value : values) {

            // rav_fan_sport d1t5 3600 concept1=10.0,concept2=5.0   0.98
            String[] csv = FieldsUtils.TAB_PATTERN.split(value.toString());
            String algo = csv[TasteboxMapper.OUT_ALGO_IDX];
            String profilId = csv[TasteboxMapper.OUT_PROFIL_IDX];
            Integer totalDuration = Integer.parseInt(csv[TasteboxMapper.OUT_DURATION_IDX]);

            userTotalDuration += totalDuration;

            Map<String, Integer> profilWeights = algoProfilWeights.getOrDefault(algo, new HashMap<>());
            profilWeights.merge(profilId, totalDuration, Integer::sum);
            algoProfilWeights.put(algo, profilWeights);
        }

        Map<String, Integer> sortedProfilWeights = new LinkedHashMap<>();

        // On utilise en priorite l'algo VECTORTYPE
        if (algoProfilWeights.containsKey(TasteboxMapper.ALGO_VECTORTYPE)) {
            sortedProfilWeights = sortProfilIdByDurationDescending(
                    algoProfilWeights.get(TasteboxMapper.ALGO_VECTORTYPE));
            context.getCounter(Counters.NB_AID_ALGO_VECTORTYPE).increment(1);
        }
        // Mais si VECTORTYPE n'a rien trouvé, on utilise l'algo TOP CONCEPTS
        else if (algoProfilWeights.containsKey(TasteboxMapper.ALGO_TOPCONCEPTS)) {
            sortedProfilWeights = sortProfilIdByDurationDescending(
                    algoProfilWeights.get(TasteboxMapper.ALGO_TOPCONCEPTS));
            context.getCounter(Counters.NB_AID_ALGO_TOPCONCEPTS).increment(1);
        } else {
            context.getCounter(Counters.NB_AID_NO_ALGO).increment(1);
        }

        int index = 0;
        // decode key
        String[] complexKey = FieldsUtils.DASH_PATTERN.split(aid);
        String aidHash = complexKey[KEY_AID_IDX];
        String device = complexKey[KEY_DEVICE_IDX];

        // write best 5 profils for stb log
        if (aid.endsWith(STB_TAG)) {


            for (String profilId : sortedProfilWeights.keySet()) {
                index++;
                mos.write(MainProfils.OUT_PROFILS, aidHash, "th_profiltype0" + index + TAB + profilId,
                        MainProfils.OUT_PROFILS + "/part");

                context.getCounter(MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS,
                        MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS + profilId).increment(1);

                if (index >= PARAM_NB_PROFIL_TYPE_MAX) {
                    break;
                }
            }

            // Calculer les ratios (scores) pour toutes les thematiques/verticales (0 pour les thematiques non vues)
            if (userTotalDuration > 0) {
                for (Profil profil : profilCatalog.values()) {
                    String profId = profil.getProfilId();
                    String verticalName = profil.getVerticalName();
                    int duration = sortedProfilWeights.getOrDefault(profId, 0);
                    double ratio = (double) duration / userTotalDuration;
                    String formattedRatio = String.format("%.4f", ratio);

                    mos.write(MainProfils.OUT_SCORES_VERTICALES, aidHash, "th_vert_" + verticalName + TAB + formattedRatio, outputDirVertical + "/" + MainProfils.OUT_SCORES_VERTICALES + "/part");
                }
            }

            // number of foyers having x profiles - counter update
            context.getCounter(MainProfils.COUNTER_GROUP_NB_USER_HAVING_N_PROFILS,
                    "NB_FOYERS_HAVING_" + index + "_PROFILES").increment(1);
            if (index > 0) {
                context.getCounter(Counters.NB_USER_WITH_PROFIL).increment(1);
            }

        }
        else {
            // write best 5 profils for OTT logs
            int index_ott = 0;
            for (String profilId : sortedProfilWeights.keySet()) {
                index_ott++;
                mos.write(MainProfils.OUT_PROFILS_OTT, aidHash, "th_profiltype_" + device + "0" + index_ott + TAB + profilId,
                        MainProfils.OUT_PROFILS_OTT + "/part");

                context.getCounter(MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS,
                        MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS + profilId).increment(1);

                if (index_ott >= PARAM_NB_PROFIL_TYPE_MAX) {
                    break;
                }
            }
            // number of foyers having x profiles - counter update
            context.getCounter(MainProfils.COUNTER_GROUP_NB_USER_HAVING_N_PROFILS,
                    "NB_FOYERS_HAVING_" + index_ott + "_PROFILES").increment(1);
            if (index_ott > 0) {
                context.getCounter(Counters.NB_USER_WITH_PROFIL).increment(1);
            }
        }




    }

    /**
     * Sorts profileId by their associated viewing durations in descending order.
     * Used to determine the most significant profiles for a user based on viewing time.
     *
     * @param profilWeights Map of profileId to their viewing durations
     * @return Sorted map with profiles ordered by descending duration
     */
    private static Map<String, Integer> sortProfilIdByDurationDescending(Map<String, Integer> profilWeights) {
        return profilWeights.entrySet().stream()
                .sorted((Map.Entry.<String, Integer>comparingByValue().reversed()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (e1, e2) -> e1, LinkedHashMap::new));
    }

    /**
     * Processes vector-based profile (mappers lines starting by VECTOR|) triggers to generate vector statistics.
     * For each vector:
     * 1. Counts occurrences by profileId and algorithm
     * 2. Tracks unique vectors
     * 3. Updates vector distribution counters
     * 4. Outputs vector-profile relationships with counts
     * <p>
     * Input format: algo TAB vector TAB profilId
     * Output format: algo;vector TAB profilId TAB count
     *
     * @param values  Iterable of vector trigger records
     * @param context Reducer context for output and counters
     * @throws IOException          If there are errors writing output
     * @throws InterruptedException If the processing is interrupted
     */
    private void reduceVectorTypeCount(Iterable<Text> values,
                                       Reducer<Text, Text, Text, Text>.Context context)
            throws IOException, InterruptedException {

        Map<String, Map<String, Integer>> countByProfilIdAndVector = new HashMap<>();
        Set<String> vectorList = new HashSet<>();
        // parse values
        for (Text value : values) {
            String[] csv = FieldsUtils.TAB_PATTERN.split(value.toString());
            String algo = csv[TasteboxMapper.VECTOR_OUT_ALGO_IDX];
            String vector = csv[TasteboxMapper.VECTOR_OUT_VECTOR_IDX];
            String profilId = csv[TasteboxMapper.VECTOR_OUT_PROFIL_IDX];

            String algoVector = algo + MainProfils.SEP + vector;

            Map<String, Integer> countByVector = countByProfilIdAndVector.getOrDefault(profilId, new HashMap<>());
            countByVector.merge(algoVector, 1, Integer::sum);
            countByProfilIdAndVector.put(profilId, countByVector);
            vectorList.add(vector);
        }
        context.getCounter(Counters.NB_VECTOR).increment(vectorList.size());

        for (Map.Entry<String, Map<String, Integer>> entryProfil : countByProfilIdAndVector.entrySet()) {

            String profilId = entryProfil.getKey();
            Map<String, Integer> countByVector = entryProfil.getValue();
            context.getCounter(MainProfils.COUNTER_GROUP_NB_VECTOR_BY_PROFIL,
                    MainProfils.COUNTER_GROUP_NB_VECTOR_BY_PROFIL + profilId).increment(countByVector.size());

            for (Map.Entry<String, Integer> entryVector : countByVector.entrySet()) {
                String vector = entryVector.getKey();
                Integer count = entryVector.getValue();
                mos.write(MainProfils.OUT_VECTORS, vector, profilId + TAB + count,
                        MainProfils.OUT_VECTORS + "/part");
            }
        }
    }

    /**
     * Sets a custom MosWriter instance (used in test to use a mock MosWriter).
     * Primarily used for testing to inject mock writers.
     *
     * @param mos The MosWriter instance to use
     */
    public void setMosWriter(MosWriter mos) {
        this.mos = mos;
    }

    /**
     * Gets the current MosWriter instance.
     * Creates a new instance if none exists.
     *
     * @return The current MosWriter instance
     */
    private MosWriter getMosWriter() {
        if (mos == null) {
            mos = new MosWriter();
        }
        return mos;
    }

    @Override
    public void cleanup(Context context) throws IOException, InterruptedException {
        mos.close();
    }

    public Map<String, Profil> loadProfilCatalog(FsUtils fsUtils, String inputPathCatProfil) throws IOException {
        Map<String, Profil> catalog = new HashMap<>();
        try (BufferedReader br = fsUtils.getReaderForFile(inputPathCatProfil)) {
            try {
                String line = br.readLine();
                while (line != null) {
                    if (line.startsWith(PROFIL_ID)) {
                        line = br.readLine();
                        continue;
                    }
                    String[] csv = FieldsUtils.TAB_PATTERN.split(line);
                    String profilId = csv[KEY_ID_PROFIL_IDX].trim();
                    String label = csv[KEY_LABEL_PROFIL_IDX].trim();
                    String varname = csv[KEY_VARNAME_PROFIL_IDX].trim();
                    catalog.put(profilId, new Profil(profilId, label, varname));
                    line = br.readLine();
                }
            } catch (IOException e) {
                LOGGER.error(e.getMessage(), e);
            }
        }
        return catalog;
    }
}
