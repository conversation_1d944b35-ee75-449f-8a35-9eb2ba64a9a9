package com.orange.profiling.ute.ravenne.stat.countbytimeslot;

import java.io.IOException;
import java.util.Map;

import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Mapper;
import com.orange.profiling.common.utils.FieldsUtils;
import com.orange.profiling.common.utils.OrangeTimeslotUtils;
import com.orange.profiling.ute.ravenne.file.ConceptsWeights;
import com.orange.profiling.ute.ravenne.util.ValuedKeyUtils;

public class ConceptsWeightsMapper extends Mapper<Object, Text, Text, Text> {

    public static final String KEY_SEPARATOR = FieldsUtils.PIPE;
    public static final int KEY_LENGTH = 2;
    public static final int KEY_TIMEBOX_IDX = 0;
    public static final int KEY_NBCONCEPTS_IDX = 1;

    private NbProfilCounter nbProfilCounter;
    private ConceptsWeights conceptsWeights = new ConceptsWeights();
    private Text outputKey = new Text();
    private static final Text OUTPUT_VALUE = new Text("1");

    @Override
    public final void setup(final Context context) throws InterruptedException {
        nbProfilCounter = new NbProfilCounter(context);
    }

    @Override
    public final void map(final Object key, final Text value, final Context context)
            throws IOException, InterruptedException {

        conceptsWeights.setValue(value.toString());
        if (conceptsWeights.checkFormat()) {
            String timebox = conceptsWeights.getField(ConceptsWeights.TIMEBOX);
            if (OrangeTimeslotUtils.isValidTimebox(timebox)) {
                String weightedConcepts = conceptsWeights.getField(ConceptsWeights.PONDERATED_CONCEPTS);
                Map<String,Long> conceptsMap = ValuedKeyUtils.parseStringToKeyValue(weightedConcepts);
                int nbConcepts = conceptsMap.size();
                outputKey.set(String.join(KEY_SEPARATOR, timebox, Integer.toString(nbConcepts)));
                context.write(outputKey, OUTPUT_VALUE);

                String timeboxType = OrangeTimeslotUtils.getTimeboxType(timebox);
                nbProfilCounter.countNbProfilsByTimeboxType(timeboxType);
                nbProfilCounter.countNbProfilsByTimeboxTypeAndNbConcepts(timeboxType, nbConcepts);
            }
        }

    }

}
