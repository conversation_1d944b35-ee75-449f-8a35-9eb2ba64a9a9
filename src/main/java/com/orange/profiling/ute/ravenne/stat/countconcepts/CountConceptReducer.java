package com.orange.profiling.ute.ravenne.stat.countconcepts;

import java.io.IOException;
import java.util.regex.Pattern;

import org.apache.hadoop.io.NullWritable;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Reducer;

import com.orange.profiling.common.utils.FieldsUtils;
import com.orange.profiling.ute.ravenne.logger.RavenneKpiLogger;

public class CountConceptReducer extends Reducer<Text, Text, NullWritable, Text> {

    public static final String PIPE = FieldsUtils.PIPE;
    public static final Pattern PIPE_PATTERN = FieldsUtils.PIPE_PATTERN;
    private static final String TAB = FieldsUtils.TAB;
    private static final Pattern TAB_PATTERN = FieldsUtils.TAB_PATTERN;
    private static final long PERCENT = 100L;
    private static final long DEFAULT_THRESHOLD = 5L;

    private long deltaThreshold = DEFAULT_THRESHOLD;
    private RavenneKpiLogger kpiLogger;
    private long totalToday;
    private long totalYesterday;
    private Text outputValue = new Text();

    @Override
    public final void reduce(final Text key, final Iterable<Text> values, final Context context)
            throws IOException, InterruptedException {
        String[] keyParts = PIPE_PATTERN.split(key.toString());
        if (keyParts.length >= ConceptsWeightsMapper.KEY_LENGTH) {

            String timeboxType = keyParts[ConceptsWeightsMapper.KEY_TIMEBOX_IDX];
            String concept = keyParts[ConceptsWeightsMapper.KEY_CONCEPT_IDX];

            totalToday = 0L;
            totalYesterday = 0L;
            readValuesToUpdateTotalCount(values);

            if (totalToday > 0 || totalYesterday > 0) {
                writeOutput(context, timeboxType, concept);
            }
        }
    }

    private void readValuesToUpdateTotalCount(final Iterable<Text> values) {
        for(Text value: values) {
            String[] valueParts = TAB_PATTERN.split(value.toString());
            if (valueParts.length > 1) {
                String outId = valueParts[0];
                String outNum = valueParts[1];
                long num = FieldsUtils.getSafelyLong(outNum, 0L);
                if (ConceptsWeightsMapper.OUT_ID_TODAY.equals(outId)) {
                    totalToday += num;
                }
                else if (PreviousCountConceptsMapper.OUT_ID_YESTERDAY.equals(outId)) {
                    totalYesterday += num;
                }
            }
        }
    }

    private void writeOutput(final Context context, String timeboxType, String concept)
            throws IOException, InterruptedException {
        long deltaPercent = PERCENT;
        long deltaCount = totalToday - totalYesterday;
        if (totalYesterday > 0) {
            deltaPercent = PERCENT*deltaCount/totalYesterday;
        }

        outputValue.set(String.join(TAB, timeboxType, concept,
                Long.toString(totalToday), Long.toString(deltaPercent), Long.toString(deltaCount)));
        context.write(NullWritable.get(), outputValue);

        if (Math.abs(deltaPercent)>deltaThreshold) {
            kpiLogger.sendDeltaCountConcept(timeboxType, concept, deltaCount, deltaPercent);
        }
    }

    @Override
    protected void setup(Context context) throws IOException, InterruptedException {
        String deltaThresholdConf = context.getConfiguration().get(MainCountConcepts.CONF_THRESHOLD);
        deltaThreshold = FieldsUtils.getSafelyLong(deltaThresholdConf, DEFAULT_THRESHOLD);
        String processDate = context.getConfiguration().get(MainCountConcepts.CONF_PROCESS_DATE);
        buildKpiLogger(processDate);
    }

    private void buildKpiLogger(String processDate) {
        if (kpiLogger == null) {
            kpiLogger = new RavenneKpiLogger(processDate);
        }
    }

    /** For test purpos only : set kpi logger with mockLogger
     *
     * @param mockLogger
     */
    public void setKpiLogger(RavenneKpiLogger mockLogger) {
        kpiLogger = mockLogger;
    }

}
