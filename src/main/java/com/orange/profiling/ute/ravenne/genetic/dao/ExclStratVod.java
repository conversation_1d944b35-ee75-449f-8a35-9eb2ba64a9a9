package com.orange.profiling.ute.ravenne.genetic.dao;

import com.google.gson.ExclusionStrategy;
import com.google.gson.FieldAttributes;

public class ExclStratVod implements ExclusionStrategy {

    @Override
    public boolean shouldSkipClass(Class<?> arg0) {
        return false;
    }

    @Override
    public boolean shouldSkipField(FieldAttributes f) {

        return (f.getDeclaringClass() == ProfileJson.class &&
                (f.getName().equals("thRvl") || f.getName().equals("thCh")) );
    }

}