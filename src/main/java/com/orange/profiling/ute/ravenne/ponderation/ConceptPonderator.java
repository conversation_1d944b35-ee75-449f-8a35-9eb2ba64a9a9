package com.orange.profiling.ute.ravenne.ponderation;

import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;

import com.orange.profiling.common.utils.FieldsUtils;

/**
 * A utility class for weighting concepts in a hierarchical structure.
 * This class handles the ponderation (weighting) of concepts by redistributing weights
 * from leaf nodes to their parent nodes in the concept hierarchy.
 *
 * The class implements a specialized algorithm where:
 * 1. Only leaf weights are considered in the final calculation
 * 2. A portion of each leaf's weight is distributed upward to parent nodes
 * 3. The weight distribution follows an "egoistic" factor to control how much weight
 *    is shared with parent nodes
 *
 * Example hierarchy:
 * root/parent1/parent2/leaf
 * - Only the leaf's initial weight is considered
 * - Parent nodes receive a portion of their children's weights
 * - The root node is excluded from weight distribution
 */
public class ConceptPonderator {

    /**
     * The egoism factor that controls how much weight a node keeps versus shares with its parent.
     * A higher value means nodes are more "selfish" and keep more of their weight.
     */
    private static final int EGOISME = 2;

    /**
     * Map storing the initial weighted concepts.
     * Key: concept path (e.g., "root/parent/leaf")
     * Value: weight value
     */
    private Map<String, Long> weightedConcepts = new TreeMap<>();

    /**
     * Constructs a new ConceptPonderator with initial weighted concepts.
     *
     * @param weightedConcepts Initial map of concepts and their weights to be processed
     */
    public ConceptPonderator(Map<String, Long> weightedConcepts) {
        this.weightedConcepts = weightedConcepts;
    }

    /**
     * Performs the main ponderation algorithm by redistributing weights from leaf nodes to their parents.
     * The algorithm:
     * 1. Calculates the number of children for each concept node
     * 2. Identifies leaf nodes (nodes with no children)
     * 3. For each leaf node, redistributes its weight up the concept hierarchy
     * 4. Parent nodes receive a portion of their children's weights based on the egoism factor
     *
     * @return A new map containing the ponderated concepts with redistributed weights
     */
    public Map<String, Long> ponderatesParentsWithChildWeight() {

       Map<String, Integer> nbChildrenByConcept = getNbChildrenByConcept();

       Map<String, Long> weightedConceptMap = new TreeMap<>();
       for (Map.Entry<String, Long> entry : weightedConcepts.entrySet()) {
           String concept = entry.getKey();
           Long weight = entry.getValue();

           // We add weight from children to parent : we don't take into account parent's weight
           int nbrChildren = nbChildrenByConcept.getOrDefault(concept, 0);
           if (nbrChildren == 0) {
               addLeafWeightToAllParent(weightedConceptMap, nbChildrenByConcept, concept, weight);
           }
       }

       return weightedConceptMap;
   }

   /**
     * Calculates the number of direct children for each concept in the hierarchy.
     * This method:
     * 1. Processes each concept path from leaf to root
     * 2. Maintains a count of unique children for each parent node
     * 3. Stops counting for a branch once a previously seen child is encountered
     *
     * @return Map where key is the parent concept and value is its number of unique children
     */
   protected Map<String, Integer> getNbChildrenByConcept() {

       Map<String, Integer> nbChildrenByConcept = new LinkedHashMap<>();

       Map<String,Set<String>> nodesChildren = new LinkedHashMap<>();

       Set<String> conceptsSet = weightedConcepts.keySet();
       for (String concept : conceptsSet) {
           // List of concept elements from the root to the leaf
           String[] nodes = FieldsUtils.SLASH_PATTERN.split(concept);

           // we go through the tree from leaf to the last node before the root
           // we add the child to the parent and we stop if it was already here
           for (int index = nodes.length - 2; index >= 1; index--) {
               String parentConcept = buildConceptFromRootToIndex(nodes, index);
               String childLeaf = nodes[index+1];

               if (!nodesChildren.containsKey(parentConcept)) {
                   nodesChildren.put(parentConcept, new HashSet<>());
               }
               Set<String> children = nodesChildren.get(parentConcept);

               if (children.contains(childLeaf)) {
                   // The child has already been added, so we stop
                   break;
               }

               // Child not already added : we add it
               children.add(childLeaf);
               nbChildrenByConcept.merge(parentConcept,1,Integer::sum);
           }
       }
       return nbChildrenByConcept;
   }

   /**
     * Redistributes a leaf node's weight to all its parent nodes in the hierarchy.
     * The weight distribution:
     * 1. Starts from the leaf node with its full weight
     * 2. For each parent level:
     *    - Calculates portion to share based on parent's total children and egoism factor
     *    - Subtracts shared portion from child
     *    - Adds portion to parent
     * 3. Continues until reaching the root (exclusive)
     *
     * @param weightedConceptMap Map to store the redistributed weights (ponderated concepts)
     * @param nbChildrenByConcept Map containing number of children for each parent concept (from getNbChildrenByConcept)
     * @param leafConcept The leaf concept whose weight is being distributed
     * @param weight The initial weight of the leaf concept
     */
   protected void addLeafWeightToAllParent(
           Map<String, Long> weightedConceptMap,
           Map<String, Integer> nbChildrenByConcept,
           String leafConcept, Long weight) {

       long leafWeight = weight;

       // List each element parts of the concept
       String[] nodes = FieldsUtils.SLASH_PATTERN.split(leafConcept);


       String childConceptKey = leafConcept;
       long childWeightContribution = leafWeight;
       weightedConceptMap.put(childConceptKey, leafWeight);

       for (int index = nodes.length - 2; index > 0; index--) {
           // we don't add weight to root part of the concept (sujet,format,personnages...) ence index > 0
           String parentConceptKey = buildConceptFromRootToIndex(nodes, index);

           // weightToAdd is the part we take from child to give to parent
           long weightToAdd = childWeightContribution /
                   (EGOISME * nbChildrenByConcept.get(parentConceptKey));

           long currentChildWeight = weightedConceptMap.getOrDefault(childConceptKey, 0L);
           weightedConceptMap.put(childConceptKey, currentChildWeight - weightToAdd);
           long currentParentWeight = weightedConceptMap.getOrDefault(parentConceptKey, 0L);
           weightedConceptMap.put(parentConceptKey, currentParentWeight + weightToAdd);

           // We climb the concept tree to next parent.
           // Current parent is the new child.
           childConceptKey = parentConceptKey;
           // current parent contributes to its parent according what it receives
           childWeightContribution = weightToAdd;
       }
   }

   /**
     * Builds a concept path from root to a specified index in the concept hierarchy.
     *
     * @param nodes Array of concept elements split by '/'
     * @param index The index up to which to build the concept path (inclusive)
     * @return The constructed concept path from root to the specified index
     *
     * Example:
     * nodes = ["root", "parent1", "parent2", "leaf"]
     * index = 2 returns "root/parent1/parent2"
     * index = 1 returns "root/parent1"
     */
   protected static String buildConceptFromRootToIndex(String[] nodes, int index) {
       // We are sure nodes has at least one element, the root of concept
       StringBuilder bld = new StringBuilder(nodes[0]);
       for (int i = 1; i <= index; i++) {
           bld.append("/" + nodes[i]);
       }
       return bld.toString();
   }
}
