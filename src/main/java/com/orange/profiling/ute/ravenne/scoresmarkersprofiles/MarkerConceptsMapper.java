package com.orange.profiling.ute.ravenne.scoresmarkersprofiles;

import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Mapper;

import java.io.IOException;


public class MarkerConceptsMapper extends Mapper<Object, Text, Text, Text> {
    @Override
    public final void map(final Object key, final Text value,
                          final Context context) throws IOException, InterruptedException {
        try {
            context.getCounter("Debug", "TotalLines").increment(1);

            String[] parts = value.toString().split("\t");
            if (parts.length != 2) {
                context.getCounter("Debug", "InvalidFormat").increment(1);
                return;
            }

            String markername = parts[0];
            String[] concepts = parts[1].split(",");

            for (String concept : concepts) {
                concept = concept.trim();
                if (!concept.isEmpty()) {
                    context.write(
                            new Text(concept),
                            new Text("MARKER:" + markername)
                    );
                    context.getCounter("Debug", "EmittedConcepts").increment(1);
                }
            }
        } catch (Exception e) {
            context.getCounter("Error", "MapperException").increment(1);
        }
    }
}

