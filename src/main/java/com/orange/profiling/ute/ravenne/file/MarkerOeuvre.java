package com.orange.profiling.ute.ravenne.file;

import com.orange.profiling.common.file.AbstractProfilingHdfsFile;
import com.orange.profiling.common.utils.FieldsUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;


public class MarkerOeuvre extends AbstractProfilingHdfsFile {
    public static final int MARKER_NAME = 0;
    public static final int ID_OEUVRE = 1;

    private static final Map<String, Integer> FIELDS = new HashMap<>();
    static {
        FIELDS.put("markerName", MARKER_NAME);
        FIELDS.put("idOeuvre", ID_OEUVRE);
    }

    private static final String SEPARATOR = FieldsUtils.TAB;
    private static final Pattern PATTERN = FieldsUtils.TAB_PATTERN;

    @Override
    public Map<String, Integer> getFields() {
        return FIELDS;
    }

    @Override
    public String getSeparator() {
        return SEPARATOR;
    }

    @Override
    public Pattern getSplitPattern() {
        return PATTERN;
    }

}
