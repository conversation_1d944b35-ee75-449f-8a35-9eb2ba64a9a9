package com.orange.profiling.ute.ravenne.ponderation;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Scanner;

import org.apache.log4j.Logger;

import com.orange.profiling.common.utils.FieldsUtils;

/**
 * A filter that conditionally removes concepts based on the presence of other concepts.
 * This class reads a mapping table from a file that defines which concepts should be removed
 * when certain other concepts are present in the input.
 * 
 * The mapping table should be in a colon-separated format where each line contains:
 * [concept_to_check]:[concept_to_remove]
 */
public class ConditionalConceptsFilter {

    private static final Logger LOGGER = Logger.getLogger(ConditionalConceptsFilter.class);
    /** List of concepts that trigger removal of other concepts when present */
    private List<String> ifConceptPresent;
    /** List of concepts to be removed when corresponding concepts in ifConceptPresent are found */
    private List<String> removeConcept;

    /**
     * Creates a new ConditionalConceptsFilter by reading the mapping table from the specified path.
     *
     * @param conceptFilterMappingTablePath Path to the file containing concept filtering rules
     */
    ConditionalConceptsFilter(String conceptFilterMappingTablePath) {
        readConceptFilterMappingTable(conceptFilterMappingTablePath);
    }

    /**
     * Filters the input concepts string based on the conditional rules defined in the mapping table.
     * If a concept from ifConceptPresent is found in the input, its corresponding concept from
     * removeConcept will be removed from the final output.
     *
     * @param concepts Comma-separated string of concepts to filter
     * @return Filtered comma-separated string of concepts
     */
    public String filter(String concepts) {
        // new ArrayList<>() => can't be removed, otherwise remove method will give an exception,
        // Arrays.asList doesn't allow any changes to the list
        List<String> conceptsList = new ArrayList<>(Arrays.asList(FieldsUtils.COMMA_PATTERN.split(concepts)));
        if(ifConceptPresent.size() > 0){
            int index = 0;
            for (String conceptPresent: ifConceptPresent) {
                if(conceptsList.contains(conceptPresent)) {
                    String conceptToRemove = removeConcept.get(index);
                    conceptsList.remove(conceptToRemove);
                }
                index++;
            }
        }
        return String.join(",", conceptsList);
    }

    /**
     * Reads the concept filter mapping table from the specified file.
     * Each line in the file should be in the format: [concept_to_check]:[concept_to_remove]
     * The file is read using a colon as the delimiter between the two concepts.
     *
     * @param filepath Path to the mapping table file
     */
    private void readConceptFilterMappingTable(String filepath) {
        ifConceptPresent = new ArrayList<>();
        removeConcept = new ArrayList<>();
        Integer index = 0;
        String content;
        File file = new File(filepath);
        try(Scanner sc = new Scanner(new FileInputStream(file))) {
            while (sc.hasNextLine()){
                content = sc.nextLine();
                String[] contentPart = FieldsUtils.COLON_PATTERN.split(content);
                ifConceptPresent.add(FieldsUtils.get(contentPart, 0));
                removeConcept.add(FieldsUtils.get(contentPart, 1));
                index++;
            }
        }
        catch(FileNotFoundException fnf){
            LOGGER.info(filepath + " not found");
        }
        catch (Exception e) {
            LOGGER.info(e.getMessage());
        }
    }

}
