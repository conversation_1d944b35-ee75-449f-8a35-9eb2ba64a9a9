package com.orange.profiling.ute.ravenne.genetic.concat;

import com.orange.profiling.common.exceptions.FailedJobException;
import com.orange.profiling.common.mapred.UteConfiguration;
import com.orange.profiling.common.mapred.UteCountersWriter;
import com.orange.profiling.common.utils.DatesUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Job;
import org.apache.hadoop.mapreduce.lib.input.MultipleInputs;
import org.apache.hadoop.mapreduce.lib.input.TextInputFormat;
import org.apache.hadoop.mapreduce.lib.output.FileOutputFormat;
import org.apache.hadoop.mapreduce.lib.output.TextOutputFormat;
import org.apache.log4j.BasicConfigurator;
import org.joda.time.DateTime;
import java.io.IOException;
import com.orange.profiling.common.utils.FsUtils;

/**
 * MainConcat is the driver class for the Genetic Concatenation MapReduce job.
 *
 * This job combines three types of input data:
 * 1. Concept weights - User content preferences and viewing patterns
 * 2. Best channels - Most watched live TV channels
 * 3. Best providers - Most used VOD (Video on Demand) providers
 *
 * The job processes these inputs to create comprehensive user profiles that include:
 * - Weighted concepts representing user interests
 * - Ratio of VOD vs Live TV viewing
 * - Top watched live channels
 * - Most used VOD providers
 *
 * Key Features:
 * - Supports multiple input formats through separate mappers
 * - Configurable top N selection for concepts, channels, and providers
 * - Special handling for opt-out users
 * - Expiration date management for profiles
 * - Output in a format compatible with downstream processing
 *
 * Configuration Parameters:
 * - topConcepts: Maximum number of concepts to keep per profile
 * - topLiveChannels: Maximum number of live channels to keep
 * - topOdProviders: Maximum number of VOD providers to keep
 *
 * <AUTHOR>
 */
public class MainConcat {
    private static final Log LOG = LogFactory.getLog(MainConcat.class);

    private static final int ARG_LENGTH = 6; // Augmenté de 5 à 6
    private static final int INPUT_CONCEPTS_WEIGHTS_IDX = 0;
    private static final int INPUT_BEST_CHANNELS_IDX = 1;
    private static final int INPUT_BEST_PROVIDERS_IDX = 2;
    private static final int PROCESS_DATE_IDX = 3;
    private static final int OUTPUTDIR_IDX = 4;
    private static final int OUTPUTDIR_OTT_IDX = 5; // Nouveau paramètre
    private static final int TOP_CONCEPTS_IDX = 6; // Décalé
    private static final int TOP_LIVE_CHANNELS_IDX = 7; // Décalé
    private static final int TOP_OD_PROVIDERS_IDX = 8; // Décalé

    private static final String JOB_NAME = "ute.ravenne.genetic.concat";

    public static final String PROCESS_DATE = "processDate";
    public static final String PROCESS_DAY = "processDay";

    public static final String OPTOUT_AID = "999999999";



    /**
     * @param args
     *            args
     * @throws IOException
     *             Exception
     * @throws ClassNotFoundException
     *             Exception
     * @throws InterruptedException
     *             Exception
     * @throws FailedJobException
     *             Exception
     */
    public static void main(final String[] args)
            throws IOException, ClassNotFoundException, InterruptedException, FailedJobException {

        BasicConfigurator.configure();

        // get param
        if (args.length < ARG_LENGTH) {
            String mess = "Takes " + ARG_LENGTH
                    + " arguments : inputPathConceptsWeights "
                    + "inputPathBestchannels inputPathBestProviders "
                    + "processDate outputDir outputDirOtt "
                    + "[topConcepts topLiveChannels topOdProviders]";
            LOG.error(mess);
            throw new IllegalArgumentException(mess);
        }

        Path inputPathConceptsWeights = new Path(args[INPUT_CONCEPTS_WEIGHTS_IDX]);
        Path inputPathBestchannels = new Path(args[INPUT_BEST_CHANNELS_IDX]);
        Path inputPathBestProviders = new Path(args[INPUT_BEST_PROVIDERS_IDX]);

        String processDate = args[PROCESS_DATE_IDX];

        Path outputDir = new Path(args[OUTPUTDIR_IDX]);
        Path outputDirOtt = new Path(args[OUTPUTDIR_OTT_IDX]); // Nouveau paramètre

        // Create configuration
        UteConfiguration conf = new UteConfiguration();
        conf.set(PROCESS_DATE, processDate);
        conf.set("outputDirOtt", outputDirOtt.toString()); // Passer le chemin OTT au reducer
        // compute processing day : today, we must generate profil for tommorrow
        String processDay = getProcessDay(processDate);
        conf.set(PROCESS_DAY, processDay);

        setOptionalTopConf(conf, ConcatReducer.CONF_TOP_CONCEPTS, TOP_CONCEPTS_IDX, args);
        setOptionalTopConf(conf, ConcatReducer.CONF_TOP_LIVE_CHANNELS, TOP_LIVE_CHANNELS_IDX, args);
        setOptionalTopConf(conf, ConcatReducer.CONF_TOP_OD_PROVIDERS, TOP_OD_PROVIDERS_IDX, args);

        // Create job
        Job job = Job.getInstance(conf, JOB_NAME);
        job.setJarByClass(MainConcat.class);
        job.setNumReduceTasks(200);

        // Setup MapReduce
        job.setMapperClass(ConceptsWeightsMapper.class);
        job.setCombinerClass(ConcatCombiner.class);
        job.setReducerClass(ConcatReducer.class);

        // Specify key / value
        job.setOutputKeyClass(Text.class);
        job.setOutputValueClass(Text.class);

        // Input
        MultipleInputs.addInputPath(job, inputPathConceptsWeights, TextInputFormat.class,
                        ConceptsWeightsMapper.class);
        MultipleInputs.addInputPath(job, inputPathBestchannels, TextInputFormat.class,
                        BestChannelsProvidersMapper.class);
        MultipleInputs.addInputPath(job, inputPathBestProviders, TextInputFormat.class,
                        BestChannelsProvidersMapper.class);

        // Output
        FileOutputFormat.setOutputPath(job, outputDir);
        job.setOutputFormatClass(TextOutputFormat.class);

        // Delete output if exists
        FsUtils fsUtils = new FsUtils(outputDir,conf);
        if (fsUtils.getFs().exists(outputDir)) {
            fsUtils.getFs().delete(outputDir, true);
        }

        // Execute job
        boolean succeed = job.waitForCompletion(true);

        if (!succeed) {
            throw new FailedJobException(JOB_NAME);
        }
        // Statistics
        UteCountersWriter.writeAlternateCounters(job, fsUtils.getFs(), outputDir);
    }

    private static String getProcessDay(String processDate) {
        DateTime processDateTime = DatesUtils.DF_YYYYMMDD.parseDateTime(processDate);
        return DatesUtils.DF_DAY_OF_WEEK.print(processDateTime.plusDays(1));
    }

    private static void setOptionalTopConf(UteConfiguration conf, String confname, int index, String[] args) {
        if (args.length > index) {
            conf.set(confname, args[index]);
        }
    }
}
