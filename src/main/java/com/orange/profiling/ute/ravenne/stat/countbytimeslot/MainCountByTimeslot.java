package com.orange.profiling.ute.ravenne.stat.countbytimeslot;

import java.io.BufferedReader;
import java.io.IOException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.hadoop.fs.Path;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Job;
import org.apache.hadoop.mapreduce.lib.input.MultipleInputs;
import org.apache.hadoop.mapreduce.lib.input.TextInputFormat;
import org.apache.hadoop.mapreduce.lib.output.FileOutputFormat;
import org.apache.hadoop.mapreduce.lib.output.TextOutputFormat;
import org.apache.log4j.Logger;

import com.orange.profiling.common.exceptions.FailedJobException;
import com.orange.profiling.common.mapred.UteConfiguration;
import com.orange.profiling.common.mapred.UteCountersWriter;
import com.orange.profiling.common.utils.FieldsUtils;
import com.orange.profiling.common.utils.FsUtils;

/**
 * MapReduce job driver for analyzing concept usage patterns in the Ravenne system.
 *
 * Count occurrences of concepts in w, d, dp or dt timebox For each type of time
 * box, sum the occurrence of each concepts and compare the newly calculated
 * values to the one of previous week and send to kibana the delta over a
 * specific threshold
 *
 * This class manages a MapReduce workflow that:
 * 1. Processes concept occurrences across different timeboxes:
 *    - Weekly (w)
 *    - Daily (d)
 *    - Daily Prime (dp)
 *    - Daily Total (dt)
 * 2. Aggregates concept usage statistics
 * 3. Compares current values with previous week's data
 * 4. Reports significant changes to Kibana monitoring
 *
 * The job processes two main inputs:
 * - Concept weights data from Ravenne
 * - AID (Anonymous ID) counter data for normalization
 *
 * Results are used for:
 * - Concept popularity tracking
 * - Usage pattern analysis
 * - Trend detection
 * - Performance monitoring
 */
public class MainCountByTimeslot {
    private static final Logger LOGGER = Logger.getLogger(MainCountByTimeslot.class);

    /** Required number of command line arguments */
    private static final int ARG_LENGTH = 4;
    /** Index of Ravenne concepts weights input path in args array */
    private static final int INPUT_RAVENNE_CONCEPTS_WEIGHTS = 0;
    /** Index of AID counter input path in args array */
    private static final int INPUT_RAVENNE_AID_COUNTER = 1;
    /** Index of output directory path in args array */
    private static final int OUTPUT_DIR = 2;
    /** Index of process date in args array */
    private static final int PROCESS_DATE = 3;

    /** Counter for tracking weighted AIDs */
    private static final com.orange.profiling.ute.ravenne.ponderation.Counters NB_AID_COUNTER =
            com.orange.profiling.ute.ravenne.ponderation.Counters.WEIGHTED_AID;
    /** Pattern for extracting AID count from counter file */
    private static final Pattern NB_AID_PATTERN =
            Pattern.compile(FieldsUtils.TAB+NB_AID_COUNTER.toString()+FieldsUtils.TAB+"(\\d+)"+FieldsUtils.TAB);

    /** Job identifier for Hadoop */
    private static final String JOB_NAME = "ute:ravenne.stat.countbytimeslot";

    /** Configuration key for total number of AIDs */
    public static final String CONF_NB_AID = "NB_AID";
    /** Configuration key for processing date */
    public static final String CONF_PROCESS_DATE = "PROCESS_DATE";

    /** Path to input file containing concept weights */
    private String inputRavenneConceptsWeights = "";
    /** Path to input file containing AID counter data */
    private String inputRavenneAidCounter = "";
    /** Path to output directory */
    private String outputDir = "";
    /** Date for which processing is being done */
    private String processDate = "";

    /** Hadoop MapReduce job instance */
    protected Job job;

    /**
     * Main entry point for the MapReduce job.
     *
     * @param args Command line arguments:
     *             [0] - Input path for concept weights data
     *             [1] - Input path for AID counter data
     *             [2] - Output directory path
     *             [3] - Process date
     * @throws IOException If there are I/O errors
     * @throws InterruptedException If the job is interrupted
     * @throws ClassNotFoundException If required classes are not found
     * @throws FailedJobException If the MapReduce job fails
     */
    public static void main(final String[] args)
            throws IOException, InterruptedException, ClassNotFoundException, FailedJobException {
        MainCountByTimeslot main = new MainCountByTimeslot();
        main.doTreatment(args);
    }

    /**
     * Orchestrates the complete MapReduce job workflow.
     *
     * @param args Command line arguments
     * @throws IOException If there are I/O errors
     * @throws ClassNotFoundException If required classes are not found
     * @throws InterruptedException If the job is interrupted
     * @throws FailedJobException If the MapReduce job fails
     */
    private void doTreatment(String[] args)
            throws IOException, ClassNotFoundException, InterruptedException, FailedJobException {
        readArgs(args);

        initJob();
        addConfiguration();
        setMappers();
        setCombiner();
        setReducer();
        prepareOutput();
        launchJob();
        doPostTreatment();
    }

    /**
     * Validates and processes command line arguments.
     *
     * @param args Command line arguments array
     * @throws IllegalArgumentException if argument count is incorrect
     */
    private void readArgs(final String[] args) {
        if (args.length != ARG_LENGTH) {
            String mess = "Takes " + ARG_LENGTH + " arguments : "
                    + "inputRavenneConceptsWeights inputRavenneAidCounter outputDir processDate";
            LOGGER.error(mess);
            throw new IllegalArgumentException(mess);
        }
        inputRavenneConceptsWeights = args[INPUT_RAVENNE_CONCEPTS_WEIGHTS];
        inputRavenneAidCounter = args[INPUT_RAVENNE_AID_COUNTER];
        outputDir = args[OUTPUT_DIR];
        processDate = args[PROCESS_DATE];
    }

    /**
     * Initializes the Hadoop MapReduce job with basic configuration.
     *
     * @throws IOException If job initialization fails
     */
    private void initJob() throws IOException {
        // Create configuration
        UteConfiguration conf = new UteConfiguration();
        // Create job
        job = Job.getInstance(conf, getJobName());
        job.setJarByClass(this.getClass());
    }

    /**
     * Adds required configuration parameters to the job.
     * Includes total AID count and process date.
     *
     * @throws IOException If reading configuration fails
     */
    private void addConfiguration() throws IOException {
        addConfigurationItem(CONF_NB_AID, getNbAidFromCounter());
        addConfigurationItem(CONF_PROCESS_DATE, processDate);
    }

    /**
     * Extracts the total AID count from the counter file.
     * Returns "1" as default if count cannot be determined.
     *
     * @return String representation of AID count
     * @throws IOException If reading counter file fails
     */
    private String getNbAidFromCounter() throws IOException {
        FsUtils fsUtils = new FsUtils(job.getConfiguration());
        try(BufferedReader br = fsUtils.getReaderForFile(inputRavenneAidCounter)) {
            String line = br.readLine();
            while(line != null) {
                Matcher matcher = NB_AID_PATTERN.matcher(line);
                if (matcher.find()) {
                    return matcher.group(1);
                }
                line = br.readLine();
            }
        }
        return "1";
    }

    /**
     * Adds a configuration item to the job configuration.
     *
     * @param confName Configuration parameter name
     * @param confValue Configuration parameter value
     */
    private void addConfigurationItem(String confName, String confValue) {
        job.getConfiguration().set(confName, confValue);
    }

    /**
     * Configures the mapper phase of the MapReduce job.
     * Sets up input paths and key/value classes for concept weight processing.
     */
    private void setMappers() {
        job.setMapperClass(ConceptsWeightsMapper.class);
        MultipleInputs.addInputPath(job, new Path(inputRavenneConceptsWeights),
                TextInputFormat.class, ConceptsWeightsMapper.class);

        // Specify key / value
        job.setOutputKeyClass(Text.class);
        job.setOutputValueClass(Text.class);
    }

    /**
     * Sets the combiner class for the MapReduce job.
     * Used for partial aggregation of concept counts to optimize network transfer.
     */
    private void setCombiner() {
        job.setCombinerClass(CountByTimeslotCombiner.class);
    }

    /**
     * Sets the reducer class for the MapReduce job.
     * Handles final aggregation and comparison of concept counts.
     */
    private void setReducer() {
        job.setReducerClass(CountByTimeslotReducer.class);
    }

    /**
     * Prepares the output directory and sets output format.
     * Deletes existing output directory if present.
     *
     * @throws IOException If output directory operations fail
     */
    private void prepareOutput() throws IOException {
        Path outputPath = new Path(outputDir);
        FileOutputFormat.setOutputPath(job, outputPath);
        job.setOutputFormatClass(TextOutputFormat.class);

        // Delete output if exists
        FsUtils fsUtilsOutput = new FsUtils(outputPath,job.getConfiguration());
        if (fsUtilsOutput.getFs().exists(outputPath)) {
            fsUtilsOutput.getFs().delete(outputPath, true);
        }
    }

    /**
     * Executes the MapReduce job and monitors completion.
     *
     * @throws ClassNotFoundException If required classes are not found
     * @throws IOException If job execution encounters I/O errors
     * @throws InterruptedException If job is interrupted
     * @throws FailedJobException If job fails to complete successfully
     */
    private void launchJob() throws ClassNotFoundException, IOException, InterruptedException, FailedJobException {
        boolean succeeded = job.waitForCompletion(true);
        if (!succeeded) {
            throw new FailedJobException(getJobName());
        }
    }

    /**
     * Performs post-job processing tasks.
     * Currently handles counter writing and Kibana reporting.
     *
     * @throws ClassNotFoundException If required classes are not found
     * @throws IOException If post-processing encounters I/O errors
     */
    private void doPostTreatment() throws ClassNotFoundException, IOException {
        writeCounters();
    }

    /**
     * Writes job counters to output location and sends concept statistics to Kibana.
     *
     * @throws IOException If writing counters fails
     * @throws ClassNotFoundException If required counter classes are not found
     */
    private void writeCounters() throws IOException, ClassNotFoundException {
        Path outputPath = new Path(outputDir);
        FsUtils fsUtils = new FsUtils(outputPath,job.getConfiguration());
        UteCountersWriter.writeAlternateCounters(job, fsUtils.getFs(), outputPath, NbProfilCounter.getGroups());
        NbProfilCounter.sendKibanaCounterNbByConcepts(job);
    }

    /**
     * Returns the job name for Hadoop job tracking.
     *
     * @return Job name string
     */
    private String getJobName() {
        return JOB_NAME;
    }

}
