package com.orange.profiling.ute.ravenne.ponderation;

/** Class used to compute specific weight for a program vie, according
 * the view duration and the days since it has been viewed
 */
public class PonderationFunction {
    private static final String SEP=";";
    private static final String EOL="\n";

    // If zap duration is more than MAX_DURATION, we consider a zap_duration equal to MAX_DURATION (minutes)
    private static final int MAX_DURATION = 150;
    // NUMERATOR is at least equals to 1
    private static final double CALC_MIN_NUM = 1.0;
    // we use a base 2 logarithm
    private static final double CALC_LOG_BASE = Math.log(2);
    // As zapDuration is given in seconds we should transform it in minutes
    private static final int CALC_MINUTES = 60;


    // If the program has been seen more than MAX_DAYS ago, we consider it has been seen exactly MAX_DAYS ago
    private static final int MAX_DAYS = 84;
    // we use power with base 2
    private static final double CALC_POW_BASE = 2.0;

    // To make difference between values we multiply the result by 100 before rounding
    private static final long PRECISION = 100;

    public static long weight(int zapDuration, int nbDays) {

        /** NUMERATOR is minimum of 1 and bas 2 logarithm of zap duration in minutes
         * Its values go from 1 for a zap duration under 2 minutes,
         * to 7,23 for a zap duration of 150 minutes (MAX_DURATION)
         */
        double num = Math.max(CALC_MIN_NUM,
                              Math.log(Math.min(MAX_DURATION, zapDuration/ CALC_MINUTES)) / CALC_LOG_BASE
                );


        /** DENOMINATOR is (2 powered by (1 + days since the program has been viewed / MAX_DAYS) ) minus 1
         * Its values go from 1 for 1 day since the program has been view
         * to 3 for 84 days since program has been viewed
         */
        if (nbDays < 0 || nbDays > MAX_DAYS) {
            nbDays = MAX_DAYS;
        }
        if (nbDays < 1) {
            nbDays = 1;
        }
        double nbDaysAsDouble = nbDays;
        double denom = Math.pow(CALC_POW_BASE, 1+ (nbDaysAsDouble/MAX_DAYS)) - 1;

        /** Result is multiply by PRECISION to differentiate the values
         * Weights go from 33 for a program viewed less than 2 minutes 84 days ago
         * to 711 for a program viewed 2 hours and a half, yesterday
         */
        return Math.round(PRECISION*num/denom);
    }

    /**
     * Affichage de toutes les valeurs pour analyse de la fonction de pondération
     * @param args
     */
    public static void main(String[] args) {
        StringBuilder sb = new StringBuilder(SEP);
        int day;
        int duration;
        for(day=1; day<85; day++) {
            sb.append(day+SEP);
        }
        sb.append(EOL);
        for(duration=60;duration<9601;duration+=60) {
            sb.append(duration+SEP);
            for(day=1; day<85; day++) {
                long w = weight(duration, day);
                sb.append(w+SEP);
            }
            sb.append(EOL);
        }
        System.out.println(sb.toString());
    }
}
