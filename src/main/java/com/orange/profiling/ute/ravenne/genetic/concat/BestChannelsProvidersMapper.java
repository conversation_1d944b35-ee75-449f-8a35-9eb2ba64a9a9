package com.orange.profiling.ute.ravenne.genetic.concat;

import com.orange.profiling.common.utils.FieldsUtils;
import com.orange.profiling.ute.ravenne.file.BestChannelsProviders;
import com.orange.profiling.ute.ravenne.genetic.Counters;
import com.orange.profiling.ute.ravenne.genetic.dao.TimeboxFilter;

import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Mapper;

import java.io.IOException;

/**
 * BestChannelsProvidersMapper processes user viewing data for channels and VOD providers.
 * 
 * This mapper handles the second and third input types for the genetic concatenation job:
 * viewing duration data for both live TV channels and VOD providers. It:
 * 
 * 1. Processes input records containing:
 *    - User ID (aid)
 *    - Timebox (viewing time period)
 *    - Type indicator (Live TV or VOD)
 *    - Provider durations (viewing time per channel/provider)
 * 
 * 2. For each valid record:
 *    - Validates input format
 *    - Emits records for individual users and opt-out aggregate
 *    - Filters records based on target day processing
 * 
 * Key Features:
 * - Handles both Live TV channels and VOD providers
 * - Input validation and error counting
 * - Support for opt-out aggregation
 * - Time-based filtering of records
 * 
 * Output Format:
 * Key: aid-timebox
 * Value: [LIVE|VOD]\tproviderDurations
 * 
 * <AUTHOR>
 */
public class BestChannelsProvidersMapper extends Mapper<Object, Text, Text, Text> {
    public static final int OUT_DURATIONS_IDX = 1;
    public static final int OUT_NB_AID = 2;

    private TimeboxFilter timeboxFilter;
    private BestChannelsProviders bestChannelsProviders = new BestChannelsProviders();
    private Text aidTimeboxKey = new Text();
    private Text outValue = new Text();

    @Override
    public final void setup(final Context context) {
        String processDay = context.getConfiguration().get(MainConcat.PROCESS_DAY);
        timeboxFilter = new TimeboxFilter(processDay);
    }

    @Override
    public final void map(final Object key, final Text value, final Context context)
            throws IOException, InterruptedException {

        bestChannelsProviders.setValue(value.toString());
        if (bestChannelsProviders.checkFormat()) {
            String timebox = bestChannelsProviders.getField(BestChannelsProviders.TIMEBOX);

            String liveOrVod = bestChannelsProviders.getField(BestChannelsProviders.LIVE_OR_VOD);
            String providersDurations = bestChannelsProviders.getField(BestChannelsProviders.PROVIDERS_DURATIONS);

            outValue.set(String.join(FieldsUtils.TAB, liveOrVod, providersDurations));

            if (timeboxFilter.isAllowed(timebox, false)) {
                String aid = bestChannelsProviders.getField(BestChannelsProviders.AID);
                aidTimeboxKey.set(String.join(FieldsUtils.DASH, aid, timebox));
                context.write(aidTimeboxKey, outValue);
            }
            else {
                context.getCounter(Counters.PROVIDERS_NOT_IN_TARGET_DAY).increment(1);
            }

            if (timeboxFilter.isAllowed(timebox, true)) {
                aidTimeboxKey.set(String.join(FieldsUtils.DASH, MainConcat.OPTOUT_AID, timebox));
                context.write(aidTimeboxKey, outValue);
            }

        }
        else {
            context.getCounter(Counters.BAD_FORMAT).increment(1);
        }
    }

}
