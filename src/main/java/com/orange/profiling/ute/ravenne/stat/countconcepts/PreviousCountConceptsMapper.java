package com.orange.profiling.ute.ravenne.stat.countconcepts;

import java.io.IOException;
import java.util.regex.Pattern;

import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Mapper;

import com.orange.profiling.common.utils.FieldsUtils;

public class PreviousCountConceptsMapper extends Mapper<Object, Text, Text, Text> {

    private static final String TAB = FieldsUtils.TAB;
    private static final Pattern TAB_PATTERN = FieldsUtils.TAB_PATTERN;

    private static final int CSV_MIN_LENGTH = 3;
    private static final int TIMEBOX_IDX = 0;
    private static final int CONCEPT_IDX = 1;
    private static final int TOTAL_IDX = 2;

    public static final String OUT_ID_YESTERDAY = "Y";
    public static final String KEY_SEPARATOR = ConceptsWeightsMapper.KEY_SEPARATOR;

    private Text outputKey = new Text();
    private Text outputValue = new Text();

    @Override
    public final void map(final Object key, final Text value, final Context context)
            throws IOException, InterruptedException {

        String[] csv = TAB_PATTERN.split(value.toString());
        if (csv.length >= CSV_MIN_LENGTH) {
            String timebox = csv[TIMEBOX_IDX];
            String concept = csv[CONCEPT_IDX];
            String total = csv[TOTAL_IDX];

            outputKey.set(String.join(KEY_SEPARATOR, timebox, concept));
            outputValue.set(OUT_ID_YESTERDAY+TAB+total);
            context.write(outputKey, outputValue);
        }
    }
}
