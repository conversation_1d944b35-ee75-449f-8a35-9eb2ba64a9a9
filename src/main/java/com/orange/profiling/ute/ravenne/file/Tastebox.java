package com.orange.profiling.ute.ravenne.file;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;

import com.orange.profiling.common.file.AbstractProfilingHdfsFile;
import com.orange.profiling.common.utils.FieldsUtils;


/**
 * File /user/profiling-ute/private/generated/ute/ravenne/tastebox/[YYYY/WW]/part-r-*
 * Generated by Ravenne
 * Tastebox of top ponderatedConcepts per aid and timeslot, with total duration
 *
 * Format
 *   aid TAB ponderatedConcepts TAB daytimeslot TAB totalduration
 *
 *     - aid : aid of the client
 *     - ponderatedConcepts : list of ponderated concepts
 *     - daytimeslot : day and timeslot
 *     - totalDuration  : total duration of view (live + vod)
 *
 * Pig :
 *     filtered = LOAD '/user/profiling-ute/private/generated/ute/ravenne/tastebox/[YYYY/WW]/part-r-*'
 *                USING PigStorage('\t') AS (aid:chararray, ponderatedConcepts:chararray, daytimeslot:chararray,
 *                      totalDuration:long);
 *
 */
public class Tastebox extends AbstractProfilingHdfsFile {
    public static final int AID = 0;
    public static final int PONDERATED_CONCEPTS = 1;
    public static final int DAY_TIMESLOT = 2;
    public static final int TOTAL_DURATION = 3;

    private static final Map<String, Integer> FIELDS = new HashMap<>();
    static {
        FIELDS.put("aid", AID);
        FIELDS.put("ponderatedConcepts", PONDERATED_CONCEPTS);
        FIELDS.put("daytimeslot", DAY_TIMESLOT);
        FIELDS.put("totalDuration", TOTAL_DURATION);
    }

    private static final String SEPARATOR = FieldsUtils.TAB;
    private static final Pattern PATTERN = FieldsUtils.TAB_PATTERN;

    @Override
    public Map<String, Integer> getFields() {
        return FIELDS;
    }

    @Override
    public String getSeparator() {
        return SEPARATOR;
    }

    @Override
    public Pattern getSplitPattern() {
        return PATTERN;
    }

}
