package com.orange.profiling.ute.ravenne.stat.countconcepts;

import java.io.IOException;
import org.apache.hadoop.fs.FileSystem;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Job;
import org.apache.hadoop.mapreduce.lib.input.MultipleInputs;
import org.apache.hadoop.mapreduce.lib.input.TextInputFormat;
import org.apache.hadoop.mapreduce.lib.output.FileOutputFormat;
import org.apache.hadoop.mapreduce.lib.output.TextOutputFormat;
import org.apache.log4j.Logger;

import com.orange.profiling.common.exceptions.FailedJobException;
import com.orange.profiling.common.mapred.UteConfiguration;
import com.orange.profiling.common.mapred.UteCountersWriter;
import com.orange.profiling.common.utils.FsUtils;

/**
 * MapReduce job driver for analyzing and counting concept occurrences in genetic profiles.
 * 
 * This class processes concept weights data across different timeboxes (weekly, daily, etc.)
 * and compares the current counts with previous week's data. It identifies significant changes
 * in concept occurrences based on a configurable threshold and reports these changes to Kibana
 * for monitoring and analysis.
 * 
 * The job workflow consists of:
 * 1. Reading concept weights from input data
 * 2. Processing previous week's counts for comparison
 * 3. Aggregating concept occurrences using MapReduce
 * 4. Identifying changes above the specified threshold
 * 5. Generating reports for monitoring
 * 
 * Input:
 * - Ravenne concepts weights data
 * - Previous week's concept count data
 * 
 * Output:
 * - Current concept counts
 * - Change statistics for monitoring
 * - Kibana-compatible reporting data
 */
public class MainCountConcepts {
    /** Logger for class-specific logging */
    private static final Logger LOGGER = Logger.getLogger(MainCountConcepts.class);

    /** Required number of command line arguments */
    private static final int ARG_LENGTH = 5;
    /** Index of input Ravenne concepts weights path in args array */
    private static final int INPUT_RAVENNE_CONCEPTS_WEIGHTS = 0;
    /** Index of delta threshold parameter in args array */
    private static final int PARAM_THRESHOLD = 1;
    /** Index of output directory path in args array */
    private static final int OUTPUT_DIR = 2;
    /** Index of previous week's output directory path in args array */
    private static final int PREVIOUS_YEARWEEK_OUTPUT_DIR = 3;
    /** Index of process date in args array */
    private static final int PROCESS_DATE = 4;

    /** Job name identifier for Hadoop MapReduce */
    private static final String JOB_NAME = "ute:ravenne.stat.countconcepts";

    /** Configuration key for delta threshold */
    public static final String CONF_THRESHOLD = "DELTA_THRESHOLD";
    /** Configuration key for process date */
    public static final String CONF_PROCESS_DATE = "PROCESS_DATE";

    /** Path to input Ravenne concepts weights data */
    private String inputRavenneConceptsWeights = "";
    /** Threshold for identifying significant changes in concept counts */
    private String deltaThreshold = "";
    /** Output directory path for current job results */
    private String outputDir = "";
    /** Path to previous week's output for comparison */
    private String previousYearWeekOutputDir = "";
    /** Date for which the process is being run */
    private String processDate = "";

    /** Hadoop MapReduce job instance */
    protected Job job;

    /**
     * Main entry point for the concept counting job.
     * 
     * @param args Command line arguments:
     *             [0] - Input Ravenne concepts weights path
     *             [1] - Delta threshold for change detection
     *             [2] - Output directory path
     *             [3] - Previous week's output directory path
     *             [4] - Process date
     * @throws IOException If there are I/O errors during job execution
     * @throws InterruptedException If the job is interrupted
     * @throws ClassNotFoundException If required classes are not found
     * @throws FailedJobException If the MapReduce job fails
     */
    public static void main(final String[] args)
            throws IOException, InterruptedException, ClassNotFoundException, FailedJobException {
        MainCountConcepts main = new MainCountConcepts();
        main.doTreatment(args);
    }

    /**
     * Orchestrates the complete workflow of the concept counting job.
     * 
     * @param args Command line arguments for job configuration
     * @throws IOException If there are I/O errors
     * @throws ClassNotFoundException If required classes are not found
     * @throws InterruptedException If the job is interrupted
     * @throws FailedJobException If the MapReduce job fails
     */
    private void doTreatment(String[] args)
            throws IOException, ClassNotFoundException, InterruptedException, FailedJobException {
        readArgs(args);
        initJob();
        addConfiguration();
        setMappers();
        setCombiner();
        setReducer();
        prepareOutput();
        launchJob();
        doPostTreatment();
    }

    /**
     * Validates and processes command line arguments.
     * 
     * @param args Array of command line arguments
     * @throws IllegalArgumentException If the number of arguments is incorrect
     */
    private void readArgs(final String[] args) {
        if (args.length != ARG_LENGTH) {
            String mess = "Takes " + ARG_LENGTH + " arguments : "
                    + "inputRavenneConceptsWeights deltaThreshold outputDir previousYearWeek processDate";
            LOGGER.error(mess);
            throw new IllegalArgumentException(mess);
        }
        inputRavenneConceptsWeights = args[INPUT_RAVENNE_CONCEPTS_WEIGHTS];
        deltaThreshold = args[PARAM_THRESHOLD];
        outputDir = args[OUTPUT_DIR];
        previousYearWeekOutputDir = args[PREVIOUS_YEARWEEK_OUTPUT_DIR];
        processDate = args[PROCESS_DATE];
    }

    /**
     * Initializes the Hadoop MapReduce job with UteConfiguration.
     * 
     * @throws IOException If job initialization fails
     */
    private void initJob() throws IOException {
        // Create configuration
        UteConfiguration conf = new UteConfiguration();
        // Create job
        job = Job.getInstance(conf, getJobName());
        job.setJarByClass(this.getClass());
    }

    /**
     * Adds job-specific configuration parameters including threshold and process date.
     */
    private void addConfiguration() {
        addConfigurationItem(CONF_THRESHOLD, deltaThreshold);
        addConfigurationItem(CONF_PROCESS_DATE, processDate);
    }

    /**
     * Adds a single configuration item to the job configuration.
     * 
     * @param confName Configuration parameter name
     * @param confValue Configuration parameter value
     */
    private void addConfigurationItem(String confName, String confValue) {
        job.getConfiguration().set(confName, confValue);
    }

    /**
     * Configures input paths and mappers for both current and previous week's data.
     * Sets up ConceptsWeightsMapper for current data and PreviousCountConceptsMapper
     * for previous week's data.
     */
    private void setMappers() {
        job.setMapperClass(ConceptsWeightsMapper.class);
        MultipleInputs.addInputPath(job, new Path(inputRavenneConceptsWeights),
                TextInputFormat.class, ConceptsWeightsMapper.class);

        MultipleInputs.addInputPath(job, new Path(previousYearWeekOutputDir),
                TextInputFormat.class, PreviousCountConceptsMapper.class);

        // Specify key / value
        job.setOutputKeyClass(Text.class);
        job.setOutputValueClass(Text.class);
    }

    /**
     * Configures the combiner class for partial aggregation of concept counts.
     */
    private void setCombiner() {
        job.setCombinerClass(CountConceptCombiner.class);
    }

    /**
     * Configures the reducer class for final aggregation and comparison of concept counts.
     */
    private void setReducer() {
        job.setReducerClass(CountConceptReducer.class);
    }

    /**
     * Prepares the output directory and configures output format.
     * Deletes existing output directory if present.
     * 
     * @throws IOException If output directory preparation fails
     */
    private void prepareOutput() throws IOException {
        Path outputPath = new Path(outputDir);
        FileOutputFormat.setOutputPath(job, outputPath);
        job.setOutputFormatClass(TextOutputFormat.class);

        // Delete output if exists
        FsUtils fsUtils = new FsUtils(outputPath,job.getConfiguration());
        if (fsUtils.getFs().exists(outputPath)) {
            fsUtils.getFs().delete(outputPath, true);
        }
    }

    /**
     * Launches the MapReduce job and waits for completion.
     * 
     * @throws ClassNotFoundException If required classes are not found
     * @throws IOException If job execution encounters I/O errors
     * @throws InterruptedException If job execution is interrupted
     * @throws FailedJobException If the job fails to complete successfully
     */
    private void launchJob() throws ClassNotFoundException, IOException, InterruptedException, FailedJobException {
        boolean succeeded = job.waitForCompletion(true);
        if (!succeeded) {
            throw new FailedJobException(getJobName());
        }
    }

    /**
     * Performs post-job processing including counter writing.
     * 
     * @throws ClassNotFoundException If required classes are not found
     * @throws IOException If post-processing encounters I/O errors
     */
    private void doPostTreatment() throws ClassNotFoundException, IOException {
        writeCounters();
    }

    /**
     * Writes job counters to output for monitoring and analysis.
     * 
     * @throws IOException If counter writing fails
     * @throws ClassNotFoundException If required classes are not found
     */
    private void writeCounters() throws IOException, ClassNotFoundException {
        Path outputPath = new Path(outputDir);
        FsUtils fsUtilsOutput = new FsUtils(outputPath,job.getConfiguration());
        UteCountersWriter.writeAlternateCounters(job, fsUtilsOutput.getFs(), outputPath);
    }

    /**
     * Returns the job name identifier.
     * 
     * @return The job name string
     */
    private String getJobName() {
        return JOB_NAME;
    }

}
