package com.orange.profiling.ute.ravenne.dao;

import java.util.Map;
import java.util.TreeMap;
import com.orange.profiling.common.utils.FieldsUtils;
import com.orange.profiling.ute.ravenne.util.ValuedKeyUtils;

/**
 * For a given timebox, represents
 *   - weightedConcepts
 *   - LIVE and VOD global duration
 *   - liveChannel and odProvider durations
 *
 * We use this class to read and aggregte concepts and duration from filtered zaps.
 * - we add concepts occurrences from a list of concepts with addConcepts
 * - we add live or vod duration with addDuration
 * - we add liveChannel or odProvider duration with addChannelProviderDuration
 *
 * We then use this to aggregate several TimeboxZapAggregation with aggregate :
 * - we aggregate weights of same concepts from a map concept => weight with aggregateWeightedConcepts
 * - we add live and vod duration with aggregateDuration
 * - we add liveChannel and odProvider duration with aggregateChannelProviderDuration
 *
 * We can directly use addConceptWeights to add ConceptsWeights Map or String
 *
 */
public class TimeboxZapAggregation {

    private static final int PRIME = 31;

    public static final String LIVE = "LIVE";
    public static final String VOD = "VOD";
    public static final String[] BROADCAST_TYPES = { LIVE, VOD };

    private String timebox = "";
    private Map<String, Long> weightedConcepts = new TreeMap<>();
    private long liveDuration = 0L;
    private long vodDuration = 0L;
    private Map<String, Long> liveChannelDuration = new TreeMap<>();
    private Map<String, Long> odProviderDuration = new TreeMap<>();

    public TimeboxZapAggregation(String timebox) {
        this.timebox = timebox;
    }

    /**
     * @return the timebox
     */
    public String getTimebox() {
        return timebox;
    }

    /**
     * @return the weightedConcepts
     */
    public Map<String, Long> getWeightedConcepts() {
        return weightedConcepts;
    }

    /**
     * @param weightedConcepts
     *            the weightedConcepts to set
     */
    public void setWeightedConcepts(Map<String, Long> weightedConcepts) {
        this.weightedConcepts = weightedConcepts;
    }

    /**
     * @return the liveDuration
     */
    public long getLiveDuration() {
        return liveDuration;
    }

    /**
     * @return the vodDuration
     */
    public long getVodDuration() {
        return vodDuration;
    }

    /**
     * @return the liveChannelDuration
     */
    public Map<String, Long> getLiveChannelDuration() {
        return liveChannelDuration;
    }

    /**
     * @param liveChannelDuration
     *            the liveChannelDuration to set
     */
    public void setLiveChannelDuration(Map<String, Long> liveChannelDuration) {
        this.liveChannelDuration = liveChannelDuration;
    }

    /**
     * @return the odProviderDuration
     */
    public Map<String, Long> getOdProviderDuration() {
        return odProviderDuration;
    }

    /**
     * @param odProviderDuration
     *            the odProviderDuration to set
     */
    public void setOdProviderDuration(Map<String, Long> odProviderDuration) {
        this.odProviderDuration = odProviderDuration;
    }

    /**
     * Add a new concepts list to weighted concepts The weight of a concept is the
     * number of occurrences of the concept
     *
     * @param concepts
     *            list of concepts (no weights)
     */
    public void addConcepts(String concepts, long weight) {
        String[] conceptsList = FieldsUtils.COMMA_PATTERN.split(concepts);
        for (String concept : conceptsList) {
            if ((!concept.isEmpty()) && (!FieldsUtils.SLASH.equals(concept))) {
                weightedConcepts.merge(concept, weight, Long::sum);
            }
        }
    }

    /**
     * Add LIVE or VOD duration to the durations
     *
     * @param liveOrVod
     * @param zapDuration
     */
    public void addDuration(String liveOrVod, long zapDuration) {
        if (LIVE.equals(liveOrVod)) {
            liveDuration += zapDuration;
        }
        else if (VOD.equals(liveOrVod)) {
            vodDuration += zapDuration;
        }
    }

    /**
     * Add liveChanel or odProvider duration
     *
     * @param liveOrVod
     * @param channelProvider
     * @param zapDuration
     */
    public void addChannelProviderDuration(String liveOrVod, String channelProvider, long zapDuration) {
        if (LIVE.equals(liveOrVod)) {
            liveChannelDuration.merge(channelProvider, zapDuration, Long::sum);
        }
        else if (VOD.equals(liveOrVod)) {
            odProviderDuration.merge(channelProvider, zapDuration, Long::sum);
        }
    }

    /**
     * Aggregates into this the values of another TimeboxZapAggregation
     *
     * @param timeslotInfo
     */
    public void aggregate(TimeboxZapAggregation timeslotInfo) {
        aggregateWeightedConcepts(timeslotInfo);
        aggregateDuration(timeslotInfo);
        aggregateChannelProviderDuration(timeslotInfo);
    }

    /**
     * Aggregates into this the concept weights of the given TimeboxZapAggregation
     *
     * @param timeslotInfo
     */
    public void aggregateWeightedConcepts(TimeboxZapAggregation timeslotInfo) {
        addConceptWeights(timeslotInfo.getWeightedConcepts());
    }

    /**
     * Aggregates into this the weights of the given map concept => weight
     *
     * @param conceptsWeights
     */
    public void addConceptWeights(Map<String, Long> conceptsWeights) {
        for (Map.Entry<String, Long> entry : conceptsWeights.entrySet()) {
            String concept = entry.getKey();
            Long weight = entry.getValue();
            if ((!concept.isEmpty()) && (!FieldsUtils.SLASH.equals(concept))) {
                weightedConcepts.merge(concept, weight, Long::sum);
            }
        }
    }

    /**
     * Aggregates into this the weights of the given
     * concept1=weight,concept2=weight,... String
     *
     * @param weightedConceptString
     */
    public void addConceptWeights(String weightedConceptString) {
        addConceptWeights(ValuedKeyUtils.parseStringToKeyValue(weightedConceptString));
    }

    /**
     * Add live and vod duration to this live and vod duration
     *
     * @param timeslotInfo
     */
    public void aggregateDuration(TimeboxZapAggregation timeslotInfo) {
        this.liveDuration += timeslotInfo.getLiveDuration();
        this.vodDuration += timeslotInfo.getVodDuration();
    }

    /**
     * Add liveChannel and odProvider duration into this liveChannel and odProvider
     * duration
     *
     * @param timeslotInfo
     */
    public void aggregateChannelProviderDuration(TimeboxZapAggregation timeslotInfo) {
        addLiveChannelDuration(timeslotInfo.getLiveChannelDuration());
        addOdProviderDuration(timeslotInfo.getOdProviderDuration());
    }

    /**
     * Add into this the durations of the given map liveChannel => duration
     *
     * @param liveChannelDuration
     */
    public void addLiveChannelDuration(Map<String, Long> liveChannelDuration) {
        for (Map.Entry<String, Long> entry : liveChannelDuration.entrySet()) {
            String liveChanel = entry.getKey();
            Long duration = entry.getValue();
            if (!liveChanel.isEmpty()) {
                this.liveChannelDuration.merge(liveChanel, duration, Long::sum);
            }
        }
    }

    /**
     * Aggregates into this the durations of the given
     * liveChannel1=duration,liveChannel2=duration,... String
     *
     * @param liveChannel1DurationString
     */
    public void addLiveChannelDuration(String liveChannelDurationString) {
        addLiveChannelDuration(ValuedKeyUtils.parseStringToKeyValue(liveChannelDurationString));
    }

    /**
     * Add into this the durations of the given map odProvider => duration
     *
     * @param odProviderDuration
     */
    public void addOdProviderDuration(Map<String, Long> odProviderDuration) {
        for (Map.Entry<String, Long> entry : odProviderDuration.entrySet()) {
            String odProvider = entry.getKey();
            Long duration = entry.getValue();
            if (!odProvider.isEmpty()) {
                this.odProviderDuration.merge(odProvider, duration, Long::sum);
            }
        }
    }

    /**
     * Aggregates into this the durations of the given
     * odProvider1=duration,odProvider2=duration,... String
     *
     * @param odProviderDurationString
     */
    public void addOdProviderDuration(String odProviderDurationString) {
        addOdProviderDuration(ValuedKeyUtils.parseStringToKeyValue(odProviderDurationString));
    }

    /**
     * Normalize weightedConcepts map. The normalization is to replace concept name
     * by only root/leaf As there could be duplicated, we add weights of same
     * root/leaf.
     *
     * @param weightedConceptMap
     * @return
     */
    public void normalizeConceptsName() {
        Map<String, Long> finalWeightedConceptMap = new TreeMap<>();
        for (Map.Entry<String, Long> entry : weightedConcepts.entrySet()) {
            String finalConceptKey = keepRootAndLeaf(entry.getKey());
            finalWeightedConceptMap.put(finalConceptKey,
                    finalWeightedConceptMap.getOrDefault(finalConceptKey, 0L) + entry.getValue());
        }
        weightedConcepts = finalWeightedConceptMap;
    }

    /**
     * From a concept name keep only root and leaf an returns root/leaf
     *
     * @param concept
     * @return
     */
    private static String keepRootAndLeaf(String concept) {
        // -1 ensure we have at least 1 token, even for "/" and ""
        String[] tokens = FieldsUtils.SLASH_PATTERN.split(concept, -1);
        return tokens[0] + FieldsUtils.SLASH + tokens[tokens.length - 1];
    }

    /**
     * Returns the string representation of the weightedConcepts map of this timebox
     *
     * @param weightedConceptsMap
     * @return
     */
    public String getWeightedConceptsString() {
        return ValuedKeyUtils.parseKeyValueToString(weightedConcepts);
    }

    /**
     * Returns the string representation of the liveChannel duration map of this timebox
     *
     * @param weightedConceptsMap
     * @return
     */
    public String getLiveChannelDurationString() {
        return ValuedKeyUtils.parseKeyValueToString(this.liveChannelDuration);
    }

    /**
     * Returns the string representation of the odProvider duration map of this timebox
     *
     * @param weightedConceptsMap
     * @return
     */
    public String getOdProviderDurationString() {
        return ValuedKeyUtils.parseKeyValueToString(this.odProviderDuration);
    }

    /*
     * (non-Javadoc)
     *
     * @see java.lang.Object#hashCode()
     */
    @Override
    public int hashCode() {
        int result = 1;
        result = PRIME * result + ((timebox == null) ? 0 : timebox.hashCode());
        result = PRIME * result + ((weightedConcepts == null) ? 0 : weightedConcepts.hashCode());
        result = PRIME * result + (int) (liveDuration ^ (liveDuration >>> 32));
        result = PRIME * result + (int) (vodDuration ^ (vodDuration >>> 32));
        result = PRIME * result + ((liveChannelDuration == null) ? 0 : liveChannelDuration.hashCode());
        result = PRIME * result + ((odProviderDuration == null) ? 0 : odProviderDuration.hashCode());
        return result;
    }

    /*
     * (non-Javadoc)
     *
     * @see java.lang.Object#equals(java.lang.Object)
     */
    @Override
    public boolean equals(Object obj) {
        boolean isequal = false;
        if ((obj != null) && (getClass() == obj.getClass())) {
            TimeboxZapAggregation other = (TimeboxZapAggregation) obj;
            isequal = isequal(timebox, other.timebox)
                    && isequal(liveDuration, other.liveDuration)
                    && isequal(vodDuration, other.vodDuration)
                    && isequal(weightedConcepts, other.weightedConcepts)
                    && isequal(liveChannelDuration, other.liveChannelDuration)
                    && isequal(odProviderDuration, other.odProviderDuration);
        }
        return isequal;
    }

    private static boolean isequal(Object thisAttr, Object otherAttr) {
        if (thisAttr == null) {
            return (otherAttr == null);
        }
        return thisAttr.equals(otherAttr);
    }

    @Override
    public String toString() {
        return "[" + timebox + " = liveDuration:" + liveDuration + ", vodDuration:" + vodDuration
                + ", weightedConcepts:" + getWeightedConceptsString()
                + ", liveChannelDuration:" + getLiveChannelDurationString()
                + ", odProviderDuration:" + getOdProviderDurationString()
                + "]";
    }

}
