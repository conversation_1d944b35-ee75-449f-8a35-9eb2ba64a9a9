package com.orange.profiling.ute.ravenne.ponderation;

import com.orange.profiling.common.file.generated.Optin;
import com.orange.profiling.common.utils.FieldsUtils;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Mapper;

import java.io.IOException;

/**
 * Generic mapper for OptinSummary
 * <p>
 * This mapper deals with GDPR compliance.
 * To use this mapper, you have to set following configurations :
 * - KEY_TYPE : field to use as a key
 * - FLAG_FILE : file to read where we search a flag value to know if should be GDPR compliant or not
 * - FLAG_NAME : name of the file to read
 * <p>
 * If the flag is 1, we should be GDPR compliant else we should not.
 * <p>
 * The Mapper returns the key from KEY_TYPE + all fields in the value
 * <p>
 * If we are not GDPR compliant :
 * - if KEY_TYPE is Optin.AID_HASH, we replace it by Optin.AID
 * - if KEY_TYPE is Optin.MAC_HASH, we replace it by Optin.MAC
 * - In value, fields AID_HASH and MAC_HASH are replaced by fields AID and MAC
 * <p>
 * If we are GDPR compliant, no change.
 */

public class OptinPonderationMapper extends Mapper<Object, Text, Text, Text> {
    public static final String KEY_TYPE = "KEY_TYPE";
    public static final String FLAG_VALUE = "FLAG_VALUE";
    public static final String OPTIN_TAG = "OPTIN";

    private boolean gdprCompliantFlag;
    private int keyIdx = Optin.AID;
    private int aidHashIdx = Optin.AID_HASH;
    private int macHashIdx = Optin.MAC_HASH;
    private final Optin optin = new Optin();
    private final Text outkey = new Text();
    private final Text outvalue = new Text();

    /** Configuration key for the domain */
    public static final String DEVICE_STB = "stb";
    public static final String DEVICE_MOBILE = "mobile";
    public static final String DEVICE_WEB = "web";
    public static final String DEVICE_SMARTTV = "smarttv";
    private static final String DASH = FieldsUtils.DASH;

    /*
     * (non-Javadoc)
     *
     * @see org.apache.hadoop.mapreduce.Mapper#map(KEYIN, VALUEIN,
     * org.apache.hadoop.mapreduce.Mapper.Context)
     */
    @Override
    public void map(Object key, Text value, Context context) throws IOException, InterruptedException {
        optin.setValue(value.toString());
        if (optin.checkFormat() && shouldWriteOutput(optin)) {
            String aid = buildKey(optin);
            // 4 output key with device (stb, mobile, web, smarttv)
            writeOutput(context, aid, DEVICE_STB);
            writeOutput(context, aid, DEVICE_WEB);
            writeOutput(context, aid, DEVICE_MOBILE);
            writeOutput(context, aid, DEVICE_SMARTTV);
        }
    }

    /**
     * Only map lines of clients which are optin calcul.
     *
     * @param optin
     * @return
     */
    protected boolean shouldWriteOutput(Optin optin) {
        return isOptin(optin.getField(Optin.OPTIN_CALCUL));
    }

    private static boolean isOptin(String optin) {
        int optinValue = 0;
        try {
            optinValue = Integer.parseInt(optin);
        } catch (NumberFormatException e) {
            // Nothing to do, optin not an integer, 0 by default
        }

        return optinValue > 0;
    }

    protected String buildKey(Optin optin) {
        return optin.getField(keyIdx);
    }

    protected String buildValue(Optin optin) {
        return String.join(FieldsUtils.TAB,
                OPTIN_TAG,
                optin.getField(Optin.AID),
                optin.getField(Optin.OPTIN_CALCUL),
                optin.getField(Optin.MAC),
                optin.getField(Optin.STB_TYPE),
                optin.getField(aidHashIdx),
                optin.getField(macHashIdx),
                optin.getField(Optin.OPTIN_TV),
                optin.getField(Optin.OPTIN_PUB),
                optin.getField(Optin.DATE_ACTIVATION_PUB),
                optin.getField(Optin.ORIGIN_PUB),
                optin.getField(Optin.OPTIN_STAT),
                optin.getField(Optin.OPTIN_MEDIAMETRIE),
                optin.getField(Optin.LAST_OPTIN_DATETIME_MEDIAMETRIE)
        );
    }

    protected void writeOutput(Context context, String aid, String device) throws IOException, InterruptedException {
        String outKey = aid + DASH + device; // composite key
        outkey.set(outKey);
        outvalue.set(buildValue(optin));
        context.write(outkey, outvalue);
    }

    @Override
    protected void setup(Context context) {

        setGdprCompliantFlag(context.getConfiguration());
        setHashIdx(context.getConfiguration());
    }

    private void setGdprCompliantFlag(Configuration configuration) {
        gdprCompliantFlag = "1".equals(configuration.get(FLAG_VALUE));
    }

    private void setHashIdx(Configuration configuration) {
        keyIdx = configuration.getInt(KEY_TYPE, Optin.AID);
        aidHashIdx = Optin.AID_HASH;
        macHashIdx = Optin.MAC_HASH;
        if (!gdprCompliantFlag) {
            aidHashIdx = Optin.AID;
            macHashIdx = Optin.MAC;
            if (keyIdx == Optin.AID_HASH) {
                keyIdx = Optin.AID;
            }
            if (keyIdx == Optin.MAC_HASH) {
                keyIdx = Optin.MAC;
            }
        }
    }
}
