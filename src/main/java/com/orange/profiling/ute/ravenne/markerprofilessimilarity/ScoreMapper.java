package com.orange.profiling.ute.ravenne.markerprofilessimilarity;

import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Mapper;
import java.io.IOException;

public class ScoreMapper extends Mapper<Object, Text, Text, Text> {
    @Override
    public void map(Object key, Text value, Context context)
            throws IOException, InterruptedException {

        String[] parts = value.toString().split("\t");
        if (parts.length == 4) {  // markerName aid conceptWeight totalWeight
            context.write(
                    new Text(parts[0] + "\t" + parts[1]),  // clé = markerName_aid
                    new Text(parts[2] + "\t" + parts[3])   // valeur = conceptWeight_totalWeight
            );
        }
    }
}
