package com.orange.profiling.ute.ravenne.profiltype.profils.dao;

public class BestSimilarityVector {
    private ConceptsVector vector;
    private Double similarity;


    public BestSimilarityVector(ConceptsVector vector, Double similarity) {
        super();
        this.vector = vector;
        this.similarity = similarity;
    }


    /**
     * @return the vector
     */
    public ConceptsVector getVector() {
        return vector;
    }


    /**
     * @return the similarity
     */
    public Double getSimilarity() {
        return similarity;
    }



}
