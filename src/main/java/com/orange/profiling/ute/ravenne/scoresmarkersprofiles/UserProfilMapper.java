package com.orange.profiling.ute.ravenne.scoresmarkersprofiles;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.orange.profiling.common.file.generated.Progbymac;
import com.orange.profiling.ute.ravenne.dao.TimeboxZapAggregation;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Mapper;

import java.io.IOException;


public class UserProfilMapper extends Mapper<Object, Text, Text, Text> {
    private ObjectMapper jsonMapper = new ObjectMapper();

    @Override
    public void map(Object key, Text value, Context context)
            throws IOException, InterruptedException {
        try {
            context.getCounter("Debug", "TotalLines").increment(1);

            String[] parts = value.toString().split(";;");
            if (parts.length != 2) {
                context.getCounter("Debug", "InvalidMainParts").increment(1);
                return;
            }

            String aid = parts[0];
            String[] subParts = parts[1].split("\\|");
            if (subParts.length < 4) {
                context.getCounter("Debug", "InvalidSubParts").increment(1);
                return;
            }

            if (!subParts[0].equals("w")) {
                context.getCounter("Debug", "NotTypeW").increment(1);
                return;
            }

            String jsonPart = subParts[3];
            JsonNode root = jsonMapper.readTree(jsonPart);
            JsonNode thRav = root.get("th_rav");

            if (thRav != null && thRav.isArray()) {
                for (JsonNode conceptNode : thRav) {
                    String concept = conceptNode.get("concept").asText();
                    String weight = conceptNode.get("weight").asText();

                    context.write(
                            new Text(concept),
                            new Text("PROFIL:" + aid + ":" + jsonPart)
                    );
                    context.getCounter("Debug", "EmittedConcepts").increment(1);
                }
            }
        } catch (Exception e) {
            context.getCounter("Error", "MapperException").increment(1);
            // Optionnel : log l'erreur complète
            context.getCounter("Error", e.getClass().getSimpleName()).increment(1);
        }
    }
}
