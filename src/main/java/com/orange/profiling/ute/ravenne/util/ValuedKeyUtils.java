package com.orange.profiling.ute.ravenne.util;

import static java.util.stream.Collectors.joining;

import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.TreeMap;
import java.util.stream.Collectors;

import com.orange.profiling.common.utils.FieldsUtils;

public class ValuedKeyUtils {

    private ValuedKeyUtils() {
        // Utility class
    }

    public static Map<String,Long> sorKeysByValue(Map<String, Long> keyValueMap) {

        return keyValueMap.entrySet()
                .stream()
                .sorted(ValuedKeyUtils::compareByValueReverseOrderThenByKey)
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (e1, e2) -> e1, LinkedHashMap::new));
    }

    public static int compareByValueReverseOrderThenByKey(Map.Entry<String, Long> a, Map.Entry<String, Long> b) {
        // First compare value in reverse order (b compare to a instead of a compare to b)
        int compareValueReverseOrder = b.getValue().compareTo(a.getValue());
        if (compareValueReverseOrder != 0) {
            return compareValueReverseOrder;
        }
        else {
            // If value are equals, compare key in alphabetical order
            return a.getKey().compareTo(b.getKey());
        }
    }

    /**
     * Return map from key1=value1,key2=value2 String.
     * Helper method
     *
     * @param keyValueString
     * @return
     */
    public static final Map<String, Long> parseStringToKeyValue(String keyValueString) {
        if ((keyValueString == null) || (keyValueString.isEmpty())) {
            return new TreeMap<>();
        }
        return Arrays.asList(FieldsUtils.COMMA_PATTERN.split(keyValueString))
                .stream()
                .filter(e -> e!=null && !e.isEmpty())
                .map(FieldsUtils.EQUAL_PATTERN::split)
                .collect(
                        Collectors.toMap(e -> e[0].trim(), e -> ValuedKeyUtils.getSafelyLong(e, 1),
                                (oldvalue, newvalue) -> oldvalue + newvalue, TreeMap::new));
    }

    /**
     * Return key1=value1,key2=value2 String from map key => value.
     * Helper method.
     *
     * @param map
     * @return
     */
    public static final String parseKeyValueToString(Map<String, Long> map) {
        return sorKeysByValue(map).entrySet().stream()
                .map(e -> e.getKey() + FieldsUtils.EQUAL + (e.getValue()))
                .collect(joining(FieldsUtils.COMMA));
    }

    /**
     * Get integer from parsed array of string value at given index. Return 0 if any
     * problem.
     *
     * @param csv
     * @param index
     * @return
     */
    private static Long getSafelyLong(String[] csv, int index) {
        return FieldsUtils.getSafelyLong(FieldsUtils.get(csv, index), 0L);
    }

    /**
     * Return map from concept weighted String. Helper method.
     *
     * @param weightedConceptString
     * @return
     */
    public static final Map<String, Double> parseWeightedConceptsToMapDouble(String weightedConceptString) {
        if ((weightedConceptString == null) || (weightedConceptString.isEmpty())) {
            return new TreeMap<>();
        }
        return Arrays.asList(FieldsUtils.COMMA_PATTERN.split(weightedConceptString))
                .stream()
                .map(FieldsUtils.EQUAL_PATTERN::split)
                .collect(
                        Collectors.toMap(e -> e[0].trim(), e -> ValuedKeyUtils.getSafelyDouble(e, 1),
                                (oldvalue, newvalue) -> oldvalue + newvalue, TreeMap::new));
    }

    /**
     * Get double from parsed array of string value at given index. Return 0.0 if
     * any problem.
     *
     * @param csv
     * @param index
     * @return
     */
    private static Double getSafelyDouble(String[] csv, int index) {
        return FieldsUtils.getSafelyDouble(FieldsUtils.get(csv, index), 0.0);
    }


}
