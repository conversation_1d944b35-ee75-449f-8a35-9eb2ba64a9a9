package com.orange.profiling.ute.ravenne.genetic.concat;

import java.io.IOException;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Mapper;
import com.orange.profiling.common.utils.FieldsUtils;
import com.orange.profiling.ute.ravenne.genetic.Counters;
import com.orange.profiling.ute.ravenne.file.ConceptsWeights;
import com.orange.profiling.ute.ravenne.genetic.dao.TimeboxFilter;

/**
 * ConceptsWeightsMapper processes user viewing data to extract concept weights and viewing ratios.
 * 
 * This mapper handles the first input type for the genetic concatenation job:
 * user viewing data that includes concept weights and viewing durations. It:
 * 
 * 1. Processes input records containing:
 *    - User ID (aid)
 *    - Timebox (viewing time period)
 *    - Weighted concepts (user interests)
 *    - Live TV viewing duration
 *    - VOD viewing duration
 * 
 * 2. For each valid record:
 *    - Computes the VOD/Live viewing ratio
 *    - Emits records for both individual users and the opt-out aggregate
 *    - Filters records based on target day processing
 * 
 * Key Features:
 * - Input validation and error counting
 * - Ratio computation between VOD and Live viewing
 * - Support for opt-out aggregation
 * - Time-based filtering of records
 * 
 * Output Format:
 * Key: aid-timebox
 * Value: CW\tconceptWeights\tratioVodLive
 * 
 * <AUTHOR>
 *
 */
public class ConceptsWeightsMapper extends Mapper<Object, Text, Text, Text> {

    private static final double PERCENT = 100.0;

    public static final String CONCEPTS_MARK = "CW";
    public static final int OUT_CONCEPTS_IDX = 1;
    public static final int OUT_RATIO_IDX = 2;
    public static final int OUT_NB_AID = 3;

    private TimeboxFilter timeboxFilter;
    private ConceptsWeights conceptsWeights = new ConceptsWeights();
    private Text aidTimeboxKey = new Text();
    private Text outValue = new Text();


    @Override
    public final void setup(final Context context) {
        String processDay = context.getConfiguration().get(MainConcat.PROCESS_DAY);
        timeboxFilter = new TimeboxFilter(processDay);
    }
    /*
     * (non-Javadoc)
     *
     * @see org.apache.hadoop.mapreduce.Mapper#map(KEYIN, VALUEIN,
     * org.apache.hadoop.mapreduce.Mapper.Context)
     */
    @Override
    public final void map(final Object key, final Text value,
            final Context context) throws IOException, InterruptedException {

        conceptsWeights.setValue(value.toString());
        if (conceptsWeights.checkFormat()) {
            String timebox = conceptsWeights.getField(ConceptsWeights.TIMEBOX);
            String weightedConcepts = conceptsWeights.getField(ConceptsWeights.PONDERATED_CONCEPTS);
            String liveDuration = conceptsWeights.getField(ConceptsWeights.LIVE_DURATION);
            String vodDuration = conceptsWeights.getField(ConceptsWeights.VOD_DURATION);

            String aid = conceptsWeights.getField(ConceptsWeights.AID);
            int ratioVodLive = computeRatio(liveDuration, vodDuration);
            outValue.set(String.join(FieldsUtils.TAB, CONCEPTS_MARK,
                    weightedConcepts, Integer.toString(ratioVodLive)));

            if (timeboxFilter.isAllowed(timebox, false)) {
                aidTimeboxKey.set(String.join(FieldsUtils.DASH, aid, timebox));
                context.write(aidTimeboxKey, outValue);
            }
            else {
                context.getCounter(Counters.CONCEPTS_NOT_IN_TARGET_DAY).increment(1);
            }

            if(timeboxFilter.isAllowed(timebox, true)){
                aidTimeboxKey.set(String.join(FieldsUtils.DASH, MainConcat.OPTOUT_AID, timebox));
                context.write(aidTimeboxKey, outValue);
            }

        }
        else {
            context.getCounter(Counters.BAD_FORMAT).increment(1);
        }
    }

    private static int computeRatio(String liveDuration, String vodDuration) {
        int ratio = 0;
        long live = FieldsUtils.getSafelyLong(liveDuration, 0L);
        long vod = FieldsUtils.getSafelyLong(vodDuration, 0L);
        if (live > 0 || vod > 0) {
            ratio = Math.toIntExact(Math.round(PERCENT*vod / (live + vod)));
        }
        return ratio;
    }
}