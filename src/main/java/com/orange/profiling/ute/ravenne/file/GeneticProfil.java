package com.orange.profiling.ute.ravenne.file;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;

import com.orange.profiling.common.file.AbstractProfilingHdfsFile;
import com.orange.profiling.common.utils.FieldsUtils;


/**
 * File /user/profiling-ute/tmp/ravenne/genetic-profils/optinprofile/[YYYY/WW]/part-r-*
 * File /user/profiling-ute/tmp/ravenne/genetic-profils/optinprofile/[YYYY/WW]/part-r-*
 * File /user/profiling-ute/private/generated/ravenne/genetic/[YYYY/WW]/part-r-*
 * Generated by Ravenne
 * Ponderation of concepts by aid and daytimeslot with sum of live and vod durations
 *
 * Format
 *   aid TAB daytimeslot TAB ponderatedConcepts TAB liveDuration TAB vodDuration
 *
 *     - aid : aid of the client
 *     - daytimeslot : day and timeslot
 *     - ponderatedConcepts : list of ponderated concepts
 *     - liveDuration : total duration of live view
 *     - vodDuration : total duration of vod view
 *
 * Pig :
 *     filtered = LOAD '/user/profiling-ute/private/generated/ravenne/genetic/[YYYY/WW]/part-r-*'
 *                USING PigStorage('\t') AS (aid:chararray, timebox:chararray, concept:chararray, weight:double);
 *
 */
public class GeneticProfil extends AbstractProfilingHdfsFile {
    public static final int AID = 0;
    public static final int TIMEBOX = 1;
    public static final int CONCEPT = 2;
    public static final int WEIGHT = 3;

    /** Specific concept name for ratio vod / (vod + live)
     *
     */
    public static final String RATIOVODLIVE = "RATIOVODLIVE";

    private static final Map<String, Integer> FIELDS = new HashMap<>();
    static {
        FIELDS.put("aid", AID);
        FIELDS.put("timebox", TIMEBOX);
        FIELDS.put("concept", CONCEPT);
        FIELDS.put("weight", WEIGHT);
    }

    private static final String SEPARATOR = FieldsUtils.TAB;
    private static final Pattern PATTERN = FieldsUtils.TAB_PATTERN;

    @Override
    public Map<String, Integer> getFields() {
        return FIELDS;
    }

    @Override
    public String getSeparator() {
        return SEPARATOR;
    }

    @Override
    public Pattern getSplitPattern() {
        return PATTERN;
    }

}
