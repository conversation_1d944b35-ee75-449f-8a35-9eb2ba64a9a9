package com.orange.profiling.ute.ravenne.profiltype.profils.dao;

import java.io.BufferedReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.log4j.Logger;
import org.json.simple.JSONArray;
import org.json.simple.JSONObject;
import org.json.simple.parser.JSONParser;
import com.orange.profiling.common.utils.FieldsUtils;

/**
 * <AUTHOR>
 */
public class CatalogueProfilVector {
    private static final Logger LOGGER = Logger.getLogger(CatalogueProfilVector.class);

    private static final int PROFIL_ID_IDX = 0;
    private static final int ONE = 1;
    private static final int TWO = 2;
    private static final int THREE = 3;

    private List<Profil> catalogue;

    public CatalogueProfilVector() {
        catalogue = new ArrayList<>();
    }

    public List<Profil> getCatalogue() {
        return catalogue;
    }


    public final void addProfil(Profil marqueur) {
        catalogue.add(marqueur);
    }

    public final Profil getProfil(String profilId) {
        for (Profil prof : catalogue) {
            if (prof.getProfilId().equals(profilId)) {
                return prof;
            }
        }
        return new Profil(profilId);
    }

    /**
     * @param weightedConcepts
     *            Map of (String, Double)
     * @return profils
     */
    public Map<String, BestSimilarityVector> searchForProfil(Map<String, Double> weightedConcepts) {

        Map<String, BestSimilarityVector> resultProfil = new HashMap<>();

        // Parcours des profils - pour chaque profil, on va calculer la
        // similarite cosinus - on garde la meilleure au sein du profil

        for (Profil prof : catalogue) {
            BestSimilarityVector bestSimilarityVector = prof.computeBestSimilarity(weightedConcepts);
            if (bestSimilarityVector.getSimilarity() > 0D) {
                resultProfil.put(prof.getProfilId(), bestSimilarityVector);
            }
        }
        return resultProfil;
    }

    /** Load catalog defining profil type id and name
     *
     * @param br
     *            BufferedReader
     */
    public final void loadProfils(final BufferedReader br) {
        try {
            String line = br.readLine();
            while (line != null) {
                if (line.startsWith("profileId")) {
                    line = br.readLine();
                    continue;
                }
                String[] csv = FieldsUtils.TAB_PATTERN.split(line);
                String profilId = csv[PROFIL_ID_IDX];
                Profil newProfil = new Profil(profilId);
                newProfil.setLabel(csv[ONE]);
                newProfil.setVarname(csv[TWO]);
                newProfil.setThreshold(Double.parseDouble(csv[THREE]));
                this.addProfil(newProfil);
                line = br.readLine();
            }
        }
        catch (IOException e) {
            LOGGER.error(e.getMessage(), e);
        }

    }

    /** Load catalog defining concepts vectors that are associated to profil type
     *
     * @param br
     *            BufferedReader
     */
    public final void loadProfilsVector(final BufferedReader br) {
        // create a json parser
        JSONParser parser = new JSONParser();

        try {

            Object obj;
            obj = parser.parse(br);
            JSONObject allProfilsJson = (JSONObject) obj;

            for (Profil profil : this.getCatalogue()) {
                // Recuperation des profils precedemment chargés
                JSONArray profilVectorArray = (JSONArray) allProfilsJson.get(profil.getProfilId());
                if (profilVectorArray != null) {
                    List<ConceptsVector> conceptVectorList = loadProfilVectorsJson(profilVectorArray);
                    for (ConceptsVector conceptsVector: conceptVectorList) {
                        profil.addVector(conceptsVector);
                    }
                }
            }
        }
        catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    /** Load vectors definition for a specific profil
     *
     * @param profilVectorArray
     * @return the list of loaded vectors
     */
    private static List<ConceptsVector> loadProfilVectorsJson(JSONArray profilVectorArray) {
        List<ConceptsVector> conceptVectorList = new ArrayList<>();
        for (int i = 0; i < profilVectorArray.size(); i++) {

            JSONObject conceptsVectorJson = (JSONObject) profilVectorArray.get(i);

            ConceptsVector conceptsVector = loadVectorJson(conceptsVectorJson);
            conceptVectorList.add(conceptsVector);
        }
        return conceptVectorList;
    }

    private static ConceptsVector loadVectorJson(JSONObject conceptsVectorJson) {
        ConceptsVector conceptsVector = new ConceptsVector();
        Set<String> conceptsList = conceptsVectorJson.keySet();
        for(String concept: conceptsList) {
            Double conceptValue = readConceptDoubleValue(conceptsVectorJson, concept);
            if (conceptValue != null) {
                conceptsVector.addConceptWeight(concept.trim(), conceptValue);
            }
        }
        return conceptsVector;
    }

    protected static Double readConceptDoubleValue(JSONObject conceptsVectorJson, String concept) {
        Object objectValue = conceptsVectorJson.get(concept);
        if (objectValue != null) {
            try {
                return Double.valueOf(objectValue.toString());
            }
            catch (Exception ex) {
                return null;
            }
        }
        else {
            return null;
        }
    }

}
