package com.orange.profiling.ute.ravenne.genetic.pnssend;

import com.orange.profiling.common.exceptions.FailedJobException;
import com.orange.profiling.common.file.generated.Optin;
import com.orange.profiling.common.mapred.UteConfiguration;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.io.NullWritable;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Job;
import org.apache.hadoop.mapreduce.lib.input.MultipleInputs;
import org.apache.hadoop.mapreduce.lib.input.TextInputFormat;
import org.apache.hadoop.mapreduce.lib.output.FileOutputFormat;

import java.io.IOException;

import com.orange.profiling.common.optin.OptinMapper;
import com.orange.profiling.common.utils.FsUtils;

/**
 * DesAnonymizeMain is the driver class for the genetic profile deanonymization MapReduce job.
 * 
 * This job processes genetic profiles and optin data to create deanonymized user profiles
 * that can be sent to the PNS (Profile Notification Service). It:
 * 
 * 1. Combines two types of input:
 *    - Genetic profiles: User viewing preferences and behaviors
 *    - Optin data: User consent and GDPR compliance information
 * 
 * 2. Processing Flow:
 *    - Maps anonymized profiles with their corresponding optin records
 *    - Validates GDPR consent flags
 *    - Generates deanonymized profiles for consenting users
 *    - Preserves special handling for opt-out aggregates (AID: 999999999)
 * 
 * Key Features:
 * - GDPR compliance through optin validation
 * - Support for both individual and opt-out profiles
 * - Configurable consent flag validation
 * - Parallel processing with 200 reduce tasks
 * 
 * Input Parameters:
 * 1. geneticPath: Path to genetic profile data
 * 2. optinPath: Path to optin/consent data
 * 3. gdprFlagValue: Required consent flag value
 * 4. outputDir: Output directory for deanonymized profiles
 * 
 * Output Format:
 * - Deanonymized profiles with real user IDs
 * - Only profiles for users with valid consent
 * - Preserved opt-out aggregate profiles
 * 
 * Usage:
 * java DesAnonymizeMain <genetic_path> <optin_path> <gdpr_flag> <output_dir>
 */
public class DesAnonymizeMain {
	
	public static final String JOB_NAME_DESANONYMIZED_GENETIC_PNS= "ute:desanonymized-genetic-pns";
	/**
	 * @param args String[]
	 * @throws ClassNotFoundException Exception
	 * @throws FailedJobException     Exception
	 * @throws InterruptedException   Exception
	 * @throws IOException            Exception
	 */

	public static void main(String[] args)
			throws ClassNotFoundException, FailedJobException, InterruptedException, IOException {
		// get param
		if (args.length != 4) {
			String mess = "Takes 4 arguments : genetic optinPath gdprFlagValue outputDir";
			throw new IllegalArgumentException(mess);
		}

		Path geneticPath = new Path(args[0]);
		Path optinPath = new Path(args[1]);
		String gdprFlagValue = args[2];
		Path outputDir = new Path(args[3]);

		// Create configuration
		UteConfiguration conf = new UteConfiguration(true);

		conf.set(OptinMapper.KEY_TYPE, String.valueOf(Optin.AID_HASH));
		conf.set(OptinMapper.FLAG_VALUE, gdprFlagValue);

		// Create job
		Job job = Job.getInstance(conf, JOB_NAME_DESANONYMIZED_GENETIC_PNS);
		job.setJarByClass(OptinMapper.class);

		// Setup MapReduce
		job.setMapperClass(OptinMapper.class);

		job.setReducerClass(DeAnonymizeReducer.class);
		job.setNumReduceTasks(200);

		// Specify key / value
		//If your Mapper emits different types than the Reducer,
		// you can set the types emitted by the mapper
        job.setMapOutputKeyClass(Text.class);
        job.setMapOutputValueClass(Text.class);        

		// Input
		MultipleInputs.addInputPath(job, optinPath, TextInputFormat.class, OptinMapper.class);
		MultipleInputs.addInputPath(job, geneticPath, TextInputFormat.class, DeAnoPnsMapper.class);
		// Output
		FileOutputFormat.setOutputPath(job, outputDir);

		// Delete output if exists
		FsUtils fsUtils = new FsUtils(outputDir,conf);
		if (fsUtils.getFs().exists(outputDir)) {
			fsUtils.getFs().delete(outputDir, true);
		}
		// Execute job
		boolean succeed = job.waitForCompletion(true);

		if (!succeed) {
			throw new FailedJobException(JOB_NAME_DESANONYMIZED_GENETIC_PNS);
		}
	}
}
