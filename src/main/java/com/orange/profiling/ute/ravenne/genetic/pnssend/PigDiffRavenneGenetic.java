package com.orange.profiling.ute.ravenne.genetic.pnssend;

import org.codehaus.jettison.json.JSONException;
import org.codehaus.jettison.json.JSONObject;

import com.orange.profiling.common.utils.FieldsUtils;
import com.orange.profiling.common.pns.file.PnsPig;
import com.orange.profiling.common.pns.filter.PigDiff;

/**
 * Specialized PigDiff implementation for Ravenne genetic profiles that handles profile comparison
 * and maintains statistics about changes in different profile components.
 * 
 * This class extends PigDiff to:
 * 1. Handle opt-out users specially (AID: 999999999)
 * 2. Track changes in specific profile components:
 *    - Ravenne scores (th_rav)
 *    - Channel preferences (th_ch)
 *    - Catchup/Provider preferences (th_ch_catchup)
 *    - Ratio values (th_rvl)
 * 3. Maintain counters for monitoring profile changes
 */
public class PigDiffRavenneGenetic extends PigDiff {
    /** Identifier for opt-out users */
    public static final String OPTOUT_AID = "999999999";

    /**
     * Compares PnsPig values and tracks differences in profile components.
     * For opt-out users (AID: 999999999), always returns true to ensure processing.
     * For other users, compares profile components and updates relevant counters.
     *
     * @param pnsPigToday Current day's PnsPig value
     * @param pnsPigPrevious Previous day's PnsPig value
     * @return true if profiles are different or user is opt-out, false otherwise
     */
    @Override
    public boolean pnsValueAreDifferent(PnsPig pnsPigToday, PnsPig pnsPigPrevious) {
        String aid = getAidFromKey(pnsPigToday.getField(PnsPig.KEY));
        if (OPTOUT_AID.equals(aid)) {
            getContext().getCounter(Counters.OPTOUT_UID_999999999).increment(1);
            return true;
        }
        boolean valueAreDifferent = super.pnsValueAreDifferent(pnsPigToday, pnsPigPrevious);
        if (valueAreDifferent) {
            statForDifferentProfil(pnsPigToday.getField(PnsPig.VALUE),pnsPigPrevious.getField(PnsPig.VALUE));
        }
        return valueAreDifferent;
    }

    /**
     * Extracts the AID (Anonymous Identifier) from a PnsPig key.
     * The AID is expected to be the first component in a semicolon-separated key.
     *
     * @param key PnsPig key to parse
     * @return The AID string, or empty string if key is empty
     */
    private String getAidFromKey(String key) {
        if (!key.isEmpty()) {
            String[] keyParts = FieldsUtils.SEMICOLON_PATTERN.split(key);
            return keyParts[0];
        }
        return "";
    }

    /**
     * Analyzes and tracks differences between two profile JSONs.
     * Updates counters for changes in:
     * - Ravenne scores (th_rav)
     * - Channel preferences (th_ch)
     * - Catchup/Provider preferences (th_ch_catchup)
     * - Ratio values (th_rvl)
     *
     * @param profilToday Current profile JSON string
     * @param profilYesterday Previous profile JSON string
     */
    private void statForDifferentProfil(String profilToday, String profilYesterday) {
        // stats for diff
        try {
            JSONObject jsonToday = new JSONObject(profilToday);
            JSONObject jsonYesterday = new JSONObject(profilYesterday);

            if (!sameValueForKey(jsonToday, jsonYesterday, "th_rav")) {
                getContext().getCounter(Counters.RAVENNE_DONT_EQUALS).increment(1);
            }
            if (!sameValueForKey(jsonToday, jsonYesterday, "th_ch")) {
                getContext().getCounter(Counters.CHANNEL_DONT_EQUALS).increment(1);
            }
            if (!sameValueForKey(jsonToday, jsonYesterday, "th_ch_catchup")) {
                getContext().getCounter(Counters.PROVIDER_DONT_EQUALS).increment(1);
            }
            if (!sameValueForKey(jsonToday, jsonYesterday, "th_rvl")) {
                getContext().getCounter(Counters.RATIO_DONT_EQUALS).increment(1);
            }
        }
        catch (JSONException e) {
            getContext().getCounter(Counters.MALFORMED_JSON).increment(1);
        }
    }

    /**
     * Compares a specific key's value between two JSON objects.
     * Handles cases where the key may be present in:
     * - Both objects
     * - Only one object
     * - Neither object
     *
     * @param jsonToday Current JSON object
     * @param jsonYesterday Previous JSON object
     * @param key Key to compare
     * @return true if values are the same, false if different
     * @throws JSONException if JSON parsing fails
     */
    private boolean sameValueForKey(JSONObject jsonToday, JSONObject jsonYesterday, String key)
            throws JSONException {

        if (jsonToday.has(key)) {
            if (jsonYesterday.has(key)) {
                String todayValue = jsonToday.get(key).toString();
                String yesterdayValue = jsonYesterday.get(key).toString();
                return todayValue.equals(yesterdayValue);
            }
            return false;
        }
        if (jsonYesterday.has(key)) {
            return false;
        }
        return true;
    }

}
