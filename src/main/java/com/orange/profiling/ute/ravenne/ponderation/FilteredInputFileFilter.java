package com.orange.profiling.ute.ravenne.ponderation;

import java.io.IOException;

import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.conf.Configured;
import org.apache.hadoop.fs.FileSystem;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.fs.PathFilter;
import org.apache.log4j.Logger;
import org.joda.time.DateTime;
import org.joda.time.DateTimeConstants;
import com.orange.profiling.common.utils.DatesUtils;
import com.orange.profiling.common.utils.FieldsUtils;
import com.orange.profiling.common.utils.FsUtils;
import com.orange.profiling.common.utils.HadoopGCSFileSystemFactory;

/**
 * A Hadoop PathFilter implementation that filters input files based on their temporal attributes
 * and location in the filesystem hierarchy. This filter is specifically designed to work with
 * files in the UTE Ravenne system that follow the naming convention:
 * private/generated/ute/ravenne/filtered/[YearOfWeek]/[WeekOfYear]/selected/part-r-NNNNN
 * 
 * The filter accepts files that:
 * 1. Are within the specified date range (between minDate and maxDate)
 * 2. Follow the correct file pattern for filtered data
 * 3. Are either directories or valid part files
 */
public class FilteredInputFileFilter extends Configured implements PathFilter {

    private static final Logger LOGGER = Logger.getLogger(FilteredInputFileFilter.class);

    /** Index position of the year from the end of the path when split by slashes */
    private static final int YEAR_OF_WEEK_INDEX_FROM_END = 4;
    /** Index position of the week from the end of the path when split by slashes */
    private static final int WEEK_OF_YEAR_INDEX_FROM_END = 3;

    /** Configuration key for the minimum date in the range */
    public static final String MIN_DATE = "minDate";
    /** Configuration key for the maximum date in the range */
    public static final String MAX_DATE = "maxDate";
    /** Identifier for filtered data in the path */
    public static final String FILTERED = "filtered";
    /** Configuration key for the output directory path */
    public static final String OUTPUT_DIR_PATH = "outputDir";

    /** Start date of the acceptable range */
    private DateTime beginRange;
    /** End date of the acceptable range */
    private DateTime endRange;
    /** Hadoop filesystem instance */
    private FileSystem fs;
    /** Hadoop configuration */
    Configuration conf;

    /**
     * Determines whether a given path should be accepted based on the filter criteria.
     * The method checks if:
     * 1. The path is a directory (always accepted)
     * 2. For files in the 'filtered' directory:
     *    - They must be part files
     *    - Their date (derived from year/week) must fall within the configured date range
     * 3. Files outside the 'filtered' directory are always accepted
     *
     * @param path The path to evaluate
     * @return true if the path should be accepted, false otherwise
     */
    @Override
    public final boolean accept(final Path path) {

        try {
            HadoopGCSFileSystemFactory fsFactory = new HadoopGCSFileSystemFactory();
            this.fs = fsFactory.get(path,this.conf);
            if (fs != null && fs.isDirectory(path)) {
                return true;
            }
        }
        catch (IOException e) {
            LOGGER.error(e.getMessage(), e);
            return false;
        }

        if (path.toString().contains(FILTERED)) {

            try {
                String[] csv = FieldsUtils.SLASH_PATTERN.split(path.toString());
                if(!csv[csv.length - 1].contains("part")) {
                    LOGGER.info("NOT PROCESS => "+path.toString());
                    return false;
                }
                String year = csv[csv.length - YEAR_OF_WEEK_INDEX_FROM_END];
                String week = csv[csv.length - WEEK_OF_YEAR_INDEX_FROM_END];
                String yearweek = year + FieldsUtils.SLASH + week;

                DateTime fileDate = DatesUtils.getWeekDayDateTime(yearweek, DateTimeConstants.WEDNESDAY);

                if ( DatesUtils.isBetweenExclusive(fileDate, beginRange, endRange) ) {
                    LOGGER.info("Process " + path.toString());
                    return true;
                }
                else {
                    return false;
                }
            }
            catch (Exception e) {
                return false;
            }
        }
        else {
            LOGGER.info("Process " + path.toString());
            return true;
        }
    }

    /**
     * Configures the filter with the necessary parameters from the Hadoop configuration.
     * Sets up:
     * - Begin range (Monday of the min date week)
     * - End range (Sunday of the max date week)
     * - FileSystem instance for the output directory
     *
     * @param config Hadoop configuration containing the necessary parameters
     */
    @Override
    public void setConf(final Configuration config) {
        if (config != null) {
            // begin
            String minDate = config.get(MIN_DATE);
            beginRange = DatesUtils.getWeekDayDateTime(minDate, DateTimeConstants.MONDAY);

            // end
            String maxDate = config.get(MAX_DATE);
            endRange = DatesUtils.getWeekDayDateTime(maxDate, DateTimeConstants.SUNDAY);
            String outputDir = config.get(OUTPUT_DIR_PATH);

            try {
                // File system
                HadoopGCSFileSystemFactory hdfsFactory = new HadoopGCSFileSystemFactory();
                fs = hdfsFactory.get(new Path(outputDir),config);
            }
            catch(IOException e) {
                LOGGER.error("Could not get fs with config",e);
            }
        }
    }
}
