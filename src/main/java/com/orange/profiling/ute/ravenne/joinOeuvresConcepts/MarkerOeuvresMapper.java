package com.orange.profiling.ute.ravenne.joinOeuvresConcepts;

import java.io.IOException;

import com.orange.profiling.ute.ravenne.file.MarkerOeuvre;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Mapper;

import com.orange.profiling.common.file.generated.Progbymac;
import com.orange.profiling.ute.ravenne.dao.TimeboxZapAggregation;


public class MarkerOeuvresMapper extends Mapper<Object, Text, Text, Text> {


    private MarkerOeuvre markerOeuvre = new MarkerOeuvre();
    private Text outputKey = new Text();
    private Text outputValue = new Text();

    @Override
    public final void map(final Object key, final Text value,
                          final Context context) throws IOException, InterruptedException {

        markerOeuvre.setValue(value.toString());
        String idOeuvre = markerOeuvre.getField(MarkerOeuvre.ID_OEUVRE);
        String markername = markerOeuvre.getField(MarkerOeuvre.MARKER_NAME);

        outputKey.set(idOeuvre);
        outputValue.set("MARKER:" + markername);
        context.write(outputKey, outputValue);
    }
}
