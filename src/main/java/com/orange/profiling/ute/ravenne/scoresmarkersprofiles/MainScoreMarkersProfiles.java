package com.orange.profiling.ute.ravenne.scoresmarkersprofiles;

import com.orange.profiling.common.exceptions.FailedJobException;
import com.orange.profiling.common.mapred.UteConfiguration;
import com.orange.profiling.common.utils.FsUtils;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Job;
import org.apache.hadoop.mapreduce.lib.input.MultipleInputs;
import org.apache.hadoop.mapreduce.lib.input.TextInputFormat;
import org.apache.hadoop.mapreduce.lib.output.FileOutputFormat;
import java.io.IOException;

public class MainScoreMarkersProfiles {

    private static final String JOB_SCORE_MARKERS_PROFILES = "ute:marqueurs-profiles-scores";
    private static final int EXPECTED_ARGS = 3;
    private static final String USAGE = "Takes 3 arguments: profilPath markerConceptsPath outputPath";
    
        public static void main(String[] args) throws ClassNotFoundException, FailedJobException, InterruptedException, IOException {
            if (args.length != EXPECTED_ARGS) {
                throw new IllegalArgumentException(USAGE);
            }


            // Chemins en tant que constantes
            final Path markerConceptsPath = new Path(args[0]);
            final Path profilPath = new Path(args[1]);
            final Path outputDir = new Path(args[2]);

            // Création et configuration du job
            Job job = configureJob(new UteConfiguration(true),
                    markerConceptsPath,
                    profilPath,
                    outputDir);

            // Exécution du job
            if (!job.waitForCompletion(true)) {
                throw new FailedJobException(JOB_SCORE_MARKERS_PROFILES);
            }
        }

    private static Job configureJob(UteConfiguration conf,
                                    Path markerConceptsPath,
                                    Path profilPath,
                                    Path outputDir)
            throws IOException {

        // Nettoyage du répertoire de sortie
       cleanOutputDir(conf, outputDir);

        // Configuration du job
        Job job = Job.getInstance(conf, JOB_SCORE_MARKERS_PROFILES);
        job.setJarByClass(MainScoreMarkersProfiles.class);

        // Configuration des types de sortie
        job.setMapOutputKeyClass(Text.class);
        job.setMapOutputValueClass(Text.class);
        job.setOutputKeyClass(Text.class);
        job.setOutputValueClass(Text.class);

        // Configuration du reducer
        job.setReducerClass(ScoreMarkersProfilesReducer.class);
        job.setNumReduceTasks(150);

        // Configuration des entrées multiples
        MultipleInputs.addInputPath(job, markerConceptsPath,
                TextInputFormat.class,
                MarkerConceptsMapper.class);
        MultipleInputs.addInputPath(job, profilPath,
                TextInputFormat.class,
                UserProfilMapper.class);

        // Configuration de la sortie
        FileOutputFormat.setOutputPath(job, outputDir);

        return job;
    }

    private static void cleanOutputDir(UteConfiguration conf, Path outputDir)
            throws IOException {
        FsUtils fsUtils = new FsUtils(outputDir, conf);
        if (fsUtils.getFs().exists(outputDir)) {
            fsUtils.getFs().delete(outputDir, true);
        }
    }
}
