package com.orange.profiling.ute.ravenne.genetic.dao;

import com.google.gson.annotations.Expose;

public class GeneticProfil implements Comparable<GeneticProfil> {

    @Expose
    private String concept;
    @Expose
    private String weight;

    public GeneticProfil(String concept, String weight) {
        super();
        this.concept = concept;
        this.weight = weight;
    }

    @Override
    public int compareTo(GeneticProfil geneticProfil) {
        return this.concept.compareTo(geneticProfil.concept);
    }

}