package com.orange.profiling.ute.ravenne.transformer;

import com.orange.profiling.ute.ravenne.file.Filtered;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Mapper;

import java.io.IOException;

public class TransformProfilingHashMapper extends Mapper<Object, Text, Text, Text> {

    private Text outputKey = new Text();
    private Text outputValue = new Text();
    /** Helper class to parse and validate input lines */
    private Filtered filteredLine = new Filtered();

    @Override
    public final void map(final Object key, final Text value, final Context context)
            throws IOException, InterruptedException {

        // Example :
        // 110250182	Genre Orange/série/autre/série,catégories/fiction,type/série	2	3	5	VOD	4974	1593522236	TVODM6REP-M6|catchuptv_m6_umts	M6CU0094425S_S_TVODM6REP_1	L'île aux mystères : quand le passé nous rattrape   stb
        filteredLine.setValue(value.toString());
        if (filteredLine.checkFormat()) {
            String aid = filteredLine.getField(Filtered.AID);
            outputKey.set(aid);
            outputValue.set(value);

            context.write(outputKey, outputValue);
        }
    }

}
