package com.orange.profiling.ute.ravenne.file;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;

import com.orange.profiling.common.file.AbstractProfilingHdfsFile;
import com.orange.profiling.common.utils.FieldsUtils;


/**
 * File /user/profiling-ute/tmp/ravenne/ponderation/[YYYY/WW]/bestchannelsproviders/LIVE|VOD/*-r-*
 * Generated by Ravenne
 * Ponderation of live channels or od providers by aid and timebox with sum of durations
 *
 * Format
 *   aid TAB timebox TAB liveOrVod TAB providersDurations
 *
 *     - aid : aid of the client
 *     - timebox : dxty, dxpz, dx ou w
 *     - liveOrVod : LIVE or VOD
 *     - providersDurations : list of LiveChannels or OdProviders with sum of durations
 *
 * Pig :
 *     filtered = LOAD '/user/profiling-ute/tmp/ravenne/ponderation/[YYYY/WW]/bestchannelsproviders/LIVE|VOD/*-r-*'
 *                USING PigStorage('\t') AS (aid:chararray, timebox:chararray, liveOrVod:chararray,
 *                      providersDurations:chararray);
 *
 */
public class BestChannelsProviders extends AbstractProfilingHdfsFile {
    public static final int AID = 0;
    public static final int TIMEBOX = 1;
    public static final int LIVE_OR_VOD = 2;
    public static final int PROVIDERS_DURATIONS = 3;
    

    private static final Map<String, Integer> FIELDS = new HashMap<>();
    static {
        FIELDS.put("aid", AID);
        FIELDS.put("timebox", TIMEBOX);
        FIELDS.put("liveOrVod", LIVE_OR_VOD);
        FIELDS.put("providersDurations", PROVIDERS_DURATIONS);
    }

    private static final String SEPARATOR = FieldsUtils.TAB;
    private static final Pattern PATTERN = FieldsUtils.TAB_PATTERN;

    @Override
    public Map<String, Integer> getFields() {
        return FIELDS;
    }

    @Override
    public String getSeparator() {
        return SEPARATOR;
    }

    @Override
    public Pattern getSplitPattern() {
        return PATTERN;
    }

}
