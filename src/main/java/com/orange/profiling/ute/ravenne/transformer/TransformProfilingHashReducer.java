package com.orange.profiling.ute.ravenne.transformer;

import com.orange.profiling.common.mapred.MosWriter;
import com.orange.profiling.common.utils.FsUtils;
import com.orange.profiling.ute.ravenne.file.Filtered;
import com.orange.profiling.ute.ravenne.filterandtimeslot.MainFilterAndTimeslot;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Reducer;
import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import com.orange.profiling.common.utils.FieldsUtils;
import org.apache.log4j.Logger;


public class TransformProfilingHashReducer extends Reducer<Text, Text, Text, Text> {

    /** Helper class to parse and validate input lines */
    private Filtered filteredLine = new Filtered();
    private String outputKey = "";
    private String outputValue = "";
    public static final String OTT_TAG = "ott";
    private static final int KEY_PROFILINGHASH_IDX = 0;
    private static final int KEY_AIDHASH_IDX = 1;
    private static final Logger LOGGER = Logger.getLogger(TransformProfilingHashReducer.class);
    private static final String DASH = FieldsUtils.DASH;
    Map<String, String> profilingHashMap = new HashMap<>();

    private MosWriter mos;

    @Override
    public final void setup(final Context context) throws IOException, InterruptedException {
        getMosWriter().open(context);
        FsUtils fsUtils = new FsUtils(context.getConfiguration());
        // load mapping data ProfilingHash => AidHash
        String mappingDataPath = context.getConfiguration().get(MainTransformProfilingHash.MAPPING_DATA);
        profilingHashMap = loadMappingData(fsUtils, mappingDataPath);
    }

    @Override
    public final void reduce(final Text key, final Iterable<Text> values, final Context context)
            throws IOException, InterruptedException {

            for (Text value : values) {

                filteredLine.setValue(value.toString());
                String device = filteredLine.getField(Filtered.SUPPORT_TYPE);
                String[] csv = FieldsUtils.TAB_PATTERN.split(value.toString(), -1);
                outputValue = excludeFirstElement(csv);

                if (key.toString().startsWith(OTT_TAG)) {
                    // mapping ProfilingHash => AidHash
                    String aidhash = profilingHashMap.get(key.toString());
                    if (aidhash != null) {
                        // key : AidHash-device
                        outputKey = aidhash + DASH + device;
                        mos.write(MainTransformProfilingHash.OUT_SELECTED_TRANSFORM, outputKey,
                                outputValue, MainTransformProfilingHash.OUT_SELECTED_TRANSFORM + "/part");
                    } else {
                        context.getCounter(Counters.NB_PROFILINGHASH_NO_MAPPING).increment(1);
                    }
                } else {
                    // for stb lines => Aid original (AidHash)
                    outputKey = key.toString() + DASH + device;
                    mos.write(MainTransformProfilingHash.OUT_SELECTED_TRANSFORM, outputKey,
                            outputValue, MainTransformProfilingHash.OUT_SELECTED_TRANSFORM + "/part");
                }
            }
    }

    private String excludeFirstElement(String[] zap){
        return String.join(FieldsUtils.TAB, Arrays.copyOfRange(zap,1, zap.length - 1));
    }

    public Map<String, String> loadMappingData(FsUtils fsUtils, String inputPathMapping) throws IOException {
        Map<String, String> mappingData = new HashMap<>();

        try (BufferedReader br = fsUtils.getReaderForFile(inputPathMapping)) {
                    String line;
                    while ((line = br.readLine()) != null) {
                        String[] csv = FieldsUtils.TAB_PATTERN.split(line);
                        String profilingHash = csv[KEY_PROFILINGHASH_IDX];
                        String aidhash = csv[KEY_AIDHASH_IDX];
                        mappingData.put(profilingHash, aidhash);
                    }
                } catch (IOException e) {
                    LOGGER.error(e.getMessage(), e);
                }

        return mappingData;
    }

    /**
     * Gets the current MosWriter instance.
     * Creates a new instance if none exists.
     *
     * @return The current MosWriter instance
     */
    private MosWriter getMosWriter() {
        if (mos == null) {
            mos = new MosWriter();
        }
        return mos;
    }

    /**
     * Sets a custom MosWriter instance (used in test to use a mock MosWriter).
     * Primarily used for testing to inject mock writers.
     *
     * @param mos The MosWriter instance to use
     */
    public void setMosWriter(MosWriter mos) {
        this.mos = mos;
    }

    @Override
    public void cleanup(Context context) throws IOException, InterruptedException {
        mos.close();
    }
}
