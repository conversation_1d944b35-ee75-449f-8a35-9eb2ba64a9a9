package com.orange.profiling.ute.ravenne.genetic.dao;

import java.util.ArrayList;
import java.util.SortedSet;
import java.util.TreeSet;

import com.google.gson.annotations.Expose;

public class ProfileJson {

    // Using a SortedSet ensure that genetic profils
    // will be sorted using GeneticProfil.compareTo
    @Expose
    private SortedSet<GeneticProfil> thRav;
    @Expose
    private ArrayList<String> thCh;
    @Expose
    private ArrayList<String> thChCatchup;
    @Expose
    private int thRvl;

    public ProfileJson() {
        super();
        this.thRav = new TreeSet<>();
        this.thCh = new ArrayList<>();
        this.thChCatchup = new ArrayList<>();
        this.thRvl = 0;
    }

    /**
     * @param concept name of the concept to add
     * @param weight weight given to the weight
     */
    public void addProfilGenetic(String concept, String weight) {
        GeneticProfil gp = new GeneticProfil(concept, weight);
        this.thRav.add(gp);
    }

    /**
     * @param bestChannel the bestChannel to add
     */
    public void addBestChannel(String bestChannel) {
        this.thCh.add(bestChannel);
    }

    public void addBestProvider(String bestProvider) {
        this.thChCatchup.add(bestProvider);
    }
    /**
     * @param thRvl the thRvl to set
     */
    public void setRatioVodLive(int ratioVodLive) {
        this.thRvl = ratioVodLive;
    }
}
