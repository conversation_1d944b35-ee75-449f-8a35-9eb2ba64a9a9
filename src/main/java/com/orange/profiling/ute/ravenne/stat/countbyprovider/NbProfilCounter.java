package com.orange.profiling.ute.ravenne.stat.countbyprovider;

import java.io.IOException;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;

import org.apache.hadoop.mapreduce.Counter;
import org.apache.hadoop.mapreduce.Job;
import org.apache.hadoop.mapreduce.Mapper.Context;

import com.orange.profiling.common.mapred.UteCountersLogger;
import com.orange.profiling.common.utils.FieldsUtils;

public class NbProfilCounter {
    protected static final String COUNTER_NB_BY_CHANNELS_GROUP = "NB_GENETIC_PROFILES_BY_NB_CHANNELS";
    protected static final String COUNTER_NB_BY_CHANNELS_FORMAT = "NB_GENETIC_PROFILES_BY_NB_CHANNELS%s_%02d";

    protected static String[] KIBANA_GROUPS = {
            COUNTER_NB_BY_CHANNELS_GROUP
    };

    private Context context;

    public NbProfilCounter(Context context) {
        this.context = context;
    }

    /** COUNTER : NB_GENETIC_PROFILES_BY_NB_CHANNELS_TYPE_NB
     * nombre de profils génétiques par type de timebox (w, d, dp ou dt)
     * et par nombres de CHANNELS (de 1 à 20)
     *
     * @param context
     * @param timeBoxType
     * @param nbChannels
     */
    public void countNbProfilsByTimeboxTypeAndnbChannels(String timeBoxType, int nbChannels) {
        String counternbChannels = String.format(COUNTER_NB_BY_CHANNELS_FORMAT, timeBoxType, nbChannels);

        //on ne fait le décompte que pour les timeboxtype dt et dod, plus pour dp, d, w et wod
        if(timeBoxType.equals("dt") || timeBoxType.equals("dod") ){
            context.getCounter(COUNTER_NB_BY_CHANNELS_GROUP, counternbChannels).increment(1);
        }
    }

    /** sending counters by nb CHANNELS as syslog message.
     *  The nb CHANNELS will be given as counter name to kibana
     */
    public static void sendKibanaCounterNbByChannels(Job job)
            throws IOException {

        String counterGroup = COUNTER_NB_BY_CHANNELS_GROUP;
        Iterator<Counter> iteCounters = job.getCounters()
                .getGroup(counterGroup).iterator();

        while (iteCounters.hasNext()) {
            Counter counter = iteCounters.next();
            UteCountersLogger logger = new UteCountersLogger();

            String timeboxTypeAndnbChannels = counter.getName().replace(counterGroup, "");
            String[] splittedTimeboxTypeAndnbChannels =
                    FieldsUtils.UNDERSCORE_PATTERN.split(timeboxTypeAndnbChannels,2);
            if (splittedTimeboxTypeAndnbChannels.length>1) {
                String timeboxType = splittedTimeboxTypeAndnbChannels[0];
                String nbChannels = splittedTimeboxTypeAndnbChannels[1];

                logger.addType(counterGroup);
                logger.addName(nbChannels);
                logger.addValueToJson("timeboxtype", timeboxType);
                logger.addCounter("value", counter.getValue());
                //a log for each counter. to make dashboards dynamically in kibana
                logger.sendJsonLog();
            }
        }
    }

    public static List<String> getGroups() {
        return Arrays.asList(KIBANA_GROUPS);
    }

}
