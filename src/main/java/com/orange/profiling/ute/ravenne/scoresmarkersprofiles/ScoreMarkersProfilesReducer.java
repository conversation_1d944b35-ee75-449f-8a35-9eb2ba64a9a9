package com.orange.profiling.ute.ravenne.scoresmarkersprofiles;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Reducer;

import java.io.IOException;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;


public class ScoreMarkersProfilesReducer extends Reducer<Text, Text, Text, Text> {
    private ObjectMapper jsonMapper = new ObjectMapper();

    @Override
    public void reduce(Text key, Iterable<Text> values, Context context)
            throws IOException, InterruptedException {

        Set<String> markersWithConcept = new HashSet<>();
        String targetConcept = key.toString();

        // Un seul passage sur les valeurs
        for (Text value : values) {
            String val = value.toString();

            if (val.startsWith("MARKER:")) {
                // Traitement d'un marker
                String[] parts = val.substring(7).split(":", 2);
                markersWithConcept.add(parts[0]);
            }
            else if (val.startsWith("PROFIL:") && !markersWithConcept.isEmpty()) {
                // Traitement d'un profil (seulement si on a déjà des markers)
                String[] parts = val.substring(7).split(":", 2);
                String aid = parts[0];
                String jsonData = parts[1];

                try {
                    JsonNode root = jsonMapper.readTree(jsonData);
                    JsonNode thRav = root.get("th_rav");

                    if (thRav != null && thRav.isArray()) {
                        double totalWeight = 0.0;
                        double conceptWeight = 0.0;

                        for (JsonNode concept : thRav) {
                            double weight = Double.parseDouble(concept.get("weight").asText());
                            totalWeight += weight;

                            if (concept.get("concept").asText().equals(targetConcept)) {
                                conceptWeight = weight;
                            }
                        }

                        // Émettre pour chaque marker si le concept est présent
                        if (conceptWeight > 0) {
                            for (String markerName : markersWithConcept) {
                                context.write(
                                        new Text(markerName + "\t" + aid),
                                        new Text(conceptWeight + "\t" + totalWeight)
                                );
                            }
                        }
                    }
                } catch (Exception e) {
                    context.getCounter("Error", "JsonProcessing").increment(1);
                }
            }
        }
    }
}


