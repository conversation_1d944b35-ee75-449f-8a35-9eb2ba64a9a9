package com.orange.profiling.ute.ravenne.profiltype.tastebox;

import java.io.IOException;

import org.apache.hadoop.fs.Path;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Job;
import org.apache.hadoop.mapreduce.lib.input.MultipleInputs;
import org.apache.hadoop.mapreduce.lib.input.TextInputFormat;
import org.apache.hadoop.mapreduce.lib.output.FileOutputFormat;
import org.apache.hadoop.mapreduce.lib.output.TextOutputFormat;
import org.apache.log4j.BasicConfigurator;
import org.apache.log4j.Logger;

import com.orange.profiling.common.exceptions.FailedJobException;
import com.orange.profiling.common.mapred.UteConfiguration;
import com.orange.profiling.common.mapred.UteCountersWriter;
import com.orange.profiling.common.utils.FsUtils;

/**
 * Main driver class for the Ravenne Tastebox Processing System.
 * This MapReduce job processes user viewing data to generate weighted concept profiles
 * based on viewing duration and concept hierarchies.
 * 
 * Job Configuration:
 * - Input: User viewing data with optin weights
 * - Processing: Aggregates and normalizes concept weights by family
 * - Output: User profiles with weighted concepts by timeslot
 * 
 * Required Arguments:
 * 1. inputOptinWeights: Path to input file containing user viewing data
 * 2. catalogConceptFamily: Path to concept family mapping configuration
 * 3. outputDir: Directory for job output
 * 
 * Key Features:
 * - Configurable number of reduce tasks (default: 100)
 * - Automatic output directory management
 * - Counter tracking for job statistics
 * - Support for multiple input formats
 * 
 * <AUTHOR>
 */
public class MainTastebox {
    private static final Logger LOGGER = Logger.getLogger(MainTastebox.class);

    private static final int ARG_LENGTH = 3;
    private static final int INPUT_OPTIN_WEIGHTS_IDX = 0;
    private static final int CATALOG_CONCEPT_FAMILY_IDX = 1;
    private static final int OUTPUT_DIR_IDX = 2;

    private static final String JOB_NAME = "ute:ravenne.tastebox";

    public static final String CONCEPT_FAMILY = "CONCEPT_FAMILY";

    /**
     * @param args
     *            args
     * @throws IOException
     *             IOException
     * @throws InterruptedException
     *             InterruptedException
     * @throws ClassNotFoundException
     *             ClassNotFoundException
     * @throws FailedJobException
     */
    public static void main(final String[] args) throws IOException, InterruptedException, ClassNotFoundException, FailedJobException {

        BasicConfigurator.configure();

        // get param
        if (args.length < ARG_LENGTH) {
            String mess = "Takes " + ARG_LENGTH + " arguments : inputOptinWeights catalogConceptFamily outputDir";
            LOGGER.error(mess);
            throw new IllegalArgumentException(mess);
        }

        Path inputOptinWeights = new Path(args[INPUT_OPTIN_WEIGHTS_IDX]);
        String catalogConceptFamily = args[CATALOG_CONCEPT_FAMILY_IDX];
        Path outputDir = new Path(args[OUTPUT_DIR_IDX]);

        // Create configuration
        UteConfiguration conf = new UteConfiguration();
        conf.set(CONCEPT_FAMILY, catalogConceptFamily);

        // Create job
        Job job = Job.getInstance(conf, JOB_NAME);
        job.setJarByClass(MainTastebox.class);
        job.setNumReduceTasks(100);

        // Setup MapReduce
        job.setMapperClass(ConceptsWeightsMapper.class);
        job.setReducerClass(TasteboxReducer.class);

        // Specify key / value
        job.setOutputKeyClass(Text.class);
        job.setOutputValueClass(Text.class);

        // Input
        MultipleInputs.addInputPath(job, inputOptinWeights, TextInputFormat.class, ConceptsWeightsMapper.class);

        // Output
        FileOutputFormat.setOutputPath(job, outputDir);
        job.setOutputFormatClass(TextOutputFormat.class);

        // Delete output if exists
        FsUtils fs = new FsUtils(outputDir,conf);
        if (fs.getFs().exists(outputDir)) {
            fs.getFs().delete(outputDir, true);
        }

        // Execute job
        boolean succeed = job.waitForCompletion(true);
        if (!succeed) {
            throw new FailedJobException(JOB_NAME);
        }

        // Statistics
        UteCountersWriter.writeAlternateCounters(job, fs.getFs(), outputDir);
    }

}