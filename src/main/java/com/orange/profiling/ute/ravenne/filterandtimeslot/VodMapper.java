package com.orange.profiling.ute.ravenne.filterandtimeslot;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import com.orange.profiling.common.file.generated.Progbymac;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Mapper;

import com.orange.profiling.common.file.generated.Vod;
import com.orange.profiling.common.optin.OptinValue;
import com.orange.profiling.common.utils.FieldsUtils;
import com.orange.profiling.ute.ravenne.dao.TimeboxZapAggregation;
import com.orange.profiling.ute.utils.SupportTransformer;

/**
 * Mapper implementation for processing Video-on-Demand (VOD) viewing data in the
 * FilterAndTimeslot component. This mapper processes VOD viewing records and
 * prepares them for aggregation by the reducer.
 *
 * <h2>Input Format:</h2>
 * The mapper expects VOD viewing records with the following information:
 * <ul>
 *   <li>Account ID (aid)</li>
 *   <li>Content information (title, provider, content ID)</li>
 *   <li>Viewing details (duration, timestamp)</li>
 *   <li>Program concepts (categories/tags)</li>
 * </ul>
 *
 * <h2>Processing Steps:</h2>
 * <ol>
 *   <li>Parses input VOD records</li>
 *   <li>Validates viewing duration (must be ≥ 80% of show duration)</li>
 *   <li>Normalizes provider information for specific channels</li>
 *   <li>Creates a unique program identifier by combining:
 *     <ul>
 *       <li>Show duration</li>
 *       <li>Provider ID</li>
 *       <li>Content ID</li>
 *       <li>Title</li>
 *       <li>Day placeholder (0)</li>
 *     </ul>
 *   </li>
 *   <li>Calculates time-based metrics:
 *     <ul>
 *       <li>Day of week (1-7)</li>
 *       <li>Period (1-6)</li>
 *       <li>Timeslot (1-10)</li>
 *       <li>Average viewing time</li>
 *     </ul>
 *   </li>
 * </ol>
 *
 * <h2>Output Format:</h2>
 * Key: Program identifier
 * Value: Tab-separated string containing:
 * <ul>
 *   <li>Account ID</li>
 *   <li>Program concepts</li>
 *   <li>Temporal information (day, period, timeslot)</li>
 *   <li>View type (VOD)</li>
 *   <li>Duration and timing details</li>
 *   <li>Content metadata (provider, ID, title)</li>
 * </ul>
 *
 * <h2>Special Cases:</h2>
 * <ul>
 *   <li>Handles specific channel mappings (M6, W9, D8)</li>
 *   <li>Filters out views that are too short</li>
 *   <li>Preserves too-short views for cross-week processing</li>
 * </ul>
 *
 * @see FilterAndTimeslotReducer For the aggregation of mapped records
 * @see MainFilterAndTimeslot For the overall job configuration
 */
public class VodMapper extends Mapper<Object, Text, Text, Text> {

    private static final String TAB = FieldsUtils.TAB;
    private static final String DAY_PLACE_HOLDER = "0";

    private static final String PROVIDER_TVODM6REP = "TVODM6REP";
    private static final String PROVIDER_TVOD1 = "TVOD1";
    // management specific channels with short name length different to three characters: map<channel, provider>
    private static final Map<String, String> specificChannels = new HashMap<>();
    static {
        specificChannels.put("M6", PROVIDER_TVODM6REP);
        specificChannels.put("W9", PROVIDER_TVODM6REP);
        specificChannels.put("D8", PROVIDER_TVOD1);
    }
    private final OptinValue optinValue = new OptinValue();

    private Text outputKey = new Text();
    private Text outputValue = new Text();
    private Vod vodLine = new Vod();

    /*
     * (non-Javadoc)
     *
     * @see org.apache.hadoop.mapreduce.Mapper#map(KEYIN, VALUEIN,
     * org.apache.hadoop.mapreduce.Mapper.Context)
     */
    @Override
    public final void map(final Object key, final Text value,
            final Context context) throws IOException, InterruptedException {

        // Example : 711407425 ********** 1188 ASHVSEV0106W0108642_H_SVODOCS_1
        // comédie$horreur null 12 06. The Killer of Killers - HD SVODOCS 1 6
        // Ash vs Evil Dead - S01 ASHVSEVIL01W0108636

        vodLine.setValue(value.toString());
        if (vodLine.checkFormat() && !vodLine.getField(Vod.OPUSCODE).isEmpty() && containsUsedData()) {
            optinValue.fromString(vodLine.getField(Vod.OPTIN));
            if (optinValue.isOptin(OptinValue.OPTIN_RECO)) {
                // tri sur émission regardée
                outputKey.set(buildProgramKey());
                outputValue.set(buildZapValue());
                context.write(outputKey, outputValue);
            } else {
                context.getCounter(Counters.VOD_OPTOUT_RECO).increment(1);
            }
        } else {
            context.getCounter(Counters.VOD_WITHOUT_DURATION).increment(1);
        }
    }


    /** Check that line contains useful data.
     * If we have no zapduration, this line is not useful.
     * @return
     */
    private boolean containsUsedData() {
        int zapDuration = 0;
        try {
            zapDuration = Integer.parseInt(vodLine.getField(Vod.ZAPDURATION));
        }
        catch (Exception e) {
            return false;
        }
        return zapDuration > 0;
    }

    /** Build complex key.
     * The key represent a program. The goal is to group all different zaps of one same program.
     * For OnDemand, we don't use dayOfProgram as same on demand program could be viewed on several days.
     *
    * @return aid TAB VOD TAB broadcastDuration TAB contentId TAB title
     */
    private String buildProgramKey() {
        String aid = vodLine.getField(Vod.AID);
        String broadcastDuration = vodLine.getField(Vod.SHOWDURATION);

        String serviceCodeShortNameChannel = serviceCodeAndShortNameChannel();
        String catchupChannelId = vodLine.getField(Vod.CATCHUPCHANNELID);
        String fullProvider = String.join(FieldsUtils.PIPE, serviceCodeShortNameChannel, catchupChannelId);

        String contentId = vodLine.getField(Vod.CONTENTID);
        String title = vodLine.getField(Vod.TITLE);


       /* String onDemandExternalId = vodLine.getField(Vod.ONDEMANDEXTERNALID);*/
        String externalEntertainmentId = vodLine.getField(Vod.EXTERNALENTERTAINMENTID);
        String seasonName = vodLine.getField(Vod.ENTERTAINMENTSEASONNAME);
        String seriesName = vodLine.getField(Vod.ENTERTAINMENTSERIESNAME);

        String offerName = vodLine.getField(Vod.ENTERTAINMENTOFFERNAME);


        return String.join(TAB, aid, TimeboxZapAggregation.VOD, broadcastDuration,
                fullProvider, contentId, title, externalEntertainmentId, seasonName,seriesName,offerName, DAY_PLACE_HOLDER);
    }

    /**
     * build provider with short channel name accordingly External Asset Id
     * @return concat(provider name - short channel name)
     */
    private String serviceCodeAndShortNameChannel() {
        // always exist "opusCode" check with
        String specificChannel = null;
        String serviceCode = vodLine.getField(Vod.SERVICECODE);
        String opusCode = vodLine.getField(Vod.OPUSCODE);
        specificChannel = opusCode.substring(0,3);

        for(Map.Entry<String, String> spChannel : specificChannels.entrySet()) {
            if (vodLine.getField(Vod.OPUSCODE).startsWith(spChannel.getKey())
                    && (serviceCode.equalsIgnoreCase(spChannel.getValue()))) {
                specificChannel = spChannel.getKey();
                break;
            }
        }
        return String.join(FieldsUtils.DASH, serviceCode, specificChannel);
    }

    /** Build value.
     *
     * @return beginZap TAB zapDuration TAB concepts TAB supportType
     */
    private String buildZapValue() {
        String beginZap = vodLine.getField(Vod.BEGINZAP);
        String zapDuration = vodLine.getField(Vod.ZAPDURATION);
        String concepts = vodLine.getField(Vod.CONCEPTSLIST);
        String supportType = vodLine.getField(Vod.STB_SUPPORT);
        String device = SupportTransformer.getDevice(supportType, vodLine.getField(Vod.AID));


        return String.join(TAB, beginZap, zapDuration, concepts, device);
    }

}
