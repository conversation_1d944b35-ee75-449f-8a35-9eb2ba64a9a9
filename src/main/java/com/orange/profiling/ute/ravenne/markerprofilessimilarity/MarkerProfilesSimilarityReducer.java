package com.orange.profiling.ute.ravenne.markerprofilessimilarity;

import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Reducer;
import java.io.IOException;

public class MarkerProfilesSimilarityReducer extends Reducer<Text, Text, Text, Text> {
    // Seuil de similarité (à ajuster selon vos besoins)
    private static final double SIMILARITY_THRESHOLD = 0.01;

    @Override
    public void reduce(Text key, Iterable<Text> values, Context context)
            throws IOException, InterruptedException {

        double sumSharedWeights = 0.0;  // Somme des poids des concepts partagés
        double totalWeight = 0.0;       // Poids total du profil

        // Agréger les poids pour cette paire marker/profil
        for (Text value : values) {
            String[] parts = value.toString().split("\t");
            sumSharedWeights += Double.parseDouble(parts[0]);  // conceptWeight
            totalWeight = Double.parseDouble(parts[1]);        // même totalWeight pour tous
        }

        // Calculer la similarité asymétrique
        double similarity = totalWeight > 0 ? sumSharedWeights / totalWeight : 0.0;

        // Appliquer le seuil pour obtenir un score binaire
        int binaryScore = (similarity > SIMILARITY_THRESHOLD) ? 1 : 0;

        //if (binaryScore > 0) {  // Ne sortir que les scores à 1
            String[] keyParts = key.toString().split("\t");
            context.write(
                    new Text(keyParts[1]),  // markerName
                    new Text(keyParts[0] + "\t" + binaryScore)  // aid_score
            );
        //}
    }
}
