package com.orange.profiling.ute.ravenne.profiltype.profils;

import java.io.BufferedReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Mapper;
import org.apache.log4j.Logger;
import org.json.simple.parser.ParseException;

import com.orange.profiling.common.utils.FieldsUtils;
import com.orange.profiling.common.utils.FsUtils;
import com.orange.profiling.ute.ravenne.file.Tastebox;
import com.orange.profiling.ute.ravenne.profiltype.profils.dao.BestSimilarityVector;
import com.orange.profiling.ute.ravenne.profiltype.profils.dao.CatalogueProfilVector;
import com.orange.profiling.ute.ravenne.profiltype.profils.dao.ConceptsVector;
import com.orange.profiling.ute.ravenne.profiltype.profils.dao.LigneThematique;
import com.orange.profiling.ute.ravenne.profiltype.profils.dao.ScoreLTConfReader;
import com.orange.profiling.ute.ravenne.util.ValuedKeyUtils;

/**
 * Mapper implementation for the Ravenne Profile Processing System that processes user viewing data
 * to determine profile types based on weighted concepts. This class implements two distinct profiling algorithms:
 * 
 * 1. Vector Type Algorithm (VT):
 *    - Uses cosine similarity to compare user consumption vectors with profile type vectors
 *    - Primary algorithm for profile matching
 *    - More precise matching based on weighted concept distributions
 * 
 * 2. Top Concepts Algorithm (TC):
 *    - Fallback algorithm when vector type matching fails
 *    - Matches based on presence of key thematic concepts
 *    - Allows for flexible matching with missing concepts under specific conditions
 * 
 * Input Format:
 * - Tastebox format: aid concepts_with_weights day_timeslot duration
 * - Example: "aid sujets/rugby=10,sujets/ovalie=10 d1t5 18"
 * 
 * Output Types:
 * 1. User Profile Assignments:
 *    - Key: aid (user identifier)
 *    - Value: algorithm TAB profilId TAB timeslot TAB duration
 * 
 * 2. Vector Statistics:
 *    - Key: VECTOR
 *    - Value: algorithm TAB vector_representation TAB profilId
 * 
 * Configuration Requirements:
 * - Catalog Profils: Profile definitions
 * - Catalog ProfilVectors: Vector representations for profiles
 * - Catalog CombiCoeur: Thematic line configurations
 * 
 * Key Features:
 * - Dual algorithm support for flexible profile matching
 * - Weighted concept processing
 * - Vector similarity calculations
 * - Thematic line analysis
 * - Detailed statistics tracking
 * 
 * <AUTHOR>
 */
public class TasteboxMapper extends Mapper<Object, Text, Text, Text> {

    private static final Logger LOGGER = Logger.getLogger(TasteboxMapper.class);

    private static final String TAB = FieldsUtils.TAB;

    public static final String ALGO_VECTORTYPE = "VT";
    public static final String ALGO_TOPCONCEPTS = "TC";

    public static final int OUT_ALGO_IDX = 0;
    public static final int OUT_PROFIL_IDX = 1;
    public static final int OUT_TIMESLOT_IDX = 2;
    public static final int OUT_DURATION_IDX = 3;

    public static final String VECTOR_OUT = "VECTOR";
    public static final int VECTOR_OUT_ALGO_IDX = 0;
    public static final int VECTOR_OUT_VECTOR_IDX = 1;
    public static final int VECTOR_OUT_PROFIL_IDX = 2;

    private CatalogueProfilVector catalogueProfil = new CatalogueProfilVector();
    private List<LigneThematique> lignesThematiques = new ArrayList<>();

    private Text vectorOutKey = new Text(VECTOR_OUT);
    private Text outputKey = new Text();
    private Text outputValue = new Text();
    private Tastebox tasteboxLine = new Tastebox();

    /**
     * Initializes the mapper by loading necessary configuration data:
     * 1. Profile catalog containing profile definitions and vectors
     * 2. CombiCoeur configuration defining thematic lines
     * 
     * @param context The Hadoop mapper context containing configuration
     * @throws IOException If there are issues reading configuration files
     * @throws InterruptedException If the setup process is interrupted
     */
    @Override
    protected void setup(final Mapper<Object, Text, Text, Text>.Context context)
            throws IOException, InterruptedException {

        FsUtils fsUtils =  new FsUtils(context.getConfiguration());
        readCatalogProfil(context, fsUtils);
        readCombiCoeur(context, fsUtils);
    }

    /**
     * Reads the profile catalog and vector representations from configuration files.
     * 
     * @param context Mapper context for configuration access
     * @param fsUtils Utility for file system operations
     * @throws IOException If there are issues reading configuration files
     */
    private void readCatalogProfil(final Mapper<Object, Text, Text, Text>.Context context,
            FsUtils fsUtils) throws IOException {

        String inputPathCatProfil = context.getConfiguration().get(MainProfils.CATALOG_PROFILS);
        String inputPathCatProfilVector = context.getConfiguration().get(MainProfils.CATALOG_PROFILVECTORS);
        catalogueProfil = MainProfils.loadCatalogProfilsAndVectors(fsUtils,
                inputPathCatProfil, inputPathCatProfilVector);
    }

    /**
     * Reads the CombiCoeur configuration defining thematic lines.
     * 
     * @param context Mapper context for configuration access
     * @param fsUtils Utility for file system operations
     * @throws IOException If there are issues reading configuration files
     * @throws InterruptedException If the setup process is interrupted
     */
    private void readCombiCoeur(Mapper<Object, Text, Text, Text>.Context context,
            FsUtils fsUtils) throws IOException, InterruptedException {

        String inputPathCombiCoeur = context.getConfiguration().get(MainProfils.CATALOG_COMBICOEUR);
        try (BufferedReader br = fsUtils.getReaderForFile(inputPathCombiCoeur)) {
            lignesThematiques = ScoreLTConfReader.readScoreLTConfigFile(br);
        }
        catch (ParseException e) {
            throw new InterruptedException("Could not read combi coeur : "+inputPathCombiCoeur);
        }
    }

    /**
     * Processes each input record to determine user profiles based on viewing behavior.
     * The processing flow:
     * 1. Validates input format
     * 2. Extracts user ID and weighted concepts
     * 3. Attempts profile matching using Vector Type algorithm
     * 4. Falls back to Top Concepts algorithm if needed
     * 5. Outputs profile assignments and vector statistics
     * 
     * @param key Input key (unused)
     * @param value Input text in Tastebox format
     * @param context Mapper context for output and counters
     * @throws IOException If there are issues writing output
     * @throws InterruptedException If the mapping process is interrupted
     */
    @Override
    public final void map(final Object key, final Text value,
            final Context context) throws IOException, InterruptedException {

        tasteboxLine.setValue(value.toString());

        // Example : aid sujets/rugby=10, sujets/ovalie=10 d1t5 18
        if (tasteboxLine.checkFormat()) {

            String aid = tasteboxLine.getField(Tastebox.AID);
            outputKey.set(aid);

            String weightedConceptsString = tasteboxLine.getField(Tastebox.PONDERATED_CONCEPTS);

            Map<String, Double> weightedConcepts =
                    ValuedKeyUtils.parseWeightedConceptsToMapDouble(weightedConceptsString);

            boolean foundProfils = searchProfils(context, weightedConcepts);

            if (!foundProfils) {
                context.getCounter(Counters.NO_PROFIL_FOUND).increment(1);
                LOGGER.debug("No profil found for : [" + weightedConcepts + "]");
            }

        }
        else {
            context.getCounter(Counters.BAD_AID_CSV_LENGTH).increment(1);
            LOGGER.error("Bad aid csv length : [" + value.toString() + "]");
        }

    }

    /**
     * Searches for matching profile types using both available algorithms.
     * Processing order:
     * 1. Try Vector Type algorithm first (more precise)
     * 2. Fall back to Top Concepts algorithm if no matches found
     * 
     * @param context Mapper context for output
     * @param weightedConcepts Map of concepts to their weights from user viewing data
     * @return true if any profiles were found by either algorithm
     * @throws IOException If there are issues writing output
     * @throws InterruptedException If the search process is interrupted
     */
    private boolean searchProfils(final Context context, Map<String, Double> weightedConcepts)
            throws IOException, InterruptedException {

        return searchProfilWithVectorTypeAlgo(context, weightedConcepts)
                || searchProfilWithTopConceptAlgo(context, weightedConcepts);
    }

    /**
     * Implements the Vector Type algorithm using cosine similarity.
     * This algorithm:
     * 1. Compares user consumption vector with profile type vectors
     * 2. Uses cosine similarity for matching
     * 3. Tracks matches through counters
     * 
     * @param context Mapper context for output
     * @param weightedConcepts User's weighted concept vector
     * @return true if any profiles were matched
     * @throws IOException If there are issues writing output
     * @throws InterruptedException If the algorithm is interrupted
     */
    private boolean searchProfilWithVectorTypeAlgo(final Context context,
            Map<String, Double> weightedConcepts)
                    throws IOException, InterruptedException {

        // Comparaison du vecteur de consommation client avec les vecteurs types associes aux profils
        Map<String, BestSimilarityVector> listProfils = catalogueProfil.searchForProfil(weightedConcepts);
        if (listProfils.size() > 0) {
            writeOutListProfilsForAlgo(context, listProfils, ALGO_VECTORTYPE);
            context.getCounter(Counters.NB_PROFIL_ALGO_VECTORTYPE).increment(listProfils.size());
            return true;
        }
        return false;
    }

    /**
     * Implements the Top Concepts algorithm as a fallback matching method.
     * This algorithm:
     * 1. Compares user concepts with thematic line top concepts
     * 2. Allows flexible matching with missing concepts
     * 3. Generates profile matches based on concept presence
     * 
     * @param context Mapper context for output
     * @param weightedConcepts User's weighted concepts
     * @return true if any profiles were matched
     * @throws IOException If there are issues writing output
     * @throws InterruptedException If the algorithm is interrupted
     */
    private boolean searchProfilWithTopConceptAlgo(final Context context,
            Map<String, Double> weightedConcepts)
                    throws IOException, InterruptedException {

        // Comparaison du vecteur de consommation client avec les top concepts associés aux profils types
        Map<String, BestSimilarityVector> listProfils = findProfilWithTopConcepts(weightedConcepts);
        if (listProfils.size() > 0) {
            writeOutListProfilsForAlgo(context, listProfils, ALGO_TOPCONCEPTS);
            context.getCounter(Counters.NB_PROFIL_ALGO_TOPCONCEPTS).increment(listProfils.size());
            return true;
        }
        return false;
    }

    /**
     * Compares top concepts for each ligne thematique to weighted concepts.
     * 
     * @param weightedConcepts Map of concepts to their weights from user viewing data
     * @return Map of profile IDs to their matching vectors
     */
    private Map<String, BestSimilarityVector> findProfilWithTopConcepts(Map<String, Double> weightedConcepts) {
        Map<String, BestSimilarityVector> listProfils = new HashMap<>();

        Set<String> vectorConcepts = weightedConcepts.keySet();
        for (LigneThematique lt: lignesThematiques) {
            if (checkTopConcepts(vectorConcepts, lt)) {
                BestSimilarityVector bsv = buildSimilarityVectorFromConceptList(lt.getTopConcepts());
                listProfils.put(Integer.toString(lt.getIndex()), bsv);
            }
        }
        return listProfils;
    }

    /**
     * Validates if a user's vector contains the required top concepts for a thematic line.
     * Matching Rules:
     * 1. First concept must always be present
     * 2. For lines with >2 concepts, one non-first concept can be missing
     * 3. All concepts must be present for lines with ≤2 concepts
     * 
     * @param vectorConcepts Set of concepts from user vector
     * @param lt Thematic line configuration to check against
     * @return true if the vector satisfies the matching rules
     */
    private static boolean checkTopConcepts(Set<String> vectorConcepts, LigneThematique lt) {
        if (lt != null) {
            int nbMissing = 0;
            int nbTopConcepts=lt.getTopConcepts().size();
            boolean first = true;
            for(String topconcept : lt.getTopConcepts()) {
                if (!vectorConcepts.contains(topconcept)) {
                    if (first) {
                        return false;
                    }
                    nbMissing++;
                }
                first = false;
            }
            // No missing top concepts
            // or only one (and not the first) if lt has more than 2 topConcepts
            return (nbMissing == 0) || ( (nbMissing == 1) && (nbTopConcepts > 2) );
        }
        return false;
    }

    /**
     * Creates a BestSimilarityVector from a set of concepts.
     * This utility method:
     * 1. Converts concept set to weighted format
     * 2. Creates vector representation
     * 3. Assigns maximum similarity score
     * 
     * @param concepts Set of concepts to convert
     * @return BestSimilarityVector with concepts and perfect similarity score
     */
    private static BestSimilarityVector buildSimilarityVectorFromConceptList(Set<String> concepts) {
        Map<String,Double> weightedConcepts =  concepts.stream().sorted().
                collect(Collectors.toMap(c -> c, c -> 0.0, (c1 , c2) -> 0.0, LinkedHashMap::new));
        ConceptsVector cv = new ConceptsVector(weightedConcepts);
        return new BestSimilarityVector(cv, 1.0);
    }

    /**
     * Outputs profile matches for a specific algorithm.
     * Generates two types of output:
     * 1. User profile assignments with timeslot and duration
     * 2. Vector statistics for analysis
     * 
     * Output Format:
     * 1. User profiles: aid -> algorithm TAB profilId TAB timeslot TAB duration
     * 2. Vector stats: VECTOR -> algorithm TAB vector TAB profilId
     * 
     * @param context Mapper context for output
     * @param listProfils Map of profile IDs to their matching vectors
     * @param algo Algorithm identifier (VT or TC)
     * @throws IOException If there are issues writing output
     * @throws InterruptedException If the output process is interrupted
     */
    private void writeOutListProfilsForAlgo(final Context context,
            Map<String, BestSimilarityVector> listProfils, String algo)
                    throws IOException, InterruptedException {

        String timeslot = tasteboxLine.getField(Tastebox.DAY_TIMESLOT);
        String totalDuration = tasteboxLine.getField(Tastebox.TOTAL_DURATION);

        for (Map.Entry<String, BestSimilarityVector> va : listProfils.entrySet()) {
            String profilId = va.getKey();
            BestSimilarityVector bestSimilarityVector = va.getValue();
            ConceptsVector bestVector = bestSimilarityVector.getVector();

            outputValue.set(String.join(TAB, algo, profilId, timeslot, totalDuration));
            context.write(outputKey, outputValue);

            outputValue.set(String.join(TAB, algo, bestVector.toString(), profilId));
            context.write(vectorOutKey, outputValue);

        }
        context.getCounter(Counters.NB_PROFIL).increment(listProfils.size());
    }

}
