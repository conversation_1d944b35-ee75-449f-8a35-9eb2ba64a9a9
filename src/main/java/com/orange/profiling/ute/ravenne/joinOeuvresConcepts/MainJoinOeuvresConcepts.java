package com.orange.profiling.ute.ravenne.joinOeuvresConcepts;

import com.orange.profiling.common.exceptions.FailedJobException;
import com.orange.profiling.common.mapred.UteConfiguration;
import com.orange.profiling.common.utils.FsUtils;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Job;
import org.apache.hadoop.mapreduce.lib.input.MultipleInputs;
import org.apache.hadoop.mapreduce.lib.input.TextInputFormat;
import org.apache.hadoop.mapreduce.lib.output.FileOutputFormat;

import java.io.IOException;

public class MainJoinOeuvresConcepts {

    private static final String JOB_JOIN_OEUVRES_CONCEPTS = "ute:join-oeuvres-concepts";
    private static final int EXPECTED_ARGS = 3;
    private static final String USAGE = "Takes 3 arguments: cataloguePath marqueursPath outputDir";

    public static void main(String[] args)
            throws ClassNotFoundException, FailedJobException, InterruptedException, IOException {

        // Validation des arguments
        if (args.length != EXPECTED_ARGS) {
            throw new IllegalArgumentException(USAGE);
        }

        // Chemins en tant que constantes
        final Path cataloguePath = new Path(args[0]);
        final Path marqueursPath = new Path(args[1]);
        final Path outputDir = new Path(args[2]);

        // Création et configuration du job
        Job job = configureJob(new UteConfiguration(true),
                cataloguePath,
                marqueursPath,
                outputDir);

        // Exécution du job
        if (!job.waitForCompletion(true)) {
            throw new FailedJobException(JOB_JOIN_OEUVRES_CONCEPTS);
        }
    }

    private static Job configureJob(UteConfiguration conf,
                                    Path cataloguePath,
                                    Path marqueursPath,
                                    Path outputDir)
            throws IOException {

        // Nettoyage du répertoire de sortie
        cleanOutputDir(conf, outputDir);

        // Configuration du job
        Job job = Job.getInstance(conf, JOB_JOIN_OEUVRES_CONCEPTS);
        job.setJarByClass(MainJoinOeuvresConcepts.class);

        // Configuration des types de sortie
        job.setMapOutputKeyClass(Text.class);
        job.setMapOutputValueClass(Text.class);
        job.setOutputKeyClass(Text.class);
        job.setOutputValueClass(Text.class);

        // Configuration du reducer
        job.setReducerClass(JoinOeuvresConceptsReducer.class);
        job.setNumReduceTasks(15);

        // Configuration des entrées multiples
        MultipleInputs.addInputPath(job, cataloguePath,
                TextInputFormat.class,
                CatalogueOeuvresMapper.class);
        MultipleInputs.addInputPath(job, marqueursPath,
                TextInputFormat.class,
                MarkerOeuvresMapper.class);

        // Configuration de la sortie
        FileOutputFormat.setOutputPath(job, outputDir);

        return job;
    }

    private static void cleanOutputDir(UteConfiguration conf, Path outputDir)
            throws IOException {
        FsUtils fsUtils = new FsUtils(outputDir, conf);
        if (fsUtils.getFs().exists(outputDir)) {
            fsUtils.getFs().delete(outputDir, true);
        }
    }
}
