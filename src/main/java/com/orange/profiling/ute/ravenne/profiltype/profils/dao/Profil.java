package com.orange.profiling.ute.ravenne.profiltype.profils.dao;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;

/**
 * <AUTHOR>
 *
 */
public class Profil implements Serializable {

    private static final Logger LOGGER = Logger.getLogger(Profil.class);
    /**
     *
     */
    private static final long serialVersionUID = -825575713176463389L;

    private String profilId;
    private String label;
    private String varname;
    private Double threshold;

    private String verticalName;

    private List<ConceptsVector> vectorList;

    public Profil(String profil) {
        super();
        this.profilId = profil;
        vectorList = new ArrayList<>();
    }

    public Profil() {
        super();
    }

    public Profil(String profilId, String label, String varname) {
        this.profilId = profilId;
        this.label = label;
        this.varname = varname;
        this.verticalName = varname.replaceFirst("rav_", "");
    }

    public List<ConceptsVector> getVectors() {
        return vectorList;
    }

    public void addVector(ConceptsVector patterns) {
        vectorList.add(patterns);
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getProfilId() {
        return profilId;
    }

    public String getVerticalName() { return verticalName; }

    public void setProfilId(String profilId) {
        this.profilId = profilId;
    }

    public Double getThreshold() {
        return threshold;
    }

    public void setThreshold(Double threshold) {
        this.threshold = threshold;
    }

    public String getVarname() {
        return varname;
    }

    public void setVarname(String varname) {
        this.varname = varname;
    }

    @Override
    public final boolean equals(final Object object) {
        boolean sameSame = false;

        if (object instanceof Profil) {
            sameSame = this.profilId.equals(((Profil) object).profilId);
        }
        return sameSame;
    }

    /**
     * @return int
     * @see org.joda.time.base.AbstractInstant#hashCode()
     */
    @Override
    public final int hashCode() {
        return profilId.hashCode();
    }

    @Override
    public String toString() {
        StringBuilder toStringBld = new StringBuilder("ConceptsVector : ");
        toStringBld.append("[profilId=" + profilId + "]");
        toStringBld.append("[patterns=" + vectorList.toString() + "]");

        return toStringBld.toString();
    }

    public String labelAndId() {
        return label+" ("+profilId+")";
    }

    public final BestSimilarityVector computeBestSimilarity(Map<String, Double> weightedConcepts) {

        Double bestSimilarity = 0D;
        ConceptsVector bestVector = null;

        for (ConceptsVector pattern : vectorList) {
            Double similarity = pattern.computeSimilarity(weightedConcepts);
            if (similarity > bestSimilarity) {
                bestSimilarity = similarity;
                bestVector = pattern;
            }
        }

        if ((bestSimilarity >= this.threshold)&&(bestVector != null)) {
            LOGGER.debug("Profil " + profilId + "/" + label
                    + " : best similarity " + bestSimilarity + " for "
                    + weightedConcepts.toString() + " is " + bestVector.toString());
            return new BestSimilarityVector(bestVector, bestSimilarity);
        }
        return new BestSimilarityVector(null, 0D);
    }

}
