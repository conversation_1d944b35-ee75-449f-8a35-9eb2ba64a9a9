package com.orange.profiling.ute.ravenne.profiltype.profils.dao;

import java.io.BufferedReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.json.simple.JSONArray;
import org.json.simple.JSONObject;
import org.json.simple.parser.JSONParser;
import org.json.simple.parser.ParseException;

/** Read scoreLTconf.json to return LigneThematique objects.
 * This contains configuration for scoring of LigneTematique.
 * This correspond to configuration used by Meta4U to select content to recommand
 * in ligne thematique strips
 */
public final class ScoreLTConfReader {

    private ScoreLTConfReader() {}

    public static List<LigneThematique> readScoreLTConfigFile(BufferedReader br) throws IOException, ParseException {

        List<LigneThematique> ligneThematiqueList = new ArrayList<>();

        JSONParser parser = new JSONParser();


        JSONObject allLignesThematiques = (JSONObject) parser.parse(br);

        for (Object indexStr : allLignesThematiques.keySet()) {
            int index = Integer.parseInt((String) indexStr);
            JSONObject ligneThematiqueJson = checkAndGetAsJsonObject(allLignesThematiques, (String) indexStr);
            String name = checkAndGetAsString(ligneThematiqueJson, "name");
            LigneThematique ligneThematique = new LigneThematique(index, name);

            Map<String, Double> conceptsWeighted = new HashMap<>();
            JSONObject pattern = checkAndGetAsJsonObject(ligneThematiqueJson, "score");
            if (pattern != null) {
                for (Object concept : pattern.keySet()) {
                    String value = checkAndGetAsString(pattern, (String) concept);
                    conceptsWeighted.put((String) concept, Double.valueOf(value));
                }
            }
            ConceptsVector vector = new ConceptsVector(conceptsWeighted);
            ligneThematique.setScoreVector(vector);

            JSONArray includes = checkAndGetAsJsonArray(ligneThematiqueJson, "includes");
            for (Object includeIndex : includes) {
                int otherLTindex = Integer.parseInt(includeIndex.toString());
                ligneThematique.addInclude(otherLTindex);
            }

            ligneThematiqueList.add(ligneThematique);
        }

        return ligneThematiqueList;
    }


    private static boolean checkContainsAttr(JSONObject confObj, String attrName) {
        return confObj.containsKey(attrName);
    }

    private static String checkAndGetAsString(JSONObject confObj, String attrName)  {
        if (checkContainsAttr(confObj, attrName)) {
            return (String) confObj.get(attrName);

        }
        return "";
    }

    private static JSONObject checkAndGetAsJsonObject(JSONObject confObj, String attrName)  {
        if (checkContainsAttr(confObj, attrName)) {
            return (JSONObject) confObj.get(attrName);
        }
        return null;
    }

    private static JSONArray checkAndGetAsJsonArray(JSONObject confObj, String attrName)  {
        if (checkContainsAttr(confObj, attrName)) {
            return (JSONArray) confObj.get(attrName);
        }
        return null;
    }
}
