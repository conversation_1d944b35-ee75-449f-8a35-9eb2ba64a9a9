package com.orange.profiling.ute.utils;

public class SupportTransformer {

    /** Configuration key for the domain */
    public static final String DEVICE_STB = "stb";
    public static final String DEVICE_MOBILE = "mobile";
    public static final String DEVICE_WEB = "web";
    public static final String DEVICE_SMARTTV = "smarttv";

    /** Configuration key for the support type */

    public static final String DEVICE_OTT_MOBILE = "OTT_mobile";
    public static final String DEVICE_OTT_OTVCAST = "otvcast";
    public static final String DEVICE_OTT_SMARTTV = "OTT_smarttv";
    public static final String DEVICE_OTT_APPLETV = "OTT_appletv";
    public static final String DEVICE_OTT_ANDROIDTV = "OTT_androidtv";
    public static final String DEVICE_OTT_PC = "OTT_pc";
    public static final String DEVICE_OTT_MLV = "mlv";
    public static final String SUPPORT_STB_IPTV = "IPTV";
    public static final String SUPPORT_STB_WHD = "WHD";
    public static final String SUPPORT_STB_IHD = "IHD";
    public static final String AID_OTT = "ott";

    public static String getDevice (String support, String aid) {

        if (aid.startsWith(AID_OTT)) {

            if (support.contains(DEVICE_OTT_MOBILE) || support.contains(DEVICE_OTT_OTVCAST)) {
                return DEVICE_MOBILE;

            } else if (support.contains(DEVICE_OTT_SMARTTV) || support.contains(DEVICE_OTT_APPLETV) || support.contains(DEVICE_OTT_ANDROIDTV)) {
                return DEVICE_SMARTTV;

            } else if (support.contains(DEVICE_OTT_PC) || support.contains(DEVICE_OTT_MLV)) {
                return DEVICE_WEB;

            }
        }
        // pour STB
        else {
            return DEVICE_STB;
        }
        // Retourne la valeur originale si aucune condition n'est remplie
        return support;
    }
}
