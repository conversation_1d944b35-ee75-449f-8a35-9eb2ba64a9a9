package com.orange.profiling.ute.utils;

import com.orange.profiling.common.utils.FieldsUtils;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * Parser pour les identifiants composites de la forme AIDHash-<domain>
 * 
 * Gère les formats :
 * - AIDHash-STB (Set-Top Box)
 * - AIDHash-Web 
 * - AIDHash-Mobile
 * - AIDHash-SmartTV
 */
public class AidParser {
    
    private final String originalAid;
    private final String baseAid;
    private final String domain;
    private final boolean validFormat;
    
    public AidParser(String aid) {
        this.originalAid = aid;
        
        if (aid != null && aid.contains(FieldsUtils.DASH)) {
            String[] parts = aid.split(FieldsUtils.DASH, 2);
            this.baseAid = parts[0];
            this.domain = parts[1];
            this.validFormat = isValidDomain(domain);
        } else {
            this.baseAid = aid;
            this.domain = null;
            this.validFormat = false;
        }
    }
    
    public String getBaseAid() {
        return baseAid;
    }
    
    public String getDomain() {
        return domain;
    }
    
    public String getOriginalAid() {
        return originalAid;
    }
    
    public boolean isValidFormat() {
        return validFormat;
    }
    
    public boolean isStb() {
        return SupportTransformer.DEVICE_STB.equals(domain);
    }
    
    public boolean isOtt() {
        return domain != null && !isStb();
    }

    private static final Set<String> VALID_DOMAINS = new HashSet<>(Arrays.asList(
            SupportTransformer.DEVICE_STB,
            SupportTransformer.DEVICE_WEB,
            SupportTransformer.DEVICE_MOBILE,
            SupportTransformer.DEVICE_SMARTTV
    ));

    private boolean isValidDomain(String domain) {
        return VALID_DOMAINS.contains(domain);
    }
}