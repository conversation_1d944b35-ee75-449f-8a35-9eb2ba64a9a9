package com.orange.profiling.ute.utils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.io.IOException;
import java.util.Properties;
import org.apache.hadoop.fs.FSDataInputStream;
import org.apache.hadoop.fs.FileSystem;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.fs.FSDataOutputStream;

import com.orange.profiling.common.utils.HadoopGCSFileSystemFactory;

public class PropertiesWriter {
    public static HadoopGCSFileSystemFactory hdfsFactory = new HadoopGCSFileSystemFactory();

    public static void writePropertiesToFs(Properties props, Path path) throws IOException {
        FileSystem fs = hdfsFactory.get(path);
        FSDataOutputStream outputStream = fs.create(path);
        props.store(outputStream, path.toString());
        outputStream.close();
    }

    public static void writePropertiesToFile(Properties props, File file) throws IOException {
        OutputStream os = new FileOutputStream(file);
        props.store(os, "");
        os.close();
    }

}
