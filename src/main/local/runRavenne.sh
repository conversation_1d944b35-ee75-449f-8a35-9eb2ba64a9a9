#!/bin/bash
# Run ravenne workflow with follonwing parameters

# ARGS
if [ $# -ne 1 ];
then
  echo "Please tell the processDate"
  echo $0" YYYYMMDD"
  exit 1;
fi

PRODDIR="apps/workflowUteRavenne"
PREPRODDIR="apps/preprod/workflowUteRavenne"
RECDIR="gitlab/apps/workflowUteRavenne"

HDFS_ROOT="hdfs://bigdata-prod/"
HDFS_ROOT_REC="hdfs://bigdata-current/"

OOZIE_MAPRED="http://oozie.prod.mapreduce.m0.p.fti.net:11000/oozie"
OOZIE_MAPRED_REC="http://oozie.current.rec.mapreduce.m0.p.fti.net:11000/oozie"

UTE_OR_NONREG="ute"

dirname=`dirname $0`
dirname=`cd $dirname; pwd;`
isprod=`echo $dirname | grep $PRODDIR`
ispreprod=`echo $dirname | grep $PREPRODDIR`
isrec=`echo $dirname | grep $RECDIR`
env="NONE"
if [ ! -z "$ispreprod" ];
then
  env="PREPROD"
  JOB_USER="profilingutepreprod"
elif [ ! -z "$isrec" ];
then
  env="REC"
  JOB_USER="profiling-ute"
  HDFS_ROOT=$HDFS_ROOT_REC
  OOZIE_MAPRED=$OOZIE_MAPRED_REC
  UTE_OR_NONREG="nonreg"
elif [ ! -z "$isprod" ];
then
  env="PROD"
  JOB_USER="profiling-ute"
else
  echo "The script should either be under $PRODDIR or $PREPRODDIR or $RECDIR"
  exit 2;
fi

cd $dirname
pwd

processDate=$1

# PARAM to set
# CONF is taken from prod (profiling-ute) or from preprod (profilingutepreprod)
CONFFROM=$JOB_USER

# Directory to adapt
# List of accepted/rejected concepts
acceptedRejectedConcepts=$HDFS_ROOT"/user/"$CONFFROM"/private/generated/"$UTE_OR_NONREG"/catalogue_concepts"
# Family of concepts
catalogConceptFamily=$HDFS_ROOT"/user/"$CONFFROM"/private/generated/"$UTE_OR_NONREG"/ravenne/catalog/conceptfamily.csv"
# List of profil types
catalogProfils=$HDFS_ROOT"/user/"$CONFFROM"/private/generated/"$UTE_OR_NONREG"/ravenne/catalog/catalog_profils.csv"
# List of vector type for profil types
catalogProfilsVectors=$HDFS_ROOT"/user/"$CONFFROM"/private/generated/"$UTE_OR_NONREG"/ravenne/catalog/catalog_profils_vector.json"
# Combinaisons Coeurs
catalogCombiCoeurs=$HDFS_ROOT"/user/"$CONFFROM"/private/generated/"$UTE_OR_NONREG"/ravenne/catalog/scoreLignesThematiques.json"

# filterLimit
filterLimit=2

echo "Tests sur environnement $env : "
echo "  - USER = $JOB_USER"
echo "  - HDFS = $HDFS_ROOT"
echo "  - OOZIE = $OOZIE_MAPRED"
echo "  - UTE_OR_NONREG = $UTE_OR_NONREG"
echo "  - CONFFROM = $CONFFROM"
echo "  - filterLimit= $filterLimit"

#Launch script
echo "run "$processDate" as "$JOB_USER
kinit -kt /etc/security/keytabs/${JOB_USER}.keytab ${JOB_USER}
cmd="oozie job -oozie "$OOZIE_MAPRED" -config job.properties -D processDate=${processDate} \
      -D acceptedRejectedConcepts=$acceptedRejectedConcepts \
      -D catalogConceptFamily=$catalogConceptFamily \
      -D catalogProfils=$catalogProfils \
      -D catalogProfilsVectors=$catalogProfilsVectors \
      -D catalogCombiCoeurs=$catalogCombiCoeurs \
      -D filterLimit=$filterLimit \
      -run"
echo $cmd

id=$($cmd)
oozieId=$(echo $id | cut -d' ' -f2)
echo "  for "$processDate" : oozieId "$oozieId 
status="RUNNING"
while [ ${status} == "RUNNING" ]
do
    sleep 60s
    status=$(oozie job -oozie "$OOZIE_MAPRED" -info ${oozieId} | grep 'Status        :' | cut -d':' -f2 | sed 's/ //g')
    echo "    for "$processDate" : oozieId "$oozieId" = "$status
done
