import json
from pprint import pprint

with open('/home/<USER>/Dev/profiling/ptv_gitFolder/ute-ravenne-profils/src/test/resources/in/catalogue/catalog_profils_vecteur.json') as data_file:
    data = json.load(data_file)

# pprint(data)

def weightedSumm(list):
    wSumm = {};
    for timeBox  in list:
        pprint(timeBox)
        for concept, value in timeBox.iteritems():
            wSumm[concept] = wSumm[concept] + value if concept in wSumm else value
    return wSumm

ndata = dict(map(lambda (k,v): (k, weightedSumm(v)), data.iteritems()))

# pprint(ndata)
with open('/home/<USER>/Dev/profiling/ptv_gitFolder/ute-ravenne-profils/src/test/resources/in/catalogue/catalog_profils_vecteur2.json', 'w') as fp:
    json.dump(ndata, fp, indent=4)