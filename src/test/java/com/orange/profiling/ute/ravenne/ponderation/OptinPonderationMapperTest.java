package com.orange.profiling.ute.ravenne.ponderation;


import static com.orange.profiling.common.optin.OptinMapper.OPTIN_TAG;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.PrintStream;

import org.apache.hadoop.io.Text;
import org.apache.hadoop.mrunit.mapreduce.MapDriver;
import org.apache.log4j.Level;
import org.apache.log4j.Logger;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

import com.orange.profiling.common.file.generated.Optin;
import com.orange.profiling.common.utils.FieldsUtils;

/**
 * Liste des tests
 * <p>
 * Si Optin value > 0
 * | Key Type    | Gdpr Flag    | Expected Key   | Value with/without Hash |
 * +-------------+--------------+----------------+-------------------------+
 * | AID         | 0            | AID            | without Hash            |
 * | MAC         | 0            | MAC            | without Hash            |
 * | AID_HASH    | 0            | AID            | without Hash            |
 * | MAC_HASH    | 0            | AID            | without Hash            |
 * | AID         | 1            | AID            | with Hash               |
 * | MAC         | 1            | MAC            | with Hash               |
 * | AID_HASH    | 1            | AID            | with Hash               |
 * | MAC_HASH    | 1            | AID            | with Hash               |
 * | AID_HASH    | Nan (A) => 0 | AID            | without Hash            |
 * | AID_HASH    | Bad Flag => 0| AID            | without Hash            |
 * | AID_HASH    | No File => 0 | AID            | without Hash            |
 * <p>
 * Si Optin value = 0, pas de sortie
 * Si Optin value not a number => 0 => pas de sortie
 *
 * <AUTHOR>
 */

public class OptinPonderationMapperTest {

    MapDriver<Object, Text, Text, Text> mapDriver;
    private final ByteArrayOutputStream errContent = new ByteArrayOutputStream();
    private static final String TAB = "\t";

    private static final String AID = "123456789";
    private static final String MAC = "AA:BB:CC:DD:EE:FF";
    private static final String STB_TYPE = "UHD2020";
    private static final String AID_HASH = "ahabcdef0123456789";
    private static final String MAC_HASH = "mh0123456789abcdef";
    private static final String OPTIN_TV_VAL = "1";
    private static final String OPTIN_PUB_VAL = "0";
    private static final String PUB_ACT_DATE = "20200701";
    private static final String PUB_ORIGIN = "Camela";
    private static final String OPTIN_STAT = "0";
    private static final String OPTIN_MEDIAMETRIE = "0";
    private static final String LAST_OPTIN_MEDIAMETRIE = "1634940735";

    @Before
    public void setUp() {
        OptinPonderationMapper optinMapper = new OptinPonderationMapper();
        mapDriver = MapDriver.newMapDriver(optinMapper);
        mapDriver.getConfiguration().set(OptinPonderationMapper.FLAG_VALUE, "0");

        Logger.getRootLogger().setLevel(Level.ERROR);
    }

    @Before
    public void setUpStreams() {
        System.setErr(new PrintStream(this.errContent));
    }

    @After
    public final void cleanUpStreams() {
        System.setErr(null);
    }

    @Test
    public void testShortLine() throws IOException {
        mapDriver.getConfiguration().set(OptinPonderationMapper.KEY_TYPE, Integer.toString(Optin.AID));
        mapDriver.getConfiguration().set(OptinPonderationMapper.FLAG_VALUE, "0");

        String inputValue = String.join(TAB, AID, "1", MAC);

        mapDriver.withInput(new Text(), new Text(inputValue));

        mapDriver.runTest();
    }

    @Test
    public void testLineWithoutHashKeyAidGdprNoOptinShouldOutputKeyAidAndValueWithoutHash() throws IOException {
        String keyType = Integer.toString(Optin.AID);
        String flagValue = "0";
        String inputValue = String.join(TAB, AID, "1", MAC, STB_TYPE, "", "", OPTIN_TV_VAL, OPTIN_PUB_VAL, PUB_ACT_DATE, PUB_ORIGIN, OPTIN_STAT, OPTIN_MEDIAMETRIE, LAST_OPTIN_MEDIAMETRIE);
        String expectedKey = AID;
        String expectedValue = String.join(TAB, OptinPonderationMapper.OPTIN_TAG, AID, "1", MAC, STB_TYPE, AID, MAC, OPTIN_TV_VAL, OPTIN_PUB_VAL, PUB_ACT_DATE, PUB_ORIGIN, OPTIN_STAT, OPTIN_MEDIAMETRIE, LAST_OPTIN_MEDIAMETRIE);

        testLine(keyType, flagValue, inputValue, expectedKey, expectedValue);
    }

    @Test
    public void testKeyAidGdprNoOptinShouldOutputKeyAidAndValueWithoutHash() throws IOException {
        String keyType = Integer.toString(Optin.AID);
        String flagValue = "0";
        String inputValue = String.join(TAB, AID, "1", MAC, STB_TYPE, AID_HASH, MAC_HASH, OPTIN_TV_VAL, OPTIN_PUB_VAL, PUB_ACT_DATE, PUB_ORIGIN, OPTIN_STAT, OPTIN_MEDIAMETRIE, LAST_OPTIN_MEDIAMETRIE);
        String expectedKey = AID;
        String expectedValue = String.join(TAB, OPTIN_TAG, AID, "1", MAC, STB_TYPE, AID, MAC, OPTIN_TV_VAL, OPTIN_PUB_VAL, PUB_ACT_DATE, PUB_ORIGIN, OPTIN_STAT, OPTIN_MEDIAMETRIE, LAST_OPTIN_MEDIAMETRIE);

        testLine(keyType, flagValue, inputValue, expectedKey, expectedValue);
    }

    @Test
    public void testKeyMacGdprNoOptinShouldOutputKeyMacAndValueWithoutHash() throws IOException {
        String keyType = Integer.toString(Optin.MAC);
        String flagValue = "0";
        String inputValue = String.join(TAB, AID, "1", MAC, STB_TYPE, AID_HASH, MAC_HASH, OPTIN_TV_VAL, OPTIN_PUB_VAL, PUB_ACT_DATE, PUB_ORIGIN, OPTIN_STAT, OPTIN_MEDIAMETRIE, LAST_OPTIN_MEDIAMETRIE);
        String expectedKey = MAC;
        String expectedValue = String.join(TAB, OPTIN_TAG, AID, "1", MAC, STB_TYPE, AID, MAC, OPTIN_TV_VAL, OPTIN_PUB_VAL, PUB_ACT_DATE, PUB_ORIGIN, OPTIN_STAT, OPTIN_MEDIAMETRIE, LAST_OPTIN_MEDIAMETRIE);

        testLine(keyType, flagValue, inputValue, expectedKey, expectedValue);
    }

    @Test
    public void testKeyAidHashGdprNoOptinShouldOutputKeyAidAndValueWithoutHash() throws IOException {
        String keyType = Integer.toString(Optin.AID_HASH);
        String flagValue = "0";
        String inputValue = String.join(TAB, AID, "1", MAC, STB_TYPE, AID_HASH, MAC_HASH, OPTIN_TV_VAL, OPTIN_PUB_VAL, PUB_ACT_DATE, PUB_ORIGIN, OPTIN_STAT, OPTIN_MEDIAMETRIE, LAST_OPTIN_MEDIAMETRIE);
        String expectedKey = AID;
        String expectedValue = String.join(TAB, OPTIN_TAG, AID, "1", MAC, STB_TYPE, AID, MAC, OPTIN_TV_VAL, OPTIN_PUB_VAL, PUB_ACT_DATE, PUB_ORIGIN, OPTIN_STAT, OPTIN_MEDIAMETRIE, LAST_OPTIN_MEDIAMETRIE);

        testLine(keyType, flagValue, inputValue, expectedKey, expectedValue);
    }

    @Test
    public void testKeyMacHashGdprNoOptinShouldOputputKeyMacAndValueWithoutHash() throws IOException {
        String keyType = Integer.toString(Optin.MAC_HASH);
        String flagValue = "0";
        String inputValue = String.join(TAB, AID, "1", MAC, STB_TYPE, AID_HASH, MAC_HASH, OPTIN_TV_VAL, OPTIN_PUB_VAL, PUB_ACT_DATE, PUB_ORIGIN, OPTIN_STAT, OPTIN_MEDIAMETRIE, LAST_OPTIN_MEDIAMETRIE);
        String expectedKey = MAC;
        String expectedValue = String.join(TAB, OPTIN_TAG, AID, "1", MAC, STB_TYPE, AID, MAC, OPTIN_TV_VAL, OPTIN_PUB_VAL, PUB_ACT_DATE, PUB_ORIGIN, OPTIN_STAT, OPTIN_MEDIAMETRIE, LAST_OPTIN_MEDIAMETRIE);

        testLine(keyType, flagValue, inputValue, expectedKey, expectedValue);
    }

    @Test
    public void testLineWithoutHashKeyAidGdprYesOptinShouldOputputKeyAidAndValueWithHash() throws IOException {
        String keyType = Integer.toString(Optin.AID);
        String flagValue = "1";
        String inputValue = String.join(TAB, AID, "1", MAC, STB_TYPE, "", "", OPTIN_TV_VAL, OPTIN_PUB_VAL, PUB_ACT_DATE, PUB_ORIGIN, OPTIN_STAT, OPTIN_MEDIAMETRIE, LAST_OPTIN_MEDIAMETRIE);
        String expectedKey = AID;
        String expectedValue = String.join(TAB, OPTIN_TAG, AID, "1", MAC, STB_TYPE, "", "", OPTIN_TV_VAL, OPTIN_PUB_VAL, PUB_ACT_DATE, PUB_ORIGIN, OPTIN_STAT, OPTIN_MEDIAMETRIE, LAST_OPTIN_MEDIAMETRIE);

        testLine(keyType, flagValue, inputValue, expectedKey, expectedValue);
    }

    @Test
    public void testKeyAidGdprYesOptinShouldOputputKeyAidAndValueWithHash() throws IOException {
        String keyType = Integer.toString(Optin.AID);
        String flagValue = "1";
        String inputValue = String.join(TAB, AID, "1", MAC, STB_TYPE, AID_HASH, MAC_HASH, OPTIN_TV_VAL, OPTIN_PUB_VAL, PUB_ACT_DATE, PUB_ORIGIN, OPTIN_STAT, OPTIN_MEDIAMETRIE, LAST_OPTIN_MEDIAMETRIE);
        String expectedKey = AID;
        String expectedValue = String.join(TAB, OPTIN_TAG, AID, "1", MAC, STB_TYPE, AID_HASH, MAC_HASH, OPTIN_TV_VAL, OPTIN_PUB_VAL, PUB_ACT_DATE, PUB_ORIGIN, OPTIN_STAT, OPTIN_MEDIAMETRIE, LAST_OPTIN_MEDIAMETRIE);

        testLine(keyType, flagValue, inputValue, expectedKey, expectedValue);
    }

    @Test
    public void testKeyMacGdprYesOptinShouldOputputKeyMacAndValueWithHash() throws IOException {
        String keyType = Integer.toString(Optin.MAC);
        String flagValue = "1";
        String inputValue = String.join(TAB, AID, "1", MAC, STB_TYPE, AID_HASH, MAC_HASH, OPTIN_TV_VAL, OPTIN_PUB_VAL, PUB_ACT_DATE, PUB_ORIGIN, OPTIN_STAT, OPTIN_MEDIAMETRIE, LAST_OPTIN_MEDIAMETRIE);
        String expectedKey = MAC;
        String expectedValue = String.join(TAB, OPTIN_TAG, AID, "1", MAC, STB_TYPE, AID_HASH, MAC_HASH, OPTIN_TV_VAL, OPTIN_PUB_VAL, PUB_ACT_DATE, PUB_ORIGIN, OPTIN_STAT, OPTIN_MEDIAMETRIE, LAST_OPTIN_MEDIAMETRIE);

        testLine(keyType, flagValue, inputValue, expectedKey, expectedValue);
    }

    @Test
    public void testKeyAidHashGdprYesOptinShouldOputputKeyAidHashAndValueWithHash() throws IOException {
        String keyType = Integer.toString(Optin.AID_HASH);
        String flagValue = "1";
        String inputValue = String.join(TAB, AID, "1", MAC, STB_TYPE, AID_HASH, MAC_HASH, OPTIN_TV_VAL, OPTIN_PUB_VAL, PUB_ACT_DATE, PUB_ORIGIN, OPTIN_STAT, OPTIN_MEDIAMETRIE, LAST_OPTIN_MEDIAMETRIE);
        String expectedKey = AID_HASH;
        String expectedValue = String.join(TAB, OPTIN_TAG, AID, "1", MAC, STB_TYPE, AID_HASH, MAC_HASH, OPTIN_TV_VAL, OPTIN_PUB_VAL, PUB_ACT_DATE, PUB_ORIGIN, OPTIN_STAT, OPTIN_MEDIAMETRIE, LAST_OPTIN_MEDIAMETRIE);


        testLine(keyType, flagValue, inputValue, expectedKey, expectedValue);
    }

    @Test
    public void testKeyMacHashGdprYesOptinShouldOputputKeyMacHashAndValueWithHash() throws IOException {
        String keyType = Integer.toString(Optin.MAC_HASH);
        String flagValue = "1";
        String inputValue = String.join(TAB, AID, "1", MAC, STB_TYPE, AID_HASH, MAC_HASH, OPTIN_TV_VAL, OPTIN_PUB_VAL, PUB_ACT_DATE, PUB_ORIGIN, OPTIN_STAT, OPTIN_MEDIAMETRIE, LAST_OPTIN_MEDIAMETRIE);
        String expectedKey = MAC_HASH;
        String expectedValue = String.join(TAB, OPTIN_TAG, AID, "1", MAC, STB_TYPE, AID_HASH, MAC_HASH, OPTIN_TV_VAL, OPTIN_PUB_VAL, PUB_ACT_DATE, PUB_ORIGIN, OPTIN_STAT, OPTIN_MEDIAMETRIE, LAST_OPTIN_MEDIAMETRIE);


        testLine(keyType, flagValue, inputValue, expectedKey, expectedValue);
    }

    @Test
    public void testKeyAidHashGdprNanOptinShouldOputputKeyAidAndValueWithoutHash() throws IOException {
        String keyType = Integer.toString(Optin.AID_HASH);
        String flagValue = "X";
        String inputValue = String.join(TAB, AID, "1", MAC, STB_TYPE, AID_HASH, MAC_HASH, OPTIN_TV_VAL, OPTIN_PUB_VAL, PUB_ACT_DATE, PUB_ORIGIN, OPTIN_STAT, OPTIN_MEDIAMETRIE, LAST_OPTIN_MEDIAMETRIE);
        String expectedKey = AID;
        String expectedValue = String.join(TAB, OPTIN_TAG, AID, "1", MAC, STB_TYPE, AID, MAC, OPTIN_TV_VAL, OPTIN_PUB_VAL, PUB_ACT_DATE, PUB_ORIGIN, OPTIN_STAT, OPTIN_MEDIAMETRIE, LAST_OPTIN_MEDIAMETRIE);


        testLine(keyType, flagValue, inputValue, expectedKey, expectedValue);

    }

    @Test
    public void testKeyAidHashGdprBadFlagOptinShouldOputputKeyAidAndValueWithoutHash() throws IOException {
        String keyType = Integer.toString(Optin.AID_HASH);
        String flagValue = "";
        String inputValue = String.join(TAB, AID, "1", MAC, STB_TYPE, AID_HASH, MAC_HASH, OPTIN_TV_VAL, OPTIN_PUB_VAL, PUB_ACT_DATE, PUB_ORIGIN, OPTIN_STAT, OPTIN_MEDIAMETRIE, LAST_OPTIN_MEDIAMETRIE);
        String expectedKey = AID;
        String expectedValue = String.join(TAB, OPTIN_TAG, AID, "1", MAC, STB_TYPE, AID, MAC, OPTIN_TV_VAL, OPTIN_PUB_VAL, PUB_ACT_DATE, PUB_ORIGIN, OPTIN_STAT, OPTIN_MEDIAMETRIE, LAST_OPTIN_MEDIAMETRIE);

        testLine(keyType, flagValue, inputValue, expectedKey, expectedValue);
    }

    @Test
    public void testKeyAidHashGdprNoFileOptinShouldOputputKeyAidAndValueWithoutHash() throws IOException {
        //override flag file with no file
        mapDriver.getConfiguration().set(OptinPonderationMapper.FLAG_VALUE, "");

        String keyType = Integer.toString(Optin.AID_HASH);
        String flagValue = "";
        String inputValue = String.join(TAB, AID, "1", MAC, STB_TYPE, AID_HASH, MAC_HASH, OPTIN_TV_VAL, OPTIN_PUB_VAL, PUB_ACT_DATE, PUB_ORIGIN, OPTIN_STAT, OPTIN_MEDIAMETRIE, LAST_OPTIN_MEDIAMETRIE);
        String expectedKey = AID;
        String expectedValue = String.join(TAB, OPTIN_TAG, AID, "1", MAC, STB_TYPE, AID, MAC, OPTIN_TV_VAL, OPTIN_PUB_VAL, PUB_ACT_DATE, PUB_ORIGIN, OPTIN_STAT, OPTIN_MEDIAMETRIE, LAST_OPTIN_MEDIAMETRIE);

        testLine(keyType, flagValue, inputValue, expectedKey, expectedValue);
    }

    @Test
    public void testNotOptin() throws IOException {
        mapDriver.getConfiguration().set(OptinPonderationMapper.KEY_TYPE, Integer.toString(Optin.AID_HASH));
        mapDriver.getConfiguration().set(OptinPonderationMapper.FLAG_VALUE, "1");

        String inputValue = String.join(TAB, AID, "0", MAC, STB_TYPE, AID_HASH, MAC_HASH, OPTIN_TV_VAL, OPTIN_PUB_VAL, PUB_ACT_DATE, PUB_ORIGIN);

        mapDriver.withInput(new Text(), new Text(inputValue));

        mapDriver.runTest();

    }

    @Test
    public void testOptinNan() throws IOException {
        mapDriver.getConfiguration().set(OptinPonderationMapper.KEY_TYPE, Integer.toString(Optin.AID_HASH));
        mapDriver.getConfiguration().set(OptinPonderationMapper.FLAG_VALUE, "1");

        String inputValue = String.join(TAB, AID, "A", MAC, STB_TYPE, AID_HASH, MAC_HASH, OPTIN_TV_VAL, OPTIN_PUB_VAL, PUB_ACT_DATE, PUB_ORIGIN);

        mapDriver.withInput(new Text(), new Text(inputValue));

        mapDriver.runTest();

    }

    private void testLine(String keyType, String flagValue, String inputValue, String expectedKey, String expectedValue) throws IOException {
        mapDriver.getConfiguration().set(OptinPonderationMapper.KEY_TYPE, keyType);
        mapDriver.getConfiguration().set(OptinPonderationMapper.FLAG_VALUE, flagValue);
        mapDriver.withInput(new Text(), new Text(inputValue));
        mapDriver.withOutput(new Text(expectedKey + FieldsUtils.DASH + OptinPonderationMapper.DEVICE_STB), new Text(expectedValue));
        mapDriver.withOutput(new Text(expectedKey + FieldsUtils.DASH + OptinPonderationMapper.DEVICE_WEB), new Text(expectedValue));
        mapDriver.withOutput(new Text(expectedKey + FieldsUtils.DASH + OptinPonderationMapper.DEVICE_MOBILE), new Text(expectedValue));
        mapDriver.withOutput(new Text(expectedKey + FieldsUtils.DASH + OptinPonderationMapper.DEVICE_SMARTTV), new Text(expectedValue));
        mapDriver.runTest();
    }


}