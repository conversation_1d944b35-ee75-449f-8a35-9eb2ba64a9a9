package com.orange.profiling.ute.ravenne.stat.countbytimeslot;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.PrintStream;
import java.util.ArrayList;
import java.util.List;

import org.apache.hadoop.io.NullWritable;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mrunit.mapreduce.ReduceDriver;
import org.apache.log4j.BasicConfigurator;
import org.apache.log4j.Level;
import org.apache.log4j.Logger;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.runners.MockitoJUnitRunner;

import com.orange.profiling.common.utils.FieldsUtils;
import com.orange.profiling.ute.ravenne.logger.RavenneKpiLogger;

@RunWith(MockitoJUnitRunner.class)
public class CountByTimeslotReducerTest {

    private static final String TAB = FieldsUtils.TAB;
    private static final String PIPE = FieldsUtils.PIPE;

    ReduceDriver<Text, Text, NullWritable, Text> reduceDriver;
    private final ByteArrayOutputStream errContent = new ByteArrayOutputStream();

    @Mock
    private RavenneKpiLogger mockLogger;

    @Before
    public void setUp() {

        CountByTimeslotReducer reducer = new CountByTimeslotReducer();
        reducer.setKpiLogger(mockLogger);
        reduceDriver = ReduceDriver.newReduceDriver(reducer);

        reduceDriver.getConfiguration().set(MainCountByTimeslot.CONF_NB_AID, "10000");
        reduceDriver.getConfiguration().set(MainCountByTimeslot.CONF_PROCESS_DATE, "20190904");

        BasicConfigurator.configure();
        Logger.getRootLogger().setLevel(Level.ERROR);
    }

    @Before
    public void setUpStreams() {
        System.setErr(new PrintStream(this.errContent));
    }

    @After
    public final void cleanUpStreams() {
        System.setErr(null);
    }

    @Test
    public final void testSimpleReducer() throws IOException, InterruptedException, ClassNotFoundException {

        String timebox = "d2t6";
        String nbconcept = "12";



        List<Text> values = new ArrayList<Text>();
        values.add(new Text("500"));
        values.add(new Text("300"));
        values.add(new Text("200"));
        values.add(new Text("200"));

        reduceDriver.withInput(new Text(timebox+PIPE+nbconcept), values);

        reduceDriver.withOutput(NullWritable.get(),
                new Text(String.join(TAB, timebox, nbconcept, "1200", "12")));

        reduceDriver.runTest();

        withKpiLoggerSend(timebox, nbconcept, 1200L, 12L);

    }

    @Test
    public final void testSimpleReducerFloor() throws IOException, InterruptedException, ClassNotFoundException {

        String timebox = "d2t6";
        String nbconcept = "12";



        List<Text> values = new ArrayList<Text>();
        values.add(new Text("500"));
        values.add(new Text("300"));
        values.add(new Text("200"));
        values.add(new Text("40"));

        reduceDriver.withInput(new Text(timebox+PIPE+nbconcept), values);

        reduceDriver.withOutput(NullWritable.get(),
                new Text(String.join(TAB, timebox, nbconcept, "1040", "10")));

        reduceDriver.runTest();

        withKpiLoggerSend(timebox, nbconcept, 1040L, 10L);
    }

    @Test
    public final void testSimpleReducerFifity() throws IOException, InterruptedException, ClassNotFoundException {

        String timebox = "d2t6";
        String nbconcept = "12";



        List<Text> values = new ArrayList<Text>();
        values.add(new Text("500"));
        values.add(new Text("250"));

        reduceDriver.withInput(new Text(timebox+PIPE+nbconcept), values);

        reduceDriver.withOutput(NullWritable.get(),
                new Text(String.join(TAB, timebox, nbconcept, "750", "8")));

        reduceDriver.runTest();

        withKpiLoggerSend(timebox, nbconcept, 750L, 8L);
    }

    @Test
    public final void testSimpleReducerCeil() throws IOException, InterruptedException, ClassNotFoundException {

        String timebox = "d2t6";
        String nbconcept = "12";



        List<Text> values = new ArrayList<Text>();
        values.add(new Text("500"));
        values.add(new Text("300"));
        values.add(new Text("160"));

        reduceDriver.withInput(new Text(timebox+PIPE+nbconcept), values);

        reduceDriver.withOutput(NullWritable.get(),
                new Text(String.join(TAB, timebox, nbconcept, "960", "10")));

        reduceDriver.runTest();
        withKpiLoggerSend(timebox, nbconcept, 960L, 10L);
    }

    @Test
    public final void testReducerBadNumber() throws IOException, InterruptedException, ClassNotFoundException {

        String timebox = "d2t6";
        String nbconcept = "12";



        List<Text> values = new ArrayList<Text>();
        values.add(new Text("500"));
        values.add(new Text("300"));
        values.add(new Text("200"));
        values.add(new Text("A"));

        reduceDriver.withInput(new Text(timebox+PIPE+nbconcept), values);

        reduceDriver.withOutput(NullWritable.get(),
                new Text(String.join(TAB, timebox, nbconcept, "1000", "10")));

        reduceDriver.runTest();
        withKpiLoggerSend(timebox, nbconcept, 1000L, 10L);
    }

    @Test
    public final void testReducerOnlyBadValue() throws IOException, InterruptedException, ClassNotFoundException {

        String timebox = "d2t6";
        String nbconcept = "12";



        List<Text> values = new ArrayList<Text>();
        values.add(new Text("BAD"));

        reduceDriver.withInput(new Text(timebox+PIPE+nbconcept), values);

        reduceDriver.runTest();
        Mockito.verifyZeroInteractions(mockLogger);
    }

    @Test
    public final void testReducerBadKeyFormat() throws IOException, InterruptedException, ClassNotFoundException {

        String timebox = "d2t6";
        String nbconcept = "12";



        List<Text> values = new ArrayList<Text>();
        values.add(new Text("100"));

        reduceDriver.withInput(new Text(timebox+"-"+nbconcept), values);

        reduceDriver.runTest();
        Mockito.verifyZeroInteractions(mockLogger);
    }

    private void withKpiLoggerSend(String timebox, String nbconcept, long nbProfils, long profilsPct) {
        Mockito.verify(mockLogger).sendDeltaCountByTimeslot(timebox, nbconcept, nbProfils, profilsPct);
    }

}
