package com.orange.profiling.ute.ravenne.transformer;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.PrintStream;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.List;

import com.orange.profiling.common.mapred.MosWriter;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mrunit.mapreduce.ReduceDriver;
import org.apache.log4j.BasicConfigurator;
import org.apache.log4j.Level;
import org.apache.log4j.Logger;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.runners.MockitoJUnitRunner;
import com.orange.profiling.common.utils.FieldsUtils;

@RunWith(MockitoJUnitRunner.class)
public class TransformProfilingHashReducerTest {

    private ReduceDriver<Text, Text, Text, Text> reduceDriver;
    private final ByteArrayOutputStream errContent = new ByteArrayOutputStream();

    private URI runtimeRessourceFolder;

    @Mock
    private MosWriter mockMosWriter;

    private static final String TAB = "\t";

    @Before
    public void setUp() throws URISyntaxException {

        runtimeRessourceFolder = TransformProfilingHashReducerTest.class.getResource("/ute-ravenne-transformer/").toURI();
        TransformProfilingHashReducer TransformReducer = new TransformProfilingHashReducer();
        TransformReducer.setMosWriter(mockMosWriter);
        String inputPathMapping = runtimeRessourceFolder.resolve("in/mappingott/part-r-00001").getPath();

        reduceDriver = ReduceDriver.newReduceDriver(TransformReducer);
        reduceDriver.getConfiguration().set(MainTransformProfilingHash.MAPPING_DATA, inputPathMapping);

        BasicConfigurator.configure();
        Logger.getRootLogger().setLevel(Level.ERROR);
    }

    @Before
    public void setUpStreams() {
        System.setErr(new PrintStream(this.errContent));
    }

    @After
    public void cleanUpStreams() {
        System.setErr(null);
    }

    @Test
    public final void testReducerOk() throws IOException, InterruptedException, ClassNotFoundException {

        String aid = "123456789";
        String profilingHash = "ottprofilingHash1";
        String profilingHashNotExist = "ottprofilingHashXX";
        String AidHash = "78e670bad57c1a9e";
        String concepts = "Genre Orange/émission/football/émission football,sujets/sport/compétitions,sujets/sport,sous-genres/sport,sujets/sport/football";
        String day = "4";
        String period = "3";
        String timeslot = "6";
        String vodorlive = "LIVE";
        String zapduration = "15";
        String zaptime = "**********";
        String provider = "111";
        String content_id = "id123";
        String title = "Title of program";
        String externalEntertainmentID = "episode-Title of serie and episode-season-X-episode-Y-2000";
        String seasonName = "Saison X";
        String serieName = "Serie X";
        String offerName = "LIVE";
        String device = "stb";
        String deviceOtt = "mobile";

        String outputValue = concepts + TAB + day + TAB + period + TAB + timeslot
                + TAB + vodorlive + TAB + zapduration + TAB + zaptime + TAB + provider
                + TAB + content_id + TAB + title + TAB + externalEntertainmentID + TAB + seasonName + TAB + serieName + TAB + offerName;


        List<Text> values = new ArrayList<Text>();
        List<Text> valuesOtt = new ArrayList<Text>();

        values.add(new Text(aid + TAB + concepts + TAB + day + TAB + period + TAB + timeslot
                + TAB + vodorlive + TAB + zapduration + TAB + zaptime + TAB + provider
                + TAB + content_id + TAB + title + TAB + externalEntertainmentID + TAB + seasonName + TAB + serieName + TAB + offerName + TAB + device));

        valuesOtt.add(new Text(profilingHash + TAB + concepts + TAB + day + TAB + period + TAB + timeslot
                + TAB + vodorlive + TAB + zapduration + TAB + zaptime + TAB + provider
                + TAB + content_id + TAB + title + TAB + externalEntertainmentID + TAB + seasonName + TAB + serieName + TAB + offerName + TAB + deviceOtt));

        reduceDriver.withInput(new Text(aid), values);
        reduceDriver.withInput(new Text(profilingHash), valuesOtt);
        reduceDriver.withInput(new Text(profilingHashNotExist), valuesOtt);

        reduceDriver.withCounter(Counters.NB_PROFILINGHASH_NO_MAPPING,1);

        reduceDriver.runTest();

        Mockito.verify(mockMosWriter).open(Mockito.any());
        int nbCall = 0;
        nbCall += withSelectedTransform(aid, device, outputValue);
        nbCall += withSelectedTransform(AidHash, deviceOtt, outputValue);
        Mockito.verify(mockMosWriter, Mockito.times(nbCall)).write(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any());
        Mockito.verify(mockMosWriter).close();

    }

    private int withSelectedTransform(String aid, String device, String outputValue)
            throws IOException, InterruptedException {

        String key = aid + FieldsUtils.DASH + device;
        Mockito.verify(mockMosWriter).write(MainTransformProfilingHash.OUT_SELECTED_TRANSFORM,key, outputValue, MainTransformProfilingHash.OUT_SELECTED_TRANSFORM + "/part");
        return 1;
    }

}