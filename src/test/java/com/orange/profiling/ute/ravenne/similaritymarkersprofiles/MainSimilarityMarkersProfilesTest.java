package com.orange.profiling.ute.ravenne.similaritymarkersprofiles;

import com.orange.profiling.common.exceptions.FailedJobException;
import com.orange.profiling.common.utils.unit_tests.TestUtils;
import com.orange.profiling.ute.ravenne.markerprofilessimilarity.MainMarkerProfilesSimilarity;
import com.orange.profiling.ute.ravenne.scoresmarkersprofiles.MainScoreMarkersProfiles;
import org.apache.log4j.BasicConfigurator;
import org.apache.log4j.Level;
import org.apache.log4j.Logger;
import org.junit.Before;
import org.junit.Test;

import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.fail;

/**
 * <AUTHOR>
 *
 */
public class MainSimilarityMarkersProfilesTest {

    private URI runtimeRessourceFolder;

    @Before
    public void setUp() throws URISyntaxException {
        runtimeRessourceFolder = MainSimilarityMarkersProfilesTest.class.getResource("/ute-ravenne-similaritymarkersprofiles/").toURI();

        BasicConfigurator.configure();
        Logger.getRootLogger().setLevel(Level.ERROR);
    }

    @Test
    public void testMainKo() {

        String[] args = new String[1];
        args[0] = "";
        try {
            MainMarkerProfilesSimilarity.main(args);
            fail("Should have thrown an IllegalArgumentException because no parameters");
        }
        catch (Exception e) {
            assertEquals(
                    "Takes 2 arguments: inputPath outputPath",
                    e.getMessage());
        }
    }

    @Test
    public final void testMainOk()
            throws ClassNotFoundException, IOException, InterruptedException, FailedJobException {

        final String scores = runtimeRessourceFolder.resolve("in/score/*").getPath();
        final String output = runtimeRessourceFolder.resolve("output/").getPath();

        String[] args = new String[2];
        args[0] = scores;
        args[1] = output;

        final String expectedFile = runtimeRessourceFolder.resolve("expected").getPath();

        MainMarkerProfilesSimilarity.main(args);

            List<String> expected = TestUtils.getLinesFromPath(expectedFile);
            List<String> actual = TestUtils.getLinesFromPath(output , "part*");
            assertEquals("test markers profiles", expected, actual);


    }
    
}