package com.orange.profiling.ute.ravenne.genetic.concat;

import static org.junit.Assert.*;

import java.util.ArrayList;
import java.util.List;

import org.apache.hadoop.io.Text;
import org.junit.Before;
import org.junit.Test;

import com.orange.profiling.common.utils.FieldsUtils;
import com.orange.profiling.ute.ravenne.dao.TimeboxZapAggregation;

public class ConcatHelperTest {
    private static final String DASH = FieldsUtils.DASH;
    private static final String TAB = FieldsUtils.TAB;
    private static final String AID = "123456789";
    private static final String TIMEBOX = "d4t2";

    @Before
    public void setUp() throws Exception {
    }

    @Test
    public void testKeySplit() {
        Text key = new Text(AID+DASH+TIMEBOX);
        ConcatHelper concatHelper = new ConcatHelper();
        concatHelper.initHelper(key);

        assertEquals("Check KEY", AID, concatHelper.getAid());
        assertEquals("Check Timebox", TIMEBOX, concatHelper.getTimebox());
    }

    @Test
    public void testAggregateOneConceptsWeightWithoutNb() {
        Text key = new Text(AID+DASH+TIMEBOX);
        ConcatHelper concatHelper = new ConcatHelper();
        concatHelper.initHelper(key);

        String concepts = "ambiance et ton/humoristique=300,sujet/sport=200,type/live=100";
        long ratioVodLive = 50;
        int nbValues = 1;

        List<Text> values = new ArrayList<Text>();
        values.add(new Text(String.join(TAB, ConceptsWeightsMapper.CONCEPTS_MARK,
                concepts, Long.toString(ratioVodLive))));

        concatHelper.aggregateProfilValues(values);
        TimeboxZapAggregation zapAggregation = concatHelper.getZapAggregation();

        assertEquals("ConceptsWeights", concepts,zapAggregation.getWeightedConceptsString());
        assertEquals("RatioVodLive", ratioVodLive, zapAggregation.getLiveDuration());
        assertEquals("Nb Values", nbValues, concatHelper.getNbValues());

    }

    @Test
    public void testAggregateOneConceptsWeightWithNb() {
        Text key = new Text(AID+DASH+TIMEBOX);
        ConcatHelper concatHelper = new ConcatHelper();
        concatHelper.initHelper(key);

        String concepts = "ambiance et ton/humoristique=300,sujet/sport=200,type/live=100";
        long ratioVodLive = 50;
        int nbValues = 2;

        List<Text> values = new ArrayList<Text>();
        values.add(new Text(String.join(TAB, ConceptsWeightsMapper.CONCEPTS_MARK,
                concepts, Long.toString(ratioVodLive), Integer.toString(nbValues))));

        concatHelper.aggregateProfilValues(values);
        TimeboxZapAggregation zapAggregation = concatHelper.getZapAggregation();

        assertEquals("ConceptsWeights", concepts,zapAggregation.getWeightedConceptsString());
        assertEquals("RatioVodLive", ratioVodLive, zapAggregation.getLiveDuration());
        assertEquals("Nb Values", nbValues, concatHelper.getNbValues());

    }

    @Test
    public void testAggregateOneConceptsWeightWithBadNb() {
        Text key = new Text(AID+DASH+TIMEBOX);
        ConcatHelper concatHelper = new ConcatHelper();
        concatHelper.initHelper(key);

        String concepts = "ambiance et ton/humoristique=300,sujet/sport=200,type/live=100";
        long ratioVodLive = 50;
        int nbValues = 1;

        List<Text> values = new ArrayList<Text>();
        values.add(new Text(String.join(TAB, ConceptsWeightsMapper.CONCEPTS_MARK,
                concepts, Long.toString(ratioVodLive), "1A")));

        concatHelper.aggregateProfilValues(values);
        TimeboxZapAggregation zapAggregation = concatHelper.getZapAggregation();

        assertEquals("ConceptsWeights", concepts,zapAggregation.getWeightedConceptsString());
        assertEquals("RatioVodLive", ratioVodLive, zapAggregation.getLiveDuration());
        assertEquals("Nb Values", nbValues, concatHelper.getNbValues());

    }

    @Test
    public void testAggregateThreeConceptsWeight() {
        Text key = new Text(AID+DASH+TIMEBOX);
        ConcatHelper concatHelper = new ConcatHelper();
        concatHelper.initHelper(key);

        List<Text> values = new ArrayList<Text>();

        String concepts1 = "ambiance et ton/humoristique=300,sujet/sport=200,type/live=100";
        long ratioVodLive1 = 60;
        int nbValues1 = 2;
        values.add(new Text(String.join(TAB, ConceptsWeightsMapper.CONCEPTS_MARK,
                concepts1, Long.toString(ratioVodLive1), Integer.toString(nbValues1))));

        String concepts2 = "ambiance et ton/suspens=100,sujet/sport=100,type/série=200";
        long ratioVodLive2 = 40;
        int nbValues2 = 1;
        values.add(new Text(String.join(TAB, ConceptsWeightsMapper.CONCEPTS_MARK,
                concepts2, Long.toString(ratioVodLive2), Integer.toString(nbValues2))));

        String concepts3 = "genre/comédie=300,sujet/sport=100,type/film=100";
        long ratioVodLive3 = 80;
        int nbValues3 = 3;
        values.add(new Text(String.join(TAB, ConceptsWeightsMapper.CONCEPTS_MARK,
                concepts3, Long.toString(ratioVodLive3), Integer.toString(nbValues3))));


        concatHelper.aggregateProfilValues(values);
        TimeboxZapAggregation zapAggregation = concatHelper.getZapAggregation();

        String conceptsExpected = "sujet/sport=400,ambiance et ton/humoristique=300,"
                + "genre/comédie=300,type/série=200,ambiance et ton/suspens=100,type/film=100,type/live=100";
        long ratioVodLiveExpected = 180;
        int nbValuesExpected = 6;

        assertEquals("ConceptsWeights", conceptsExpected,zapAggregation.getWeightedConceptsString());
        assertEquals("RatioVodLive", ratioVodLiveExpected, zapAggregation.getLiveDuration());
        assertEquals("Nb Values", nbValuesExpected, concatHelper.getNbValues());

    }

    @Test
    public void testAggregateOneLiveChannelsDurationsWithoutNb() {
        Text key = new Text(AID+DASH+TIMEBOX);
        ConcatHelper concatHelper = new ConcatHelper();
        concatHelper.initHelper(key);

        String liveChannels = "1234=300,5678=200,7654=100";
        int nbLives = 1;

        List<Text> values = new ArrayList<Text>();
        values.add(new Text(String.join(TAB, TimeboxZapAggregation.LIVE,
                liveChannels)));

        concatHelper.aggregateProfilValues(values);
        TimeboxZapAggregation zapAggregation = concatHelper.getZapAggregation();

        assertEquals("LiveChannelsDurations", liveChannels,zapAggregation.getLiveChannelDurationString());
        assertEquals("Nb Values", nbLives, concatHelper.getNbLives());

    }

    @Test
    public void testAggregateOneLiveChannelsDurationsWithNb() {
        Text key = new Text(AID+DASH+TIMEBOX);
        ConcatHelper concatHelper = new ConcatHelper();
        concatHelper.initHelper(key);

        String liveChannels = "1234=300,5678=200,7654=100";
        int nbLives = 2;

        List<Text> values = new ArrayList<Text>();
        values.add(new Text(String.join(TAB, TimeboxZapAggregation.LIVE,
                liveChannels, Integer.toString(nbLives))));

        concatHelper.aggregateProfilValues(values);
        TimeboxZapAggregation zapAggregation = concatHelper.getZapAggregation();

        assertEquals("LiveChannelsDurations", liveChannels,zapAggregation.getLiveChannelDurationString());
        assertEquals("Nb Values", nbLives, concatHelper.getNbLives());

    }

    @Test
    public void testAggregateOneLiveChannelsDurationsWithBadNb() {
        Text key = new Text(AID+DASH+TIMEBOX);
        ConcatHelper concatHelper = new ConcatHelper();
        concatHelper.initHelper(key);

        String liveChannels = "1234=300,5678=200,7654=100";
        int nbLives = 1;

        List<Text> values = new ArrayList<Text>();
        values.add(new Text(String.join(TAB, TimeboxZapAggregation.LIVE,
                liveChannels, "1A")));

        concatHelper.aggregateProfilValues(values);
        TimeboxZapAggregation zapAggregation = concatHelper.getZapAggregation();

        assertEquals("LiveChannelsDurations", liveChannels,zapAggregation.getLiveChannelDurationString());
        assertEquals("Nb Values", nbLives, concatHelper.getNbLives());

    }

    @Test
    public void testAggregateThreeLiveChannelsDurations() {
        Text key = new Text(AID+DASH+TIMEBOX);
        ConcatHelper concatHelper = new ConcatHelper();
        concatHelper.initHelper(key);

        List<Text> values = new ArrayList<Text>();

        String liveChannels1 = "1234=300,5678=200,7654=100";
        int nbLives1 = 2;
        values.add(new Text(String.join(TAB, TimeboxZapAggregation.LIVE,
                liveChannels1, Integer.toString(nbLives1))));

        String liveChannels2 = "2222=100,5678=100,9876=200";
        int nbLives2 = 1;
        values.add(new Text(String.join(TAB, TimeboxZapAggregation.LIVE,
                liveChannels2, Integer.toString(nbLives2))));

        String liveChannels3 = "3579=300,5678=100,6543=100";
        int nbLives3 = 3;
        values.add(new Text(String.join(TAB, TimeboxZapAggregation.LIVE,
                liveChannels3, Integer.toString(nbLives3))));


        concatHelper.aggregateProfilValues(values);
        TimeboxZapAggregation zapAggregation = concatHelper.getZapAggregation();

        String liveChannelsExpected = "5678=400,1234=300,3579=300,9876=200,2222=100,6543=100,7654=100";
        int nbLivesExpected = 6;

        assertEquals("LiveChannelsDurations", liveChannelsExpected,zapAggregation.getLiveChannelDurationString());
        assertEquals("Nb Values", nbLivesExpected, concatHelper.getNbLives());

    }

    @Test
    public void testAggregateOneOdProvidersDurationsWithoutNb() {
        Text key = new Text(AID+DASH+TIMEBOX);
        ConcatHelper concatHelper = new ConcatHelper();
        concatHelper.initHelper(key);

        String odProviders = "FTVOD=300,MYM6=200,TF1REPLAY=100";
        int nbOds = 1;

        List<Text> values = new ArrayList<Text>();
        values.add(new Text(String.join(TAB, TimeboxZapAggregation.VOD,
                odProviders)));

        concatHelper.aggregateProfilValues(values);
        TimeboxZapAggregation zapAggregation = concatHelper.getZapAggregation();

        assertEquals("OdProvidersDurations", odProviders,zapAggregation.getOdProviderDurationString());
        assertEquals("Nb Values", nbOds, concatHelper.getNbOds());

    }

    @Test
    public void testAggregateOneOdProvidersDurationsWithNb() {
        Text key = new Text(AID+DASH+TIMEBOX);
        ConcatHelper concatHelper = new ConcatHelper();
        concatHelper.initHelper(key);

        String odProviders = "FTVOD=300,MYM6=200,TF1REPLAY=100";
        int nbOds = 2;

        List<Text> values = new ArrayList<Text>();
        values.add(new Text(String.join(TAB, TimeboxZapAggregation.VOD,
                odProviders, Integer.toString(nbOds))));

        concatHelper.aggregateProfilValues(values);
        TimeboxZapAggregation zapAggregation = concatHelper.getZapAggregation();

        assertEquals("OdProvidersDurations", odProviders,zapAggregation.getOdProviderDurationString());
        assertEquals("Nb Values", nbOds, concatHelper.getNbOds());

    }

    @Test
    public void testAggregateOneOdProvidersDurationsWithBadNb() {
        Text key = new Text(AID+DASH+TIMEBOX);
        ConcatHelper concatHelper = new ConcatHelper();
        concatHelper.initHelper(key);

        String odProviders = "FTVOD=300,MYM6=200,TF1REPLAY=100";
        int nbOds = 1;

        List<Text> values = new ArrayList<Text>();
        values.add(new Text(String.join(TAB, TimeboxZapAggregation.VOD,
                odProviders, "1A")));

        concatHelper.aggregateProfilValues(values);
        TimeboxZapAggregation zapAggregation = concatHelper.getZapAggregation();

        assertEquals("OdProvidersDurations", odProviders,zapAggregation.getOdProviderDurationString());
        assertEquals("Nb Values", nbOds, concatHelper.getNbOds());

    }

    @Test
    public void testAggregateThreeOdProvidersDurations() {
        Text key = new Text(AID+DASH+TIMEBOX);
        ConcatHelper concatHelper = new ConcatHelper();
        concatHelper.initHelper(key);

        List<Text> values = new ArrayList<Text>();

        String odProviders1 = "FTVOD=300,MYM6=200,TF1REPLAY=100";
        int nbOds1 = 2;
        values.add(new Text(String.join(TAB, TimeboxZapAggregation.VOD,
                odProviders1, Integer.toString(nbOds1))));

        String odProviders2 = "GVU=100,MYM6=100,VOD24=200";
        int nbOds2 = 1;
        values.add(new Text(String.join(TAB, TimeboxZapAggregation.VOD,
                odProviders2, Integer.toString(nbOds2))));

        String odProviders3 = "ISEE=300,MYM6=100,SVOD=100";
        int nbOds3 = 3;
        values.add(new Text(String.join(TAB, TimeboxZapAggregation.VOD,
                odProviders3, Integer.toString(nbOds3))));

        concatHelper.aggregateProfilValues(values);
        TimeboxZapAggregation zapAggregation = concatHelper.getZapAggregation();

        String odProvidersExpected = "MYM6=400,FTVOD=300,ISEE=300,"
                + "VOD24=200,GVU=100,SVOD=100,TF1REPLAY=100";
        int nbOdsExpected = 6;

        assertEquals("OdProvidersDurations", odProvidersExpected,zapAggregation.getOdProviderDurationString());
        assertEquals("Nb Values", nbOdsExpected, concatHelper.getNbOds());

    }


    /**
     * The goal of this test is to verify we add different information in the same zapAggregation
     */
    @Test
    public void testAggregateDifferent() {
        Text key = new Text(AID+DASH+TIMEBOX);
        ConcatHelper concatHelper = new ConcatHelper();
        concatHelper.initHelper(key);

        List<Text> values = new ArrayList<Text>();

        String concepts1 = "ambiance et ton/humoristique=300,sujet/sport=200,type/live=100";
        long ratioVodLive1 = 60;
        int nbValues1 = 2;
        values.add(new Text(String.join(TAB, ConceptsWeightsMapper.CONCEPTS_MARK,
                concepts1, Long.toString(ratioVodLive1), Integer.toString(nbValues1))));

        String concepts2 = "ambiance et ton/suspens=100,sujet/sport=100,type/série=200";
        long ratioVodLive2 = 40;
        int nbValues2 = 1;
        values.add(new Text(String.join(TAB, ConceptsWeightsMapper.CONCEPTS_MARK,
                concepts2, Long.toString(ratioVodLive2), Integer.toString(nbValues2))));

        String concepts3 = "genre/comédie=300,sujet/sport=100,type/film=100";
        long ratioVodLive3 = 80;
        int nbValues3 = 3;
        values.add(new Text(String.join(TAB, ConceptsWeightsMapper.CONCEPTS_MARK,
                concepts3, Long.toString(ratioVodLive3), Integer.toString(nbValues3))));


        String liveChannels1 = "1234=300,5678=200,7654=100";
        int nbLives1 = 2;
        values.add(new Text(String.join(TAB, TimeboxZapAggregation.LIVE,
                liveChannels1, Integer.toString(nbLives1))));

        String liveChannels2 = "2222=100,5678=100,9876=200";
        int nbLives2 = 1;
        values.add(new Text(String.join(TAB, TimeboxZapAggregation.LIVE,
                liveChannels2, Integer.toString(nbLives2))));

        String liveChannels3 = "3579=300,5678=100,6543=100";
        int nbLives3 = 3;
        values.add(new Text(String.join(TAB, TimeboxZapAggregation.LIVE,
                liveChannels3, Integer.toString(nbLives3))));


        String odProviders1 = "FTVOD=300,MYM6=200,TF1REPLAY=100";
        int nbOds1 = 2;
        values.add(new Text(String.join(TAB, TimeboxZapAggregation.VOD,
                odProviders1, Integer.toString(nbOds1))));

        String odProviders2 = "GVU=100,MYM6=100,VOD24=200";
        int nbOds2 = 1;
        values.add(new Text(String.join(TAB, TimeboxZapAggregation.VOD,
                odProviders2, Integer.toString(nbOds2))));

        String odProviders3 = "ISEE=300,MYM6=100,SVOD=100";
        int nbOds3 = 3;
        values.add(new Text(String.join(TAB, TimeboxZapAggregation.VOD,
                odProviders3, Integer.toString(nbOds3))));

        concatHelper.aggregateProfilValues(values);
        TimeboxZapAggregation zapAggregation = concatHelper.getZapAggregation();

        String conceptsExpected = "sujet/sport=400,ambiance et ton/humoristique=300,genre/comédie=300,"
                + "type/série=200,ambiance et ton/suspens=100,type/film=100,type/live=100";
        long ratioVodLiveExpected = 180;
        int nbValuesExpected = 6;

        assertEquals("ConceptsWeights", conceptsExpected,zapAggregation.getWeightedConceptsString());
        assertEquals("RatioVodLive", ratioVodLiveExpected, zapAggregation.getLiveDuration());
        assertEquals("Nb Values", nbValuesExpected, concatHelper.getNbValues());

        String liveChannelsExpected = "5678=400,1234=300,3579=300,"
                + "9876=200,2222=100,6543=100,7654=100";
        int nbLivesExpected = 6;

        assertEquals("LiveChannelsDurations", liveChannelsExpected,zapAggregation.getLiveChannelDurationString());
        assertEquals("Nb Lives", nbLivesExpected, concatHelper.getNbLives());

        String odProvidersExpected = "MYM6=400,FTVOD=300,ISEE=300,VOD24=200,"
                + "GVU=100,SVOD=100,TF1REPLAY=100";
        int nbOdsExpected = 6;

        assertEquals("OdProvidersDurations", odProvidersExpected,zapAggregation.getOdProviderDurationString());
        assertEquals("Nb Ods", nbOdsExpected, concatHelper.getNbOds());
    }

    @Test
    public void testAggregateBadMark() {
        Text key = new Text(AID+DASH+TIMEBOX);
        ConcatHelper concatHelper = new ConcatHelper();
        concatHelper.initHelper(key);

        String val = "FTVOD=300,sujet/sport=200,1234=100";
        int nb = 1;

        List<Text> values = new ArrayList<Text>();
        values.add(new Text(String.join(TAB, "ANY",
                val, "42")));

        TimeboxZapAggregation zapAggregation = concatHelper.aggregateProfilValues(values);

        assertEquals("ConceptsWeights", "",zapAggregation.getWeightedConceptsString());
        assertEquals("Nb Values", 0, concatHelper.getNbValues());
        assertEquals("LiveChannelsDurations", "",zapAggregation.getLiveChannelDurationString());
        assertEquals("Nb Lives", 0, concatHelper.getNbLives());
        assertEquals("OdProvidersDurations", "",zapAggregation.getOdProviderDurationString());
        assertEquals("Nb Ods", 0, concatHelper.getNbOds());

    }
}
