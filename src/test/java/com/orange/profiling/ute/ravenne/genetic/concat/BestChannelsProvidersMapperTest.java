package com.orange.profiling.ute.ravenne.genetic.concat;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.PrintStream;
import org.apache.hadoop.io.LongWritable;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mrunit.mapreduce.MapDriver;
import org.apache.log4j.BasicConfigurator;
import org.apache.log4j.Level;
import org.apache.log4j.Logger;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

import com.orange.profiling.common.utils.FieldsUtils;
import com.orange.profiling.ute.ravenne.genetic.Counters;

public class BestChannelsProvidersMapperTest {

    private static final String LIVE = "LIVE";
    private static final String VOD = "VOD";
    private static final String TAB = FieldsUtils.TAB;
    private static final String DASH = FieldsUtils.DASH;
    private static final String AID = "123456789";
    private static final String OPTOUT_AID = "999999999";
    private static final String PROCESS_DAY_VALUE = "5";
    private static final String TIMESLOT_OK = "d5t6";
    private static final String PERIOD_OK = "d5p3";
    private static final String DAY_OK = "d5";
    private static final String WEEK_OK = "w";
    private static final String TIMESLOT_KO = "d4t5";
    private static final String PERIOD_KO = "d3p4";
    private static final String DAY_KO = "d6";
    private static final String LIVE_CHANNELS_DURATION = "42=7200,234=3600,192=2400";
    private static final String VOD_PROVIDERS_DURATION = "TVOD1=7200,SVOD2=3600,VID3000=2400";

    MapDriver<Object, Text, Text, Text> bestchannelsDriver;
    private final ByteArrayOutputStream errContent = new ByteArrayOutputStream();

    @Before
    public void setUp() {

        BasicConfigurator.configure();
        Logger.getRootLogger().setLevel(Level.ERROR);

        BestChannelsProvidersMapper bestchannelsMapper = new BestChannelsProvidersMapper();
        bestchannelsDriver = MapDriver.newMapDriver(bestchannelsMapper);
        bestchannelsDriver.getConfiguration().set(MainConcat.PROCESS_DAY, PROCESS_DAY_VALUE);

        setUpStreams();
    }

    public void setUpStreams() {
        System.setErr(new PrintStream(this.errContent));
        bestchannelsDriver.resetOutput();
    }

    @After
    public final void cleanUpStreams() {
        System.setErr(null);
    }

    @Test
    public final void testBestLiveChannelsTooShort() throws IOException {
        bestchannelsDriver.withInput(new LongWritable(),
                new Text(AID + TAB + TIMESLOT_OK + TAB + LIVE));

        bestchannelsDriver.withCounter(Counters.BAD_FORMAT, 1);
        bestchannelsDriver.runTest();
    }

    @Test
    public final void testBestOdProvidersTooShort() throws IOException {
        bestchannelsDriver.withInput(new LongWritable(),
                new Text(AID + TAB + TIMESLOT_OK + TAB + VOD));

        bestchannelsDriver.withCounter(Counters.BAD_FORMAT, 1);
        bestchannelsDriver.runTest();
    }

    @Test
    public final void testBestLiveChannelsTimeslotOK() throws IOException {
        bestchannelsDriver.withInput(new LongWritable(),
                new Text(AID + TAB + TIMESLOT_OK + TAB + LIVE + TAB + LIVE_CHANNELS_DURATION));

        bestchannelsDriver.withOutput(new Text(AID + DASH + TIMESLOT_OK),
                new Text(LIVE + TAB + LIVE_CHANNELS_DURATION));
        bestchannelsDriver.withOutput(new Text(OPTOUT_AID + DASH + TIMESLOT_OK),
                new Text(LIVE + TAB + LIVE_CHANNELS_DURATION));

        bestchannelsDriver.runTest();
    }

    @Test
    public final void testBestLiveChannelsTimeslotKO() throws IOException {
        bestchannelsDriver.withInput(new LongWritable(),
                new Text(AID + TAB + TIMESLOT_KO + TAB + LIVE + TAB + LIVE_CHANNELS_DURATION));

        bestchannelsDriver.withCounter(Counters.PROVIDERS_NOT_IN_TARGET_DAY, 1);
        bestchannelsDriver.runTest();
    }

    @Test
    public final void testBestLiveChannelsPeriodOK() throws IOException {
        bestchannelsDriver.withInput(new LongWritable(),
                new Text(AID + TAB + PERIOD_OK + TAB + LIVE + TAB + LIVE_CHANNELS_DURATION));

        bestchannelsDriver.withOutput(new Text(AID + DASH + PERIOD_OK),
                new Text(LIVE + TAB + LIVE_CHANNELS_DURATION));
        bestchannelsDriver.withOutput(new Text(OPTOUT_AID + DASH + PERIOD_OK),
                new Text(LIVE + TAB + LIVE_CHANNELS_DURATION));

        bestchannelsDriver.runTest();
    }

    @Test
    public final void testBestLiveChannelsPeriodKO() throws IOException {
        bestchannelsDriver.withInput(new LongWritable(),
                new Text(AID + TAB + PERIOD_KO + TAB + LIVE + TAB + LIVE_CHANNELS_DURATION));

        bestchannelsDriver.withCounter(Counters.PROVIDERS_NOT_IN_TARGET_DAY, 1);
        bestchannelsDriver.runTest();
    }

    @Test
    public final void testBestLiveChannelsDayOK() throws IOException {
        bestchannelsDriver.withInput(new LongWritable(),
                new Text(AID + TAB + DAY_OK + TAB + LIVE + TAB + LIVE_CHANNELS_DURATION));

        bestchannelsDriver.withOutput(new Text(AID + DASH + DAY_OK),
                new Text(LIVE + TAB + LIVE_CHANNELS_DURATION));
        bestchannelsDriver.withOutput(new Text(OPTOUT_AID + DASH + DAY_OK),
                new Text(LIVE + TAB + LIVE_CHANNELS_DURATION));

        bestchannelsDriver.runTest();
    }

    @Test
    public final void testBestLiveChannelsDayKO() throws IOException {
        bestchannelsDriver.withInput(new LongWritable(),
                new Text(AID + TAB + DAY_KO + TAB + LIVE + TAB + LIVE_CHANNELS_DURATION));

        bestchannelsDriver.withCounter(Counters.PROVIDERS_NOT_IN_TARGET_DAY, 1);
        bestchannelsDriver.runTest();
    }

    @Test
    public final void testBestLiveChannelsWeek() throws IOException {
        bestchannelsDriver.withInput(new LongWritable(),
                new Text(AID + TAB + WEEK_OK + TAB + LIVE + TAB + LIVE_CHANNELS_DURATION));

        bestchannelsDriver.withOutput(new Text(AID + DASH + WEEK_OK),
                new Text(LIVE + TAB + LIVE_CHANNELS_DURATION));
        bestchannelsDriver.withOutput(new Text(OPTOUT_AID + DASH + WEEK_OK),
                new Text(LIVE + TAB + LIVE_CHANNELS_DURATION));

        bestchannelsDriver.runTest();
    }

    @Test
    public final void testBestOdProvidersTimeslotOK() throws IOException {
        bestchannelsDriver.withInput(new LongWritable(),
                new Text(AID + TAB + TIMESLOT_OK + TAB + VOD + TAB + VOD_PROVIDERS_DURATION));

        bestchannelsDriver.withOutput(new Text(AID + DASH + TIMESLOT_OK),
                new Text(VOD + TAB + VOD_PROVIDERS_DURATION));
        bestchannelsDriver.withOutput(new Text(OPTOUT_AID + DASH + TIMESLOT_OK),
                new Text(VOD + TAB + VOD_PROVIDERS_DURATION));

        bestchannelsDriver.runTest();
    }

    @Test
    public final void testBestOdProvidersTimeslotKO() throws IOException {
        bestchannelsDriver.withInput(new LongWritable(),
                new Text(AID + TAB + TIMESLOT_KO + TAB + VOD + TAB + VOD_PROVIDERS_DURATION));

        bestchannelsDriver.withCounter(Counters.PROVIDERS_NOT_IN_TARGET_DAY, 1);
        bestchannelsDriver.runTest();
    }

    @Test
    public final void testBestOdProvidersPeriodOK() throws IOException {
        bestchannelsDriver.withInput(new LongWritable(),
                new Text(AID + TAB + PERIOD_OK + TAB + VOD + TAB + VOD_PROVIDERS_DURATION));

        bestchannelsDriver.withOutput(new Text(AID + DASH + PERIOD_OK),
                new Text(VOD + TAB + VOD_PROVIDERS_DURATION));
        bestchannelsDriver.withOutput(new Text(OPTOUT_AID + DASH + PERIOD_OK),
                new Text(VOD + TAB + VOD_PROVIDERS_DURATION));

        bestchannelsDriver.runTest();
    }

    @Test
    public final void testBestOdProvidersPeriodKO() throws IOException {
        bestchannelsDriver.withInput(new LongWritable(),
                new Text(AID + TAB + PERIOD_KO + TAB + VOD + TAB + VOD_PROVIDERS_DURATION));

        bestchannelsDriver.withCounter(Counters.PROVIDERS_NOT_IN_TARGET_DAY, 1);
        bestchannelsDriver.runTest();
    }

    @Test
    public final void testBestOdProvidersDayOK() throws IOException {
        bestchannelsDriver.withInput(new LongWritable(),
                new Text(AID + TAB + DAY_OK + TAB + VOD + TAB + VOD_PROVIDERS_DURATION));

        bestchannelsDriver.withOutput(new Text(AID + DASH + DAY_OK),
                new Text(VOD + TAB + VOD_PROVIDERS_DURATION));
        bestchannelsDriver.withOutput(new Text(OPTOUT_AID + DASH + DAY_OK),
                new Text(VOD + TAB + VOD_PROVIDERS_DURATION));

        bestchannelsDriver.runTest();
    }

    @Test
    public final void testBestOdProvidersDayKO() throws IOException {
        bestchannelsDriver.withInput(new LongWritable(),
                new Text(AID + TAB + DAY_KO + TAB + VOD + TAB + VOD_PROVIDERS_DURATION));

        bestchannelsDriver.withCounter(Counters.PROVIDERS_NOT_IN_TARGET_DAY, 1);
        bestchannelsDriver.runTest();
    }

    @Test
    public final void testBestOdProvidersWeek() throws IOException {
        bestchannelsDriver.withInput(new LongWritable(),
                new Text(AID + TAB + WEEK_OK + TAB + VOD + TAB + VOD_PROVIDERS_DURATION));

        bestchannelsDriver.withOutput(new Text(AID + DASH + WEEK_OK),
                new Text(VOD + TAB + VOD_PROVIDERS_DURATION));
        bestchannelsDriver.withOutput(new Text(OPTOUT_AID + DASH + WEEK_OK),
                new Text(VOD + TAB + VOD_PROVIDERS_DURATION));

        bestchannelsDriver.runTest();
    }

    @Test
    public final void testBestAnythingTimeslotOK() throws IOException {
        bestchannelsDriver.withInput(new LongWritable(),
                new Text(AID + TAB + TIMESLOT_OK + TAB + "ANY" + TAB + VOD_PROVIDERS_DURATION));

        bestchannelsDriver.withOutput(new Text(AID + DASH + TIMESLOT_OK),
                new Text("ANY" + TAB + VOD_PROVIDERS_DURATION));
        bestchannelsDriver.withOutput(new Text(OPTOUT_AID + DASH + TIMESLOT_OK),
                new Text("ANY" + TAB + VOD_PROVIDERS_DURATION));

        bestchannelsDriver.runTest();
    }

    @Test
    public final void testBestAnythingTimeslotKO() throws IOException {
        bestchannelsDriver.withInput(new LongWritable(),
                new Text(AID + TAB + TIMESLOT_KO + TAB + "ANY" + TAB + VOD_PROVIDERS_DURATION));

        bestchannelsDriver.withCounter(Counters.PROVIDERS_NOT_IN_TARGET_DAY, 1);
        bestchannelsDriver.runTest();
    }
}
