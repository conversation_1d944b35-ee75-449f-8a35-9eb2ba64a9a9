package com.orange.profiling.ute.ravenne.ponderation;

import static org.junit.Assert.*;

import java.util.Arrays;
import java.util.Collection;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.Parameterized;

@RunWith(Parameterized.class)
public class PonderationFunctionTest {
    @Parameterized.Parameter(0)
    public Integer zapDuration;
    @Parameterized.Parameter(1)
    public Integer nbDays;
    @Parameterized.Parameter(2)
    public Long expectedWeight;

    /** Generates Test Data
    *
    * @return data to use in test
    */
    @Parameterized.Parameters(name = "{index}: PonderationFunction for zapDuration = {0} seconds and {1} days after view should return weight = {2}")
    public static Collection<Object[]> data() {
        Object[][] data = new Object[][]{
            {   60,  1,  98L }, {   60, 14,  80L }, {   60, 28,  66L }, {   60, 42,  55L }, {   60, 56,  46L }, {   60, 70,  39L }, {   60, 84,  33L },
            {  300,  1, 228L }, {  300, 14, 187L }, {  300, 28, 153L }, {  300, 42, 127L }, {  300, 56, 107L }, {  300, 70,  91L }, {  300, 84,  77L },
            {  900,  1, 384L }, {  900, 14, 314L }, {  900, 28, 257L }, {  900, 42, 214L }, {  900, 56, 180L }, {  900, 70, 152L }, {  900, 84, 130L },
            { 1800,  1, 483L }, { 1800, 14, 394L }, { 1800, 28, 323L }, { 1800, 42, 268L }, { 1800, 56, 226L }, { 1800, 70, 191L }, { 1800, 84, 164L },
            { 3600,  1, 581L }, { 3600, 14, 474L }, { 3600, 28, 389L }, { 3600, 42, 323L }, { 3600, 56, 272L }, { 3600, 70, 230L }, { 3600, 84, 197L },
            { 5400,  1, 639L }, { 5400, 14, 521L }, { 5400, 28, 427L }, { 5400, 42, 355L }, { 5400, 56, 299L }, { 5400, 70, 253L }, { 5400, 84, 216L },
            { 7200,  1, 679L }, { 7200, 14, 555L }, { 7200, 28, 454L }, { 7200, 42, 378L }, { 7200, 56, 318L }, { 7200, 70, 269L }, { 7200, 84, 230L },
            { 9000,  1, 711L }, { 9000, 14, 581L }, { 9000, 28, 476L }, { 9000, 42, 395L }, { 9000, 56, 332L }, { 9000, 70, 282L }, { 9000, 84, 241L },
            {  300,  0, 228L }, { 1800, -1, 164L }, { 5400, 85, 216L }
        };
        return Arrays.asList(data);

    }

    /** Test PonderationFunction.weight method with all given date
     *
     */
    @Test
    public void testPonderationFunctionWeight() {
        long actualWeight = PonderationFunction.weight(zapDuration.intValue(), nbDays.intValue());

        assertEquals(expectedWeight.longValue(), actualWeight);
    }

}
