package com.orange.profiling.ute.ravenne.scoresmarkersprofiles;

import com.orange.profiling.common.exceptions.FailedJobException;
import com.orange.profiling.common.utils.unit_tests.TestUtils;
import org.apache.log4j.BasicConfigurator;
import org.apache.log4j.Level;
import org.apache.log4j.Logger;
import org.junit.Before;
import org.junit.Test;

import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.fail;

/**
 * <AUTHOR>
 *
 */
public class MainScoreMarkersProfilesTest {

    private URI runtimeRessourceFolder;

    @Before
    public void setUp() throws URISyntaxException {
        runtimeRessourceFolder = MainScoreMarkersProfilesTest.class.getResource("/ute-ravenne-scoremarkersprofiles/").toURI();

        BasicConfigurator.configure();
        Logger.getRootLogger().setLevel(Level.ERROR);
    }

    @Test
    public void testMainKo() {

        String[] args = new String[1];
        args[0] = "";
        try {
            MainScoreMarkersProfiles.main(args);
            fail("Should have thrown an IllegalArgumentException because no parameters");
        }
        catch (Exception e) {
            assertEquals(
                    "Takes 3 arguments: profilPath markerConceptsPath outputPath",
                    e.getMessage());
        }
    }

    @Test
    public final void testMainOk()
            throws ClassNotFoundException, IOException, InterruptedException, FailedJobException {

        final String markersConcepts = runtimeRessourceFolder.resolve("in/marConcepts/*").getPath();
        final String profils = runtimeRessourceFolder.resolve("in/profiles/*").getPath();
        final String output = runtimeRessourceFolder.resolve("output/").getPath();

        String[] args = new String[3];
        args[0] = markersConcepts;
        args[1] = profils;
        args[2] = output;

        final String expectedFile = runtimeRessourceFolder.resolve("expected").getPath();

        MainScoreMarkersProfiles.main(args);

            List<String> expected = TestUtils.getLinesFromPath(expectedFile);
            List<String> actual = TestUtils.getLinesFromPath(output , "part*");
            assertEquals("test markers scores", expected, actual);


    }
    
}