package com.orange.profiling.ute.ravenne.stat.countconcepts;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.PrintStream;
import java.util.ArrayList;
import java.util.List;

import org.apache.hadoop.io.Text;
import org.apache.hadoop.mrunit.mapreduce.ReduceDriver;
import org.apache.log4j.BasicConfigurator;
import org.apache.log4j.Level;
import org.apache.log4j.Logger;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import com.orange.profiling.common.utils.FieldsUtils;

public class CountConceptCombinerTest {
    private static final String TAB = FieldsUtils.TAB;
    private static final String PIPE = FieldsUtils.PIPE;
    private static final String OUT_ID_TODAY = "T";
    private static final String OUT_ID_YESTERDAY = "Y";

    ReduceDriver<Text, Text, Text, Text> reduceDriver;
    private final ByteArrayOutputStream errContent = new ByteArrayOutputStream();

    @Before
    public void setUp() {

        CountConceptCombiner reducer = new CountConceptCombiner();
        reduceDriver = ReduceDriver.newReduceDriver(reducer);

        BasicConfigurator.configure();
        Logger.getRootLogger().setLevel(Level.ERROR);
    }

    @Before
    public void setUpStreams() {
        System.setErr(new PrintStream(this.errContent));
    }

    @After
    public final void cleanUpStreams() {
        System.setErr(null);
    }

    @Test
    public final void testSimpleReducer() throws IOException, InterruptedException, ClassNotFoundException {

        String timebox = "dt";
        String concept = "sujet/test/reducer";

        List<Text> values = new ArrayList<Text>();
        values.add(new Text(OUT_ID_TODAY+TAB+"500"));
        values.add(new Text(OUT_ID_TODAY+TAB+"300"));
        values.add(new Text(OUT_ID_TODAY+TAB+"200"));
        values.add(new Text(OUT_ID_TODAY+TAB+"250"));
        values.add(new Text(OUT_ID_YESTERDAY+TAB+"1000"));

        Text key = new Text(timebox+PIPE+concept);
        reduceDriver.withInput(key, values);

        reduceDriver.withOutput(key,
                new Text(OUT_ID_TODAY+TAB+"1250"));
        reduceDriver.withOutput(key,
                new Text(OUT_ID_YESTERDAY+TAB+"1000"));

        reduceDriver.runTest();
    }

    @Test
    public final void testReducerZeroTodayZeroYesterday() throws IOException, InterruptedException, ClassNotFoundException {

        String timebox = "dt";
        String concept = "sujet/test/reducer";

        List<Text> values = new ArrayList<Text>();
        values.add(new Text(OUT_ID_TODAY+TAB+"0"));
        values.add(new Text(OUT_ID_YESTERDAY+TAB+"0"));

        Text key = new Text(timebox+PIPE+concept);
        reduceDriver.withInput(key, values);

        reduceDriver.withOutput(key,
                new Text(OUT_ID_YESTERDAY+TAB+"0"));

        reduceDriver.runTest();
    }

    @Test
    public final void testReducerEmptyTodayZeroYesterday() throws IOException, InterruptedException, ClassNotFoundException {

        String timebox = "dt";
        String concept = "sujet/test/reducer";

        List<Text> values = new ArrayList<Text>();
        values.add(new Text(OUT_ID_YESTERDAY+TAB+"0"));

        Text key = new Text(timebox+PIPE+concept);
        reduceDriver.withInput(key, values);

        reduceDriver.withOutput(key,
                new Text(OUT_ID_YESTERDAY+TAB+"0"));

        reduceDriver.runTest();
    }

    @Test
    public final void testReducerNewTodayEmptyYesterday() throws IOException, InterruptedException, ClassNotFoundException {

        String timebox = "dt";
        String concept = "sujet/test/reducer";

        List<Text> values = new ArrayList<Text>();
        values.add(new Text(OUT_ID_TODAY+TAB+"500"));
        values.add(new Text(OUT_ID_TODAY+TAB+"300"));
        values.add(new Text(OUT_ID_TODAY+TAB+"200"));

        Text key = new Text(timebox+PIPE+concept);
        reduceDriver.withInput(key, values);

        reduceDriver.withOutput(key,
                new Text(OUT_ID_TODAY+TAB+"1000"));

        reduceDriver.runTest();

    }

    @Test
    public final void testReducerEmptyTodayExistingYesterday() throws IOException, InterruptedException, ClassNotFoundException {

        String timebox = "dt";
        String concept = "sujet/test/reducer";

        List<Text> values = new ArrayList<Text>();
        values.add(new Text(OUT_ID_YESTERDAY+TAB+"1000"));

        Text key = new Text(timebox+PIPE+concept);
        reduceDriver.withInput(key, values);

        reduceDriver.withOutput(key,
                new Text(OUT_ID_YESTERDAY+TAB+"1000"));

        reduceDriver.runTest();
    }

    @Test
    public final void testReducerBadNumber() throws IOException, InterruptedException, ClassNotFoundException {

        String timebox = "dt";
        String concept = "sujet/test/reducer";

        List<Text> values = new ArrayList<Text>();
        values.add(new Text(OUT_ID_TODAY+TAB+"500"));
        values.add(new Text(OUT_ID_TODAY+TAB+"300"));
        values.add(new Text(OUT_ID_TODAY+TAB+"200"));
        values.add(new Text(OUT_ID_TODAY+TAB+"A"));

        Text key = new Text(timebox+PIPE+concept);
        reduceDriver.withInput(key, values);

        reduceDriver.withOutput(key,
                new Text(OUT_ID_TODAY+TAB+"1000"));

        reduceDriver.runTest();
    }

    @Test
    public final void testReducerBadIdOut() throws IOException, InterruptedException, ClassNotFoundException {

        String timebox = "dt";
        String concept = "sujet/test/reducer";

        List<Text> values = new ArrayList<Text>();
        values.add(new Text("W"+TAB+"2000"));

        reduceDriver.withInput(new Text(timebox+PIPE+concept), values);

        reduceDriver.runTest();
    }

    @Test
    public final void testReducerBadFormat() throws IOException, InterruptedException, ClassNotFoundException {

        String timebox = "dt";
        String concept = "sujet/test/reducer";

        List<Text> values = new ArrayList<Text>();
        values.add(new Text(OUT_ID_TODAY));

        Text key = new Text(timebox+PIPE+concept);
        reduceDriver.withInput(key, values);

        reduceDriver.runTest();
    }


    @Test
    public final void testReducerBadKeyFormat() throws IOException, InterruptedException, ClassNotFoundException {

        String timebox = "dt";
        String concept = "sujet/test/reducer";

        List<Text> values = new ArrayList<Text>();
        values.add(new Text(OUT_ID_TODAY+TAB+"100"));

        // No check is made on key format in combiner as we reuse the key as is (and it should not be badly formated)
        Text key = new Text(timebox+"-"+concept);
        reduceDriver.withInput(key, values);
        reduceDriver.withOutput(key,
                new Text(OUT_ID_TODAY+TAB+"100"));
        reduceDriver.runTest();
    }

}
