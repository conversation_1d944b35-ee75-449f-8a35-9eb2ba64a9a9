package com.orange.profiling.ute.ravenne.genetic.pnssend;

import static org.junit.Assert.*;

import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.Path;
import org.apache.log4j.BasicConfigurator;
import org.apache.log4j.Level;
import org.apache.log4j.Logger;
import org.junit.Before;
import org.junit.Test;

public class FileFilterRavenneGeneticTest {
    FileFilterRavenneGenetic filter;

    @Before
    public void setUp() {

        filter = new FileFilterRavenneGenetic();
        Configuration config = new Configuration();
        config.set(FileFilterRavenneGenetic.MIN_DATE, "20200513");
        config.set(FileFilterRavenneGenetic.MAX_DATE, "20200617");
        config.set(FileFilterRavenneGenetic.FILE_TYPE, "/sent/");
        filter.setConf(config);

        BasicConfigurator.configure();
        Logger.getRootLogger().setLevel(Level.ERROR);
    }

    @Test
    public void testBetwenMinAndMaxGoodDay() {
        String filePath = "/user/profiling-ute/private/generated/ute/ravenne/genetic/20200610/sent/part-m-00001";
        boolean processFile = filter.accept(new Path(filePath));
        assertTrue(processFile);
    }

    @Test
    public void testBetwenMinAndMaxBadDay() {
        String filePath = "/user/profiling-ute/private/generated/ute/ravenne/genetic/20200609/sent/part-m-00001";
        boolean processFile = filter.accept(new Path(filePath));
        assertFalse(processFile);
    }

    @Test
    public void testEqualMin() {
        String filePath = "/user/profiling-ute/private/generated/ute/ravenne/genetic/20200513/sent/part-m-00001";
        boolean processFile = filter.accept(new Path(filePath));
        assertTrue(processFile);
    }

    @Test
    public void testOneDayBeforeMin() {
        String filePath = "/user/profiling-ute/private/generated/ute/ravenne/genetic/20200512/sent/part-m-00001";
        boolean processFile = filter.accept(new Path(filePath));
        assertFalse(processFile);
    }

    @Test
    public void testOneWeekBeforeMin() {
        String filePath = "/user/profiling-ute/private/generated/ute/ravenne/genetic/20200506/sent/part-m-00001";
        boolean processFile = filter.accept(new Path(filePath));
        assertFalse(processFile);
    }

    @Test
    public void testEqualMax() {
        String filePath = "/user/profiling-ute/private/generated/ute/ravenne/genetic/20200617/sent/part-m-00001";
        boolean processFile = filter.accept(new Path(filePath));
        assertTrue(processFile);
    }

    @Test
    public void testOneDayAfterMax() {
        String filePath = "/user/profiling-ute/private/generated/ute/ravenne/genetic/20200618/sent/part-m-00001";
        boolean processFile = filter.accept(new Path(filePath));
        assertFalse(processFile);
    }

    @Test
    public void testWeekDayAfterMax() {
        String filePath = "/user/profiling-ute/private/generated/ute/ravenne/genetic/20200624/sent/part-m-00001";
        boolean processFile = filter.accept(new Path(filePath));
        assertFalse(processFile);
    }

    @Test
    public void testNotFilteredFileType() {
        String filePath = "/user/profiling-ute/private/generated/ute/ravenne/genetic/20200610/send/part-m-00001";
        boolean processFile = filter.accept(new Path(filePath));
        assertTrue(processFile);
    }

    @Test
    public void testNotValidDate() {
        String filePath = "/user/profiling-ute/private/generated/ute/ravenne/genetic/202063/sent/part-m-00001";
        boolean processFile = filter.accept(new Path(filePath));
        assertFalse(processFile);
    }

}
