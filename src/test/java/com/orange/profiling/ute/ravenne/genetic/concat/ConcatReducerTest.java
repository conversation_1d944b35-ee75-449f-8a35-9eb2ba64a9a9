package com.orange.profiling.ute.ravenne.genetic.concat;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.PrintStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import java.util.stream.Collectors;

import org.apache.hadoop.io.NullWritable;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mrunit.mapreduce.ReduceDriver;
import org.apache.log4j.BasicConfigurator;
import org.apache.log4j.Level;
import org.apache.log4j.Logger;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import static org.junit.Assert.assertEquals;

import com.orange.profiling.common.utils.FieldsUtils;
import com.orange.profiling.ute.ravenne.dao.TimeboxZapAggregation;
import com.orange.profiling.ute.utils.AidParser;

public class ConcatReducerTest {
    private static final String DASH = FieldsUtils.DASH;
    private static final String TAB = FieldsUtils.TAB;
    private static final String PIPE = FieldsUtils.PIPE;
    private static final String AID = "123456789";
    private static final String OPTOUT_AID = "999999999";
    private static final String TIMEBOX = "d4t2";
    private static final String JSON_KEY = AID+";;"+TIMEBOX;

    private static final String PROCESS_DATE = "20200522";
    private static final String FRESHNESS_DATE = "1590098400000";
    private static final String EXPIRATION_DATE = "1592690400000";
    private static final String EXPIRATON_OPTOUT_DATE = "1598738400000";

    private ReduceDriver<Text, Text, NullWritable, Text> reduceDriver;
    private final ByteArrayOutputStream errContent = new ByteArrayOutputStream();

    @Before
    public void setUp() {

        BasicConfigurator.configure();
        Logger.getRootLogger().setLevel(Level.ERROR);

        ConcatReducer reducer = new ConcatReducer();
        reduceDriver = ReduceDriver.newReduceDriver(reducer);
        reduceDriver.getConfiguration().set(MainConcat.PROCESS_DATE, PROCESS_DATE);
        reduceDriver.getConfiguration().set(ConcatReducer.CONF_TOP_CONCEPTS, "6");
        reduceDriver.getConfiguration().set(ConcatReducer.CONF_TOP_LIVE_CHANNELS, "3");
        reduceDriver.getConfiguration().set(ConcatReducer.CONF_TOP_OD_PROVIDERS, "3");

        setUpStreams();
    }

    public void setUpStreams() {
        System.setErr(new PrintStream(this.errContent));
        reduceDriver.resetOutput();
    }

    @After
    public final void cleanUpStreams() {
        System.setErr(null);
    }

    @Test
    public void testAggregateOneConceptsWeightWithoutNb() throws IOException {
        Text key = new Text(AID+DASH+TIMEBOX);

        String concepts = "ambiance et ton/humoristique=300,sujet/sport=200,type/live=100";
        long ratioVodLive = 50;
        int nbValues = 1;

        List<Text> values = new ArrayList<Text>();
        values.add(new Text(String.join(TAB, ConceptsWeightsMapper.CONCEPTS_MARK,
                concepts, Long.toString(ratioVodLive))));

        reduceDriver.withInput(key, values);

        Map<String,Long> conceptsWeights = new TreeMap<>();
        long ratioVodLiveExpected = 50;
        conceptsWeights.put("ambiance et ton/humoristique", 300L);
        conceptsWeights.put("sujet/sport", 200L);
        conceptsWeights.put("type/live", 100L);
        List<String> bestLiveChannels = new ArrayList();
        List<String> bestOdProviders = new ArrayList();

        reduceDriver.withOutput(NullWritable.get(),
                new Text(buildValue(conceptsWeights, ratioVodLiveExpected,
                        bestLiveChannels, bestOdProviders, true))
        );

        reduceDriver.runTest();
    }

    @Test
    public void testAggregateOneConceptsWeightWithNb() throws IOException {
        Text key = new Text(AID+DASH+TIMEBOX);

        String concepts = "ambiance et ton/humoristique=300,sujet/sport=200,type/live=100";
        long ratioVodLive = 50;
        int nbValues = 2;

        List<Text> values = new ArrayList<Text>();
        values.add(new Text(String.join(TAB, ConceptsWeightsMapper.CONCEPTS_MARK,
                concepts, Long.toString(ratioVodLive), Integer.toString(nbValues))));

        reduceDriver.withInput(key, values);

        Map<String,Long> conceptsWeights = new TreeMap<>();
        long ratioVodLiveExpected = 25;
        conceptsWeights.put("ambiance et ton/humoristique", 150L);
        conceptsWeights.put("sujet/sport", 100L);
        conceptsWeights.put("type/live", 50L);
        List<String> bestLiveChannels = new ArrayList();
        List<String> bestOdProviders = new ArrayList();

        reduceDriver.withOutput(NullWritable.get(),
                new Text(buildValue(conceptsWeights, ratioVodLiveExpected,
                        bestLiveChannels, bestOdProviders, true))
        );

        reduceDriver.runTest();
    }

    @Test
    public void testAggregateOneConceptsWeightWithBadNb() throws IOException {
        Text key = new Text(AID+DASH+TIMEBOX);

        String concepts = "ambiance et ton/humoristique=300,sujet/sport=200,type/live=100";
        long ratioVodLive = 50;

        List<Text> values = new ArrayList<Text>();
        values.add(new Text(String.join(TAB, ConceptsWeightsMapper.CONCEPTS_MARK,
                concepts, Long.toString(ratioVodLive), "1A")));

        reduceDriver.withInput(key, values);

        Map<String,Long> conceptsWeights = new TreeMap<>();
        long ratioVodLiveExpected = 50;
        conceptsWeights.put("ambiance et ton/humoristique", 300L);
        conceptsWeights.put("sujet/sport", 200L);
        conceptsWeights.put("type/live", 100L);
        List<String> bestLiveChannels = new ArrayList();
        List<String> bestOdProviders = new ArrayList();

        reduceDriver.withOutput(NullWritable.get(),
                new Text(buildValue(conceptsWeights, ratioVodLiveExpected,
                        bestLiveChannels, bestOdProviders, true))
        );

        reduceDriver.runTest();
    }

    @Test
    public void testAggregateThreeConceptsWeight() throws IOException {
        Text key = new Text(AID+DASH+TIMEBOX);

        List<Text> values = new ArrayList<Text>();

        String concepts1 = "ambiance et ton/humoristique=300,sujet/sport=200,type/live=100";
        long ratioVodLive1 = 60;
        int nbValues1 = 2;
        values.add(new Text(String.join(TAB, ConceptsWeightsMapper.CONCEPTS_MARK,
                concepts1, Long.toString(ratioVodLive1), Integer.toString(nbValues1))));

        String concepts2 = "ambiance et ton/suspens=100,sujet/sport=100,type/série=200";
        long ratioVodLive2 = 40;
        int nbValues2 = 1;
        values.add(new Text(String.join(TAB, ConceptsWeightsMapper.CONCEPTS_MARK,
                concepts2, Long.toString(ratioVodLive2), Integer.toString(nbValues2))));

        String concepts3 = "genre/comédie=300,sujet/sport=100,type/film=100";
        long ratioVodLive3 = 80;
        int nbValues3 = 3;
        values.add(new Text(String.join(TAB, ConceptsWeightsMapper.CONCEPTS_MARK,
                concepts3, Long.toString(ratioVodLive3), Integer.toString(nbValues3))));


        String conceptsExpected = "ambiance et ton/humoristique=300,ambiance et ton/suspens=100,"
                + "genre/comédie=300,sujet/sport=400,type/film=100,type/live=100,type/série=200";

        reduceDriver.withInput(key, values);

        Map<String,Long> conceptsWeights = new TreeMap<>();
        long ratioVodLiveExpected = 30;
        conceptsWeights.put("ambiance et ton/humoristique", 50L);
        conceptsWeights.put("ambiance et ton/suspens", 17L);
        conceptsWeights.put("genre/comédie", 50L);
        conceptsWeights.put("sujet/sport", 67L);
        conceptsWeights.put("type/film", 17L);
        conceptsWeights.put("type/série", 33L);
        List<String> bestLiveChannels = new ArrayList();
        List<String> bestOdProviders = new ArrayList();

        reduceDriver.withOutput(NullWritable.get(),
                new Text(buildValue(conceptsWeights, ratioVodLiveExpected,
                        bestLiveChannels, bestOdProviders, true))
        );

        reduceDriver.runTest();
    }

    @Test
    public void testAggregateOneLiveChannelsDurationsWithoutNb() throws IOException {
        Text key = new Text(AID+DASH+TIMEBOX);

        String liveChannels = "1234=300,5678=200,7654=100";
        int nbLives = 1;

        List<Text> values = new ArrayList<Text>();
        values.add(new Text(String.join(TAB, TimeboxZapAggregation.LIVE,
                liveChannels)));

        reduceDriver.withInput(key, values);

        Map<String,Long> conceptsWeights = new TreeMap<>();
        long ratioVodLiveExpected = 0;
        List<String> bestLiveChannels = new ArrayList();
        bestLiveChannels.add("1234");
        bestLiveChannels.add("5678");
        bestLiveChannels.add("7654");
        List<String> bestOdProviders = new ArrayList();

        reduceDriver.withOutput(NullWritable.get(),
                new Text(buildValue(conceptsWeights, ratioVodLiveExpected,
                        bestLiveChannels, bestOdProviders, true))
        );

        reduceDriver.runTest();
    }

    @Test
    public void testAggregateOneLiveChannelsDurationsWithNb() throws IOException {
        Text key = new Text(AID+DASH+TIMEBOX);

        String liveChannels = "1234=300,5678=200,7654=100";
        int nbLives = 2;

        List<Text> values = new ArrayList<Text>();
        values.add(new Text(String.join(TAB, TimeboxZapAggregation.LIVE,
                liveChannels, Integer.toString(nbLives))));

        reduceDriver.withInput(key, values);

        Map<String,Long> conceptsWeights = new TreeMap<>();
        long ratioVodLiveExpected = 0;
        List<String> bestLiveChannels = new ArrayList();
        bestLiveChannels.add("1234");
        bestLiveChannels.add("5678");
        bestLiveChannels.add("7654");
        List<String> bestOdProviders = new ArrayList();

        reduceDriver.withOutput(NullWritable.get(),
                new Text(buildValue(conceptsWeights, ratioVodLiveExpected,
                        bestLiveChannels, bestOdProviders, true))
        );

        reduceDriver.runTest();
    }

    @Test
    public void testAggregateOneLiveChannelsDurationsWithBadNb() throws IOException {
        Text key = new Text(AID+DASH+TIMEBOX);

        String liveChannels = "1234=300,5678=200,7654=100";
        int nbLives = 1;

        List<Text> values = new ArrayList<Text>();
        values.add(new Text(String.join(TAB, TimeboxZapAggregation.LIVE,
                liveChannels, "1A")));

        reduceDriver.withInput(key, values);

        Map<String,Long> conceptsWeights = new TreeMap<>();
        long ratioVodLiveExpected = 0;
        List<String> bestLiveChannels = new ArrayList();
        bestLiveChannels.add("1234");
        bestLiveChannels.add("5678");
        bestLiveChannels.add("7654");
        List<String> bestOdProviders = new ArrayList();

        reduceDriver.withOutput(NullWritable.get(),
                new Text(buildValue(conceptsWeights, ratioVodLiveExpected,
                        bestLiveChannels, bestOdProviders, true))
        );

        reduceDriver.runTest();
    }

    @Test
    public void testAggregateThreeLiveChannelsDurations() throws IOException {
        Text key = new Text(AID+DASH+TIMEBOX);

        List<Text> values = new ArrayList<Text>();

        String liveChannels1 = "1234=300,5678=200,7654=100";
        int nbLives1 = 2;
        values.add(new Text(String.join(TAB, TimeboxZapAggregation.LIVE,
                liveChannels1, Integer.toString(nbLives1))));

        String liveChannels2 = "2222=100,5678=100,9876=200";
        int nbLives2 = 1;
        values.add(new Text(String.join(TAB, TimeboxZapAggregation.LIVE,
                liveChannels2, Integer.toString(nbLives2))));

        String liveChannels3 = "3579=300,5678=100,6543=100";
        int nbLives3 = 3;
        values.add(new Text(String.join(TAB, TimeboxZapAggregation.LIVE,
                liveChannels3, Integer.toString(nbLives3))));


        String liveChannelsExpected = "1234=300,2222=100,"
                + "3579=300,5678=400,6543=100,7654=100,9876=200";
        int nbLivesExpected = 6;

        reduceDriver.withInput(key, values);

        Map<String,Long> conceptsWeights = new TreeMap<>();
        long ratioVodLiveExpected = 0;
        List<String> bestLiveChannels = new ArrayList();
        bestLiveChannels.add("5678");
        bestLiveChannels.add("1234");
        bestLiveChannels.add("3579");
        List<String> bestOdProviders = new ArrayList();

        reduceDriver.withOutput(NullWritable.get(),
                new Text(buildValue(conceptsWeights, ratioVodLiveExpected,
                        bestLiveChannels, bestOdProviders, true))
        );

        reduceDriver.runTest();
    }

    @Test
    public void testAggregateOneOdProvidersDurationsWithoutNb() throws IOException {
        Text key = new Text(AID+DASH+TIMEBOX);

        String odProviders = "FTVOD=300,MYM6=200,TF1REPLAY=100";
        int nbOds = 1;

        List<Text> values = new ArrayList<Text>();
        values.add(new Text(String.join(TAB, TimeboxZapAggregation.VOD,
                odProviders)));

        reduceDriver.withInput(key, values);

        Map<String,Long> conceptsWeights = new TreeMap<>();
        long ratioVodLiveExpected = 0;
        List<String> bestLiveChannels = new ArrayList();
        List<String> bestOdProviders = new ArrayList();
        bestOdProviders.add("FTVOD");
        bestOdProviders.add("MYM6");
        bestOdProviders.add("TF1REPLAY");

        reduceDriver.withOutput(NullWritable.get(),
                new Text(buildValue(conceptsWeights, ratioVodLiveExpected,
                        bestLiveChannels, bestOdProviders, true))
        );

        reduceDriver.runTest();
    }

    @Test
    public void testAggregateOneOdProvidersDurationsWithNb() throws IOException {
        Text key = new Text(AID+DASH+TIMEBOX);

        String odProviders = "FTVOD=300,MYM6=200,TF1REPLAY=100";
        int nbOds = 2;

        List<Text> values = new ArrayList<Text>();
        values.add(new Text(String.join(TAB, TimeboxZapAggregation.VOD,
                odProviders, Integer.toString(nbOds))));

        reduceDriver.withInput(key, values);

        Map<String,Long> conceptsWeights = new TreeMap<>();
        long ratioVodLiveExpected = 0;
        List<String> bestLiveChannels = new ArrayList();
        List<String> bestOdProviders = new ArrayList();
        bestOdProviders.add("FTVOD");
        bestOdProviders.add("MYM6");
        bestOdProviders.add("TF1REPLAY");

        reduceDriver.withOutput(NullWritable.get(),
                new Text(buildValue(conceptsWeights, ratioVodLiveExpected,
                        bestLiveChannels, bestOdProviders, true))
        );

        reduceDriver.runTest();
    }

    @Test
    public void testAggregateOneOdProvidersDurationsWithBadNb() throws IOException {
        Text key = new Text(AID+DASH+TIMEBOX);

        String odProviders = "FTVOD=300,MYM6=200,TF1REPLAY=100";
        int nbOds = 1;

        List<Text> values = new ArrayList<Text>();
        values.add(new Text(String.join(TAB, TimeboxZapAggregation.VOD,
                odProviders, "1A")));

        reduceDriver.withInput(key, values);

        Map<String,Long> conceptsWeights = new TreeMap<>();
        long ratioVodLiveExpected = 0;
        List<String> bestLiveChannels = new ArrayList();
        List<String> bestOdProviders = new ArrayList();
        bestOdProviders.add("FTVOD");
        bestOdProviders.add("MYM6");
        bestOdProviders.add("TF1REPLAY");

        reduceDriver.withOutput(NullWritable.get(),
                new Text(buildValue(conceptsWeights, ratioVodLiveExpected,
                        bestLiveChannels, bestOdProviders, true))
        );

        reduceDriver.runTest();
    }

    @Test
    public void testAggregateThreeOdProvidersDurations() throws IOException {


        Text key = new Text(AID+DASH+"d4od");
        List<Text> values = new ArrayList<Text>();

        String odProviders1 = "FTVOD=300,MYM6=200,TF1REPLAY=100";
        int nbOds1 = 2;
        values.add(new Text(String.join(TAB, TimeboxZapAggregation.VOD,
                odProviders1, Integer.toString(nbOds1))));

        String odProviders2 = "GVU=100,MYM6=100,VOD24=200";
        int nbOds2 = 1;
        values.add(new Text(String.join(TAB, TimeboxZapAggregation.VOD,
                odProviders2, Integer.toString(nbOds2))));

        String odProviders3 = "ISEE=300,MYM6=100,SVOD=100";
        int nbOds3 = 3;
        values.add(new Text(String.join(TAB, TimeboxZapAggregation.VOD,
                odProviders3, Integer.toString(nbOds3))));

        reduceDriver.withInput(key, values);
        reduceDriver.withOutput(NullWritable.get(), new Text("123456789;;d4od|IA|TimeSlots|{\"th_rav\":[],\"th_ch_catchup\":[\"MYM6\",\"FTVOD\",\"ISEE\"]}|1592690400000|1590098400000"));

        reduceDriver.runTest();
    }


    /**
     * The goal of this test is to verify we add different information in the same zapAggregation
     * @throws IOException
     */
    @Test
    public void testAggregateDifferent() throws IOException {
        Text key = new Text(AID+DASH+TIMEBOX);

        List<Text> values = new ArrayList<Text>();

        String concepts1 = "ambiance et ton/humoristique=300,sujet/sport=200,type/live=100";
        long ratioVodLive1 = 60;
        int nbValues1 = 2;
        values.add(new Text(String.join(TAB, ConceptsWeightsMapper.CONCEPTS_MARK,
                concepts1, Long.toString(ratioVodLive1), Integer.toString(nbValues1))));

        String concepts2 = "ambiance et ton/suspens=100,sujet/sport=100,type/série=200";
        long ratioVodLive2 = 40;
        int nbValues2 = 1;
        values.add(new Text(String.join(TAB, ConceptsWeightsMapper.CONCEPTS_MARK,
                concepts2, Long.toString(ratioVodLive2), Integer.toString(nbValues2))));

        String concepts3 = "genre/comédie=300,sujet/sport=100,type/film=100";
        long ratioVodLive3 = 80;
        int nbValues3 = 3;
        values.add(new Text(String.join(TAB, ConceptsWeightsMapper.CONCEPTS_MARK,
                concepts3, Long.toString(ratioVodLive3), Integer.toString(nbValues3))));


        String liveChannels1 = "1234=300,5678=200,7654=100";
        int nbLives1 = 2;
        values.add(new Text(String.join(TAB, TimeboxZapAggregation.LIVE,
                liveChannels1, Integer.toString(nbLives1))));

        String liveChannels2 = "2222=100,5678=100,9876=200";
        int nbLives2 = 1;
        values.add(new Text(String.join(TAB, TimeboxZapAggregation.LIVE,
                liveChannels2, Integer.toString(nbLives2))));

        String liveChannels3 = "3579=300,5678=100,6543=100";
        int nbLives3 = 3;
        values.add(new Text(String.join(TAB, TimeboxZapAggregation.LIVE,
                liveChannels3, Integer.toString(nbLives3))));


        String odProviders1 = "FTVOD=300,MYM6=200,TF1REPLAY=100";
        int nbOds1 = 2;
        values.add(new Text(String.join(TAB, TimeboxZapAggregation.VOD,
                odProviders1, Integer.toString(nbOds1))));

        String odProviders2 = "GVU=100,MYM6=100,VOD24=200";
        int nbOds2 = 1;
        values.add(new Text(String.join(TAB, TimeboxZapAggregation.VOD,
                odProviders2, Integer.toString(nbOds2))));

        String odProviders3 = "ISEE=300,MYM6=100,SVOD=100";
        int nbOds3 = 3;
        values.add(new Text(String.join(TAB, TimeboxZapAggregation.VOD,
                odProviders3, Integer.toString(nbOds3))));

        reduceDriver.withInput(key, values);

        Map<String,Long> conceptsWeights = new TreeMap<>();
        long ratioVodLiveExpected = 30L;
        conceptsWeights.put("ambiance et ton/humoristique", 50L);
        conceptsWeights.put("ambiance et ton/suspens", 17L);
        conceptsWeights.put("genre/comédie", 50L);
        conceptsWeights.put("sujet/sport", 67L);
        conceptsWeights.put("type/film", 17L);
        conceptsWeights.put("type/série", 33L);
        List<String> bestLiveChannels = new ArrayList();
        bestLiveChannels.add("5678");
        bestLiveChannels.add("1234");
        bestLiveChannels.add("3579");
        List<String> bestOdProviders = new ArrayList();
        bestOdProviders.add("MYM6");
        bestOdProviders.add("FTVOD");
        bestOdProviders.add("ISEE");

        reduceDriver.withOutput(NullWritable.get(),
                new Text(buildValue(conceptsWeights, ratioVodLiveExpected,
                        bestLiveChannels, bestOdProviders, true))
        );

        reduceDriver.runTest();
    }

    /**
     * Test with KEY = OPTOUT_AID : expiration Date change
     * @throws IOException
     */
    @Test
    public void testAggregateOptout() throws IOException {
        Text key = new Text(OPTOUT_AID+DASH+TIMEBOX);

        List<Text> values = new ArrayList<Text>();

        String concepts1 = "ambiance et ton/humoristique=300,sujet/sport=200,type/live=100";
        long ratioVodLive1 = 60;
        int nbValues1 = 2;
        values.add(new Text(String.join(TAB, ConceptsWeightsMapper.CONCEPTS_MARK,
                concepts1, Long.toString(ratioVodLive1), Integer.toString(nbValues1))));

        String concepts2 = "ambiance et ton/suspens=100,sujet/sport=100,type/série=200";
        long ratioVodLive2 = 40;
        int nbValues2 = 1;
        values.add(new Text(String.join(TAB, ConceptsWeightsMapper.CONCEPTS_MARK,
                concepts2, Long.toString(ratioVodLive2), Integer.toString(nbValues2))));

        String concepts3 = "genre/comédie=300,sujet/sport=100,type/film=100";
        long ratioVodLive3 = 80;
        int nbValues3 = 3;
        values.add(new Text(String.join(TAB, ConceptsWeightsMapper.CONCEPTS_MARK,
                concepts3, Long.toString(ratioVodLive3), Integer.toString(nbValues3))));


        String liveChannels1 = "1234=300,5678=200,7654=100";
        int nbLives1 = 2;
        values.add(new Text(String.join(TAB, TimeboxZapAggregation.LIVE,
                liveChannels1, Integer.toString(nbLives1))));

        String liveChannels2 = "2222=100,5678=100,9876=200";
        int nbLives2 = 1;
        values.add(new Text(String.join(TAB, TimeboxZapAggregation.LIVE,
                liveChannels2, Integer.toString(nbLives2))));

        String liveChannels3 = "3579=300,5678=100,6543=100";
        int nbLives3 = 3;
        values.add(new Text(String.join(TAB, TimeboxZapAggregation.LIVE,
                liveChannels3, Integer.toString(nbLives3))));


        String odProviders1 = "FTVOD=300,MYM6=200,TF1REPLAY=100";
        int nbOds1 = 2;
        values.add(new Text(String.join(TAB, TimeboxZapAggregation.VOD,
                odProviders1, Integer.toString(nbOds1))));

        String odProviders2 = "GVU=100,MYM6=100,VOD24=200";
        int nbOds2 = 1;
        values.add(new Text(String.join(TAB, TimeboxZapAggregation.VOD,
                odProviders2, Integer.toString(nbOds2))));

        String odProviders3 = "ISEE=300,MYM6=100,SVOD=100";
        int nbOds3 = 3;
        values.add(new Text(String.join(TAB, TimeboxZapAggregation.VOD,
                odProviders3, Integer.toString(nbOds3))));

        reduceDriver.withInput(key, values);

        Map<String,Long> conceptsWeights = new TreeMap<>();
        long ratioVodLiveExpected = 30L;
        conceptsWeights.put("ambiance et ton/humoristique", 50L);
        conceptsWeights.put("ambiance et ton/suspens", 17L);
        conceptsWeights.put("genre/comédie", 50L);
        conceptsWeights.put("sujet/sport", 67L);
        conceptsWeights.put("type/film", 17L);
        conceptsWeights.put("type/série", 33L);
        List<String> bestLiveChannels = new ArrayList();
        bestLiveChannels.add("5678");
        bestLiveChannels.add("1234");
        bestLiveChannels.add("3579");
        List<String> bestOdProviders = new ArrayList();
        bestOdProviders.add("MYM6");
        bestOdProviders.add("FTVOD");
        bestOdProviders.add("ISEE");

        reduceDriver.withOutput(NullWritable.get(),
                new Text(buildOptoutValue(conceptsWeights, ratioVodLiveExpected,
                        bestLiveChannels, bestOdProviders))
        );

        reduceDriver.runTest();
    }

    @Test
    public void testAggregateBadMark() throws IOException {
        Text key = new Text(AID+DASH+TIMEBOX);

        String val = "FTVOD=300,sujet/sport=200,1234=100";
        int nb = 1;

        List<Text> values = new ArrayList<Text>();
        values.add(new Text(String.join(TAB, "ANY",
                val, "42")));

        reduceDriver.withInput(key, values);

        Map<String,Long> conceptsWeights = new TreeMap<>();
        long ratioVodLiveExpected = 0L;
        List<String> bestLiveChannels = new ArrayList();
        List<String> bestOdProviders = new ArrayList();

        reduceDriver.withOutput(NullWritable.get(),
                new Text(buildValue(conceptsWeights, ratioVodLiveExpected,
                        bestLiveChannels, bestOdProviders, true))
        );

        reduceDriver.runTest();
    }

    private String buildValue(Map<String,Long> conceptsWeights, long ratioVodLive,
            List<String> bestLiveChannels, List<String> bestOdProviders, boolean isLive) {



        if(isLive){
            return String.join(PIPE, JSON_KEY, "IA", "TimeSlots",
                    buildJsonLive(conceptsWeights, ratioVodLive, bestLiveChannels),
                    EXPIRATION_DATE, FRESHNESS_DATE);
        }else {
            return String.join(PIPE, JSON_KEY, "IA", "TimeSlots",
                    buildJsonLive(conceptsWeights, ratioVodLive, bestLiveChannels),
                    EXPIRATION_DATE, FRESHNESS_DATE);
        }


    }

    private String buildOptoutValue(Map<String,Long> conceptsWeights, long ratioVodLive,
            List<String> bestLiveChannels, List<String> bestOdProviders) {

        return String.join(PIPE, OPTOUT_AID+";;"+TIMEBOX, "IA", "TimeSlots",
                buildJsonLive(conceptsWeights, ratioVodLive, bestLiveChannels),
                EXPIRATON_OPTOUT_DATE, FRESHNESS_DATE);
    }

    private String buildJsonLive(Map<String,Long> conceptsWeights, long ratioVodLive, List<String> bestLiveChannels) {

        StringBuilder sb = new StringBuilder();
        sb.append("{");
        sb.append("\"th_rav\":");
        sb.append(buildConceptsWeights(conceptsWeights));
        sb.append(",\"th_ch\":");
        sb.append(buildList(bestLiveChannels));
        sb.append(",\"th_rvl\":"+ratioVodLive);
        sb.append("}");
        return sb.toString();
    }

    private String buildJsonVod(Map<String,Long> conceptsWeights, List<String> bestOdProviders) {

        StringBuilder sb = new StringBuilder();
        sb.append("{");
        sb.append("\"th_rav\":");
        sb.append(buildConceptsWeights(conceptsWeights));
        sb.append(",\"th_ch_catchup\":");
        sb.append(buildList(bestOdProviders));
        sb.append("}");
        return sb.toString();
    }

    private String buildConceptsWeights(Map<String, Long> conceptsWeights) {
        StringBuilder sb = new StringBuilder("[");
        boolean first = true;
        for(Map.Entry<String,Long> conceptWeight: conceptsWeights.entrySet()) {
            if (first) {
                first = false;
            }
            else {
                sb.append(",");
            }
            sb.append("{\"concept\":\""+conceptWeight.getKey()+"\",\"weight\":\""+conceptWeight.getValue()+"\"}");
        }
        sb.append("]");
        return sb.toString();
    }

    private String buildList(List<String> liste) {
        if (liste.isEmpty()) {
            return "[]";
        }
        StringBuilder sb = new StringBuilder();
        sb.append("[");
        for (int i = 0; i < liste.size(); i++) {
            if (i > 0) {
                sb.append(",");
            }
            sb.append("\"").append(liste.get(i)).append("\"");
        }
        sb.append("]");
        return sb.toString();
    }
    @Test
    public void testConcatHelperWithOttAid() throws IOException, InterruptedException {
        // Test que ConcatHelper gère correctement les AID composites OTT
        ConcatHelper helper = new ConcatHelper();
        Text key = new Text("12345678-web-d1t1");

        helper.initHelper(key);

        // Vérifier que l'AID et le timebox sont correctement parsés
        assertEquals("12345678-web", helper.getAid());
        assertEquals("d1t1", helper.getTimebox());
    }

    @Test
    public void testConcatHelperWithStbAid() throws IOException, InterruptedException {
        // Test que ConcatHelper gère correctement les AID composites STB
        ConcatHelper helper = new ConcatHelper();
        Text key = new Text("12345678-stb-d1t1");

        helper.initHelper(key);

        // Vérifier que l'AID et le timebox sont correctement parsés
        assertEquals("12345678-stb", helper.getAid());
        assertEquals("d1t1", helper.getTimebox());
    }

    @Test
    public void testConcatHelperWithSimpleAid() throws IOException, InterruptedException {
        // Test que ConcatHelper gère correctement les AID simples (format existant)
        ConcatHelper helper = new ConcatHelper();
        Text key = new Text("123456789-d4t2");

        helper.initHelper(key);

        // Vérifier que l'AID et le timebox sont correctement parsés
        assertEquals("123456789", helper.getAid());
        assertEquals("d4t2", helper.getTimebox());
    }

    @Test
    public void testAidParserLogic() throws IOException, InterruptedException {
        // Test de la logique AidParser pour différents types d'AID

        // AID simple - doit être traité comme STB par défaut
        AidParser simpleAid = new AidParser("123456789");
        assertEquals(false, simpleAid.isStb());
        assertEquals(false, simpleAid.isOtt());
        assertEquals(false, simpleAid.isValidFormat());
        assertEquals(null, simpleAid.getDomain());

        // AID STB
        AidParser stbAid = new AidParser("123456789-stb");
        assertEquals(true, stbAid.isStb());
        assertEquals(false, stbAid.isOtt());
        assertEquals(true, stbAid.isValidFormat());
        assertEquals("stb", stbAid.getDomain());

        // AID OTT Web
        AidParser ottWebAid = new AidParser("123456789-web");
        assertEquals(false, ottWebAid.isStb());
        assertEquals(true, ottWebAid.isOtt());
        assertEquals(true, ottWebAid.isValidFormat());
        assertEquals("web", ottWebAid.getDomain());
    }

    private String buildValueWithTimeslot(Map<String,Long> conceptsWeights, long ratioVodLive,
            List<String> bestLiveChannels, List<String> bestOdProviders, String timeslot) {

        return String.join(PIPE, "12345678;;" + timeslot, "IA", "TimeSlots",
                buildJsonLive(conceptsWeights, ratioVodLive, bestLiveChannels),
                EXPIRATION_DATE, FRESHNESS_DATE);
    }
}
