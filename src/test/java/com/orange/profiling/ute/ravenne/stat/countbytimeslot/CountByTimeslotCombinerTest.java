package com.orange.profiling.ute.ravenne.stat.countbytimeslot;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.PrintStream;
import java.util.ArrayList;
import java.util.List;

import org.apache.hadoop.io.Text;
import org.apache.hadoop.mrunit.mapreduce.ReduceDriver;
import org.apache.log4j.BasicConfigurator;
import org.apache.log4j.Level;
import org.apache.log4j.Logger;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

import com.orange.profiling.common.utils.FieldsUtils;

public class CountByTimeslotCombinerTest {

    private static final String PIPE = FieldsUtils.PIPE;

    ReduceDriver<Text, Text, Text, Text> reduceDriver;
    private final ByteArrayOutputStream errContent = new ByteArrayOutputStream();

    @Before
    public void setUp() {

        CountByTimeslotCombiner reducer = new CountByTimeslotCombiner();
        reduceDriver = ReduceDriver.newReduceDriver(reducer);

        BasicConfigurator.configure();
        Logger.getRootLogger().setLevel(Level.ERROR);
    }

    @Before
    public void setUpStreams() {
        System.setErr(new PrintStream(this.errContent));
    }

    @After
    public final void cleanUpStreams() {
        System.setErr(null);
    }

    @Test
    public final void testSimpleReducer() throws IOException, InterruptedException, ClassNotFoundException {

        String timebox = "d2t6";
        String nbconcept = "12";

        List<Text> values = new ArrayList<Text>();
        values.add(new Text("1"));
        values.add(new Text("1"));
        values.add(new Text("1"));
        values.add(new Text("1"));
        values.add(new Text("1"));

        Text key = new Text(timebox+PIPE+nbconcept);
        reduceDriver.withInput(key, values);

        reduceDriver.withOutput(key, new Text("5"));

        reduceDriver.runTest();
    }

    @Test
    public final void testReducerBadNumber() throws IOException, InterruptedException, ClassNotFoundException {

        String timebox = "d2t6";
        String nbconcept = "12";

        List<Text> values = new ArrayList<Text>();
        values.add(new Text("1"));
        values.add(new Text("1"));
        values.add(new Text("1"));
        values.add(new Text("1"));
        values.add(new Text("A"));

        Text key = new Text(timebox+PIPE+nbconcept);
        reduceDriver.withInput(key, values);

        reduceDriver.withOutput(key, new Text("4"));

        reduceDriver.runTest();
    }

    @Test
    public final void testReducerBadKeyFormat() throws IOException, InterruptedException, ClassNotFoundException {

        String timebox = "d2t6";
        String nbconcept = "12";

        List<Text> values = new ArrayList<Text>();
        values.add(new Text("1"));
        values.add(new Text("1"));
        values.add(new Text("1"));

        // No check is made on key format in combiner as we reuse the key as is (and it should not be badly formated)
        Text key = new Text(timebox+"-"+nbconcept);
        reduceDriver.withInput(key, values);
        reduceDriver.withOutput(key, new Text("3"));
        reduceDriver.runTest();
    }

}
