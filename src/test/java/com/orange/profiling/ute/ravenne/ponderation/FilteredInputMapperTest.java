package com.orange.profiling.ute.ravenne.ponderation;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.PrintStream;
import java.net.URI;
import java.net.URISyntaxException;

import org.apache.hadoop.io.Text;
import org.apache.hadoop.mrunit.mapreduce.MapDriver;
import org.apache.log4j.BasicConfigurator;
import org.apache.log4j.Level;
import org.apache.log4j.Logger;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

import com.orange.profiling.ute.ravenne.util.MyPredicate;

public class FilteredInputMapperTest {
    private URI runtimeRessourceFolder;

    MapDriver<Object, Text, Text, Text> mapDriver;
    private final ByteArrayOutputStream errContent = new ByteArrayOutputStream();
    private static final String TAB = "\t";

    @Before
    public void setUp() throws URISyntaxException {
        runtimeRessourceFolder = FilteredInputMapperTest.class.getResource("/ute-ravenne-ponderation/").toURI();
        String catalogConcept = runtimeRessourceFolder.resolve("in/acceptedExcludedConcepts.txt").getPath();
        String conceptFilterMappingTablePath = runtimeRessourceFolder.resolve("in/conceptFilterMappingTableEmpty").getPath();

        FilteredInputMapper inputMapper = new FilteredInputMapper();
        mapDriver = MapDriver.newMapDriver(inputMapper);

        mapDriver.getConfiguration().set(FilteredInputMapper.PREDICATE_CLASS,
                "com.orange.profiling.ute.ravenne.util.DefaultPredicate");
        mapDriver.getConfiguration().set(MyPredicate.PREDICATE_CLASS_ARG, catalogConcept);
        mapDriver.getConfiguration().set(FilteredInputMapper.PROCESS_DATE, "20190501");
        mapDriver.getConfiguration().set(FilteredInputMapper.CONCEPT_FILTER_MAPPING_TABLE,
                conceptFilterMappingTablePath);

        BasicConfigurator.configure();
        Logger.getRootLogger().setLevel(Level.ERROR);
    }

    @Before
    public void setUpStreams() {
        System.setErr(new PrintStream(this.errContent));
    }

    @After
    public final void cleanUpStreams() {
        System.setErr(null);
    }

    @Test
    public final void testOkLiveSport() throws IOException {
        String aid = "123456789";
        String concepts = "Genre Orange/émission/football/émission football,sujets/sport/compétitions,sujets/sport,sous-genres/sport,sujets/sport/football";
        String timeBoxes = "4" + TAB + "6" + TAB + "10";
        String liveOrVod = "LIVE";
        String zapDuration = "1800";
        String beginZap = "**********";
        String weight = "460";
        String provider = "123";
        String dayTimeslot = "d4t10";
        String expectedConcepts = "sujets/sport/compétitions,sujets/sport,sous-genres/sport,sujets/sport/football";
        String offerName = "LIVE";
        String daysSinceView = "4";
        String col9Tocol13 = TAB + TAB + TAB + TAB;

        mapDriver.withInput(new Text(),
                new Text(aid + TAB + concepts + TAB + timeBoxes + TAB + liveOrVod + TAB + zapDuration
                        + TAB + beginZap + TAB + provider + TAB + col9Tocol13 + TAB + offerName));

        mapDriver.withOutput(new Text(aid),
                new Text(expectedConcepts + TAB + dayTimeslot + TAB + liveOrVod + TAB + zapDuration + TAB
                        + weight + TAB + provider + TAB + offerName + TAB + daysSinceView));
        mapDriver.runTest();
    }

    @Test
    public final void testOkLiveSerie() throws IOException {
        String aid = "123456789";
        String concepts = "Genre Orange/série/autre/série,envies/éclats de rire,audience/en famille,catégories/fiction/série,"
                +"type/sitcoms,ambiance et ton/humoristique,genres/comédie,audience/pour toute la famille,envies/en famille";
        String timeBoxes = "5" + TAB + "4" + TAB + "8";
        String liveOrVod = "LIVE";
        String zapDuration = "240";
        String beginZap = "**********";
        String weight = "184";
        String provider = "123";
        String dayTimeslot = "d5t8";
        String expectedConcepts = "audience/en famille,type/sitcoms,ambiance et ton/humoristique,"
                +"genres/comédie,audience/pour toute la famille";

        String offerName = "LIVE";
        String daysSinceView = "5";
        String col9Tocol13 = TAB + TAB + TAB + TAB;

        mapDriver.withInput(new Text(),
                new Text(aid + TAB + concepts + TAB + timeBoxes + TAB + liveOrVod + TAB + zapDuration + TAB
                        + beginZap + TAB + provider + TAB + col9Tocol13 + TAB + offerName));

        mapDriver.withOutput(new Text(aid),
                new Text(expectedConcepts + TAB + dayTimeslot + TAB + liveOrVod + TAB + zapDuration + TAB
                        + weight + TAB + provider + TAB + offerName + TAB + daysSinceView));
        mapDriver.runTest();
    }

    @Test
    public final void testOkVodFilm() throws IOException {
        String aid = "123456789";
        String concepts = "Genre Orange/film/animation/film animation,personnages/fratrie,catégories/fiction/série,sujets/enfance,"
                +"thèmes/seul contre tous,ambiance et ton/humoristique,lieu de tournage/Amériques/Etats-Unis,audience/pour enfants,"
                +"format/animation,envies/éclats de rire,catégories/programme jeunesse/dessins animés,format/animation/dessin animé,"
                +"type/sitcoms,sujets/famille nombreuse,lieu de l'action/Amériques,lieu de l'action/Amériques/Amérique du nord/Etats-Unis,"
                +"thèmes/duel et rivalité";
        String timeBoxes = "2" + TAB + "4" + TAB + "7";
        String liveOrVod = "VOD";
        String zapDuration = "1284";
        String beginZap = "**********";
        String weight = "425";
        String provider1 = "123|catchuptv_xxx";
        String provider2 = "123|";
        String offerName = "TVOD";
        String daysSinceView = "2";
        String col9Tocol13 = TAB + TAB + TAB + TAB;

        String dayTimeslot = "d2t7";
        String expectedConcepts = "personnages/fratrie,sujets/enfance,"
                +"thèmes/seul contre tous,ambiance et ton/humoristique,audience/pour enfants,"
                +"type/sitcoms,sujets/famille nombreuse,thèmes/duel et rivalité";

        mapDriver.withInput(new Text(), new Text(aid + TAB + concepts + TAB + timeBoxes + TAB + liveOrVod + TAB
                + zapDuration + TAB + beginZap + TAB + provider1 + TAB + col9Tocol13 + TAB + offerName));
        mapDriver.withInput(new Text(), new Text(aid + TAB + concepts + TAB + timeBoxes + TAB + liveOrVod + TAB
                + zapDuration + TAB + beginZap + TAB + provider2 + TAB + col9Tocol13 + TAB + offerName));

        mapDriver.withOutput(new Text(aid), new Text(expectedConcepts + TAB + dayTimeslot + TAB + liveOrVod + TAB
                + zapDuration + TAB + weight + TAB + provider1 + TAB + offerName + TAB + daysSinceView));
        mapDriver.withOutput(new Text(aid), new Text(expectedConcepts + TAB + dayTimeslot + TAB + liveOrVod + TAB
                + zapDuration + TAB + weight + TAB + provider2 + TAB + offerName + TAB + daysSinceView));

        mapDriver.runTest();
    }

    @Test
    public final void testOkLiveNoRemainingConcepts() throws IOException {
        String aid = "123456789";
        String concepts = "Genre Orange/film/aventure/fil aventure,personnages:aventuriers et truands,format/thx,"
                +"envies/aventure,actions/rapide,actors/Daniel Craig,nimporte/quoi";
        String timeBoxes = "4" + TAB + "3" + TAB + "7";
        String liveOrVod = "LIVE";
        String zapDuration = "1800";
        String beginZap = "**********";
        String weight = "460";
        String provider = "123";
        String dayTimeslot = "d4t7";
        String expectedConcepts = "";
        String offerName = "LIVE";
        String daysSinceView = "4";
        String col9Tocol13 = TAB + TAB + TAB + TAB;

        mapDriver.withInput(new Text(),
                new Text(aid + TAB + concepts + TAB + timeBoxes + TAB + liveOrVod + TAB + zapDuration + TAB
                        + beginZap + TAB + provider + TAB + col9Tocol13 + TAB + offerName));

        mapDriver.withOutput(new Text(aid),
                new Text(expectedConcepts + TAB + dayTimeslot + TAB + liveOrVod + TAB + zapDuration + TAB
                        + weight + TAB + provider + TAB + offerName+ TAB + daysSinceView));
        mapDriver.withCounter(Counters.ALL_CONCEPTS_FILTERED, 1);
        mapDriver.runTest();
    }

    @Test
    public final void testKoTooShort() throws IOException {
        String aid = "123456789";
        String concepts = "Genre Orange/film/aventure/fil aventure,personnages:aventuriers et truands,format/thx,"
                +"envies/aventure,actions/rapide,actors/Daniel Craig,nimporte/quoi";
        String timeBoxes = "4" + TAB + "3" + TAB + "7";

        mapDriver.withInput(new Text(),
                new Text(aid + TAB + concepts + TAB + timeBoxes));

        mapDriver.withCounter(Counters.BAD_FORMAT, 1);
        mapDriver.runTest();
    }

    @Test
    public final void testOkLiveNoZapDuration() throws IOException {
        String aid = "123456789";
        String concepts = "Genre Orange/série/autre/série,envies/éclats de rire,audience/en famille,catégories/fiction/série,"
                +"type/sitcoms,ambiance et ton/humoristique,genres/comédie,audience/pour toute la famille,envies/en famille";
        String timeBoxes = "5" + TAB + "4" + TAB + "8";
        String liveOrVod = "LIVE";
        String weight = "33";
        String provider = "123";
        String dayTimeslot = "d5t8";
        String expectedConcepts = "audience/en famille,type/sitcoms,ambiance et ton/humoristique,"
                +"genres/comédie,audience/pour toute la famille";
        String offerName = "LIVE";
        String daysSinceView = "18017";
        String col9Tocol13 = TAB + TAB + TAB + TAB;

                mapDriver.withInput(new Text(),
                new Text(aid + TAB + concepts + TAB + timeBoxes + TAB + liveOrVod + TAB + "0" + TAB
                        + weight + TAB + provider + TAB + col9Tocol13 + TAB + offerName));

        mapDriver.withOutput(new Text(aid),
                new Text(expectedConcepts + TAB + dayTimeslot + TAB + liveOrVod + TAB + "0" + TAB
                        + weight + TAB + provider + TAB + offerName + TAB + daysSinceView));
        mapDriver.runTest();
    }
}
