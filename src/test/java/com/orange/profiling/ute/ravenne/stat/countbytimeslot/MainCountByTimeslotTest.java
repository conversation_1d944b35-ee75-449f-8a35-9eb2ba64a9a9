package com.orange.profiling.ute.ravenne.stat.countbytimeslot;

import static org.junit.Assert.*;

import java.io.File;
import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;

import org.apache.commons.io.FileUtils;
import org.apache.log4j.BasicConfigurator;
import org.apache.log4j.Level;
import org.apache.log4j.Logger;
import org.junit.Before;
import org.junit.Test;

import com.orange.profiling.common.exceptions.FailedJobException;
import com.orange.profiling.common.utils.unit_tests.TestUtils;

public class MainCountByTimeslotTest {
    private static URI runtimeResourceFolder;

    @Before
    public void setUp() throws URISyntaxException {
        runtimeResourceFolder = MainCountByTimeslotTest.class.getResource("/ute-ravenne-countbytimeslot/").toURI();

        BasicConfigurator.configure();
        Logger.getRootLogger().setLevel(Level.ERROR);
    }

    @Test
    public final void testMainKo() {

        String[] args = new String[1];
        args[0] = "";
        try {
            MainCountByTimeslot.main(args);
            fail("Should have thrown an IllegalArgumentException because no parameters");
        }
        catch (Exception e) {
            assertEquals(e.getMessage(),
                    "Takes 4 arguments : inputRavenneConceptsWeights inputRavenneAidCounter outputDir processDate");
        }
    }

    @Test
    public final void testMainOk() throws IOException, ClassNotFoundException, InterruptedException, FailedJobException {

        final String profilGeneticInput = runtimeResourceFolder.resolve("in/ponderation/conceptsweights").getPath();
        final String ponderationCounterInput = runtimeResourceFolder.resolve("in/ponderation/_COUNTER").getPath();
        final String resultOutput = runtimeResourceFolder.resolve("out/countbytimeslot/2019/11").getPath();
        final String processDate = "20190312";
        final String resultExpected = runtimeResourceFolder.resolve("expected").getPath();

        // arguments
        String[] cmdArgs = new String[] {
                profilGeneticInput,
                ponderationCounterInput,
                resultOutput,
                processDate
        };

        // delete output
        FileUtils.deleteDirectory(new File(resultOutput));

        // Run
        MainCountByTimeslot.main(cmdArgs);

        // Test _Counters
        List<String> expectedCounters = TestUtils.getLinesFromPath(resultExpected, "_COUNTERS");
        List<String> actualCounters = TestUtils.getLinesFromPath(resultOutput, "_COUNTERS");
        assertEquals("Counters", expectedCounters, actualCounters);

        List<String> outputLines = TestUtils.getLinesFromPath(resultOutput, "part*");
        List<String> expectedLines = TestUtils.getLinesFromPath(resultExpected, "part*");

        assertEquals("output lines", expectedLines, outputLines);

    }

}
