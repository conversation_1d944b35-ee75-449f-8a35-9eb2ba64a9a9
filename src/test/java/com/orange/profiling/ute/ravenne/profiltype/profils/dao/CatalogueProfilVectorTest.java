package com.orange.profiling.ute.ravenne.profiltype.profils.dao;

import static org.junit.Assert.*;

import org.junit.Test;

import com.orange.profiling.ute.ravenne.profiltype.profils.dao.CatalogueProfilVector;

import org.json.simple.JSONObject;

public class CatalogueProfilVectorTest {

    @Test
    public void testReadConceptDoubleValueOk() {
        JSONObject jsonObject = new JSONObject();
        String concept = "monconcept";
        Double expected = 4.5D;

        jsonObject.put(concept, expected);

        Double actual = CatalogueProfilVector.readConceptDoubleValue(jsonObject, concept);

        assertEquals(expected, actual);
    }

    @Test
    public void testReadConceptDoubleValueLong() {
        JSONObject jsonObject = new JSONObject();
        String concept = "monconcept";
        Double expected = 4.0D;
        Long given = 4L;

        jsonObject.put(concept, given);

        Double actual = CatalogueProfilVector.readConceptDoubleValue(jsonObject, concept);

        assertEquals(expected, actual);
    }


    @Test
    public void testReadConceptDoubleValueStringDouble() {
        JSONObject jsonObject = new JSONObject();
        String concept = "monconcept";
        Double expected = 4.5D;
        String given = "4.5";

        jsonObject.put(concept, given);

        Double actual = CatalogueProfilVector.readConceptDoubleValue(jsonObject, concept);

        assertEquals(expected, actual);
    }

    @Test
    public void testReadConceptDoubleValueStringLong() {
        JSONObject jsonObject = new JSONObject();
        String concept = "monconcept";
        Double expected = 4.0D;
        String given = "4";

        jsonObject.put(concept, given);

        Double actual = CatalogueProfilVector.readConceptDoubleValue(jsonObject, concept);

        assertEquals(expected, actual);
    }

    @Test
    public void testReadConceptDoubleValueNull() {
        JSONObject jsonObject = new JSONObject();
        String concept = "monconcept";
        Double expected = null;

        jsonObject.put(concept, null);

        Double actual = CatalogueProfilVector.readConceptDoubleValue(jsonObject, concept);

        assertEquals(expected, actual);
    }

}
