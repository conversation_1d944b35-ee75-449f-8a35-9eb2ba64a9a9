package com.orange.profiling.ute.ravenne.filterandtimeslot;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.PrintStream;
import java.util.ArrayList;
import java.util.List;

import org.apache.hadoop.io.Text;
import org.apache.hadoop.mrunit.mapreduce.ReduceDriver;
import org.apache.log4j.BasicConfigurator;
import org.apache.log4j.Level;
import org.apache.log4j.Logger;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.runners.MockitoJUnitRunner;

import com.orange.profiling.common.mapred.MosWriter;
import com.orange.profiling.common.utils.FieldsUtils;
import com.orange.profiling.ute.ravenne.dao.TimeboxZapAggregation;

@RunWith(MockitoJUnitRunner.class)
public class FilterAndTimeslotReducerTest {

    private static final String TAB = FieldsUtils.TAB;

    private static final String AID = "123456789";
    private static final String LIVE = TimeboxZapAggregation.LIVE;
    private static final String VOD = TimeboxZapAggregation.VOD;
    private static final String BROADCAST_DURATION = "3600";
    private static final String PROVIDER = "providerABC";
    private static final String CONTENT_ID = "id123";
    private static final String CONTENT_TITLE = "TITLE OF PROGRAM";
    private static final String ZAP_DAY = "20190711";
    private static final String EXTERNALENTERTAINMENTID = "episode-Title of serie and episode-season-X-episode-Y-2000";
    private static final String SEASONNAME = "Saison X";
    private static final String SERIESNAME = "Title of serie";
    private static final String OFFERNAME = "TVOD";


    private static final String LIVE_PROGRAM_KEY = AID + TAB + LIVE + TAB + BROADCAST_DURATION
            + TAB + PROVIDER + TAB + CONTENT_ID + TAB + CONTENT_TITLE + TAB + EXTERNALENTERTAINMENTID
            + TAB + SEASONNAME + TAB + SERIESNAME + TAB + LIVE + TAB + ZAP_DAY;

    private static final String VOD_PROGRAM_KEY = AID + TAB + VOD + TAB + BROADCAST_DURATION
            + TAB + PROVIDER + TAB + CONTENT_ID + TAB + CONTENT_TITLE + TAB + EXTERNALENTERTAINMENTID
            + TAB + SEASONNAME + TAB + SERIESNAME + TAB + OFFERNAME + TAB + "0";

    ReduceDriver<Text, Text, Text, Text> reduceDriver;
    private final ByteArrayOutputStream errContent = new ByteArrayOutputStream();

    @Mock
    private MosWriter mockMosWriter;

    @Before
    public void setUp() {

        FilterAndTimeslotReducer reducer = new FilterAndTimeslotReducer();
        reducer.setMosWriter(mockMosWriter);
        reduceDriver = ReduceDriver.newReduceDriver(reducer);
        reduceDriver.getConfiguration().set("minDate", "20190705");

        BasicConfigurator.configure();
        Logger.getRootLogger().setLevel(Level.ERROR);
    }

    @Before
    public void setUpStreams() {
        System.setErr(new PrintStream(this.errContent));
    }

    @After
    public final void cleanUpStreams() {
        System.setErr(null);
    }

    // #################### LIVE ##########################

    @Test
    public final void testLiveSingleZapReducer() throws IOException, InterruptedException, ClassNotFoundException {
        String beginZap = "**********";
        String dayPeriodTimeslot = "4" + TAB + "3" + TAB + "5";
        String zapDuration = "2000"; // more than 50% of BROADCAST_DURATION
        String concepts = "catégories/divertissement,sous-genres/jeu,objets/(jeux et jouets)";
        String filteredConcepts = "catégories/divertissement,sous-genres/jeu";
        String averageTime = "1562851679";
        String device = "stb";

        List<Text> values = new ArrayList<Text>();
        values.add(new Text(beginZap + TAB + zapDuration + TAB + concepts + TAB + device ));

        reduceDriver.withInput(new Text(LIVE_PROGRAM_KEY), values);
        reduceDriver.withCounter(Counters.NB_PROG_VIEW, 1);

        reduceDriver.runTest();


        Mockito.verify(mockMosWriter).open(Mockito.any());
        int nbCall = 0;
        nbCall += withSelected(AID, filteredConcepts, dayPeriodTimeslot, LIVE, zapDuration, averageTime, device);
        Mockito.verify(mockMosWriter, Mockito.times(nbCall)).write(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any());
        Mockito.verify(mockMosWriter).close();
    }

    @Test
    public final void testLiveSingleZapDuration0Reducer() throws IOException, InterruptedException, ClassNotFoundException {
        String beginZap = "**********";
        String zapDuration = "0";
        String concepts = "catégories/divertissement,sous-genres/jeu";
        String device = "stb";

        List<Text> values = new ArrayList<Text>();
        values.add(new Text(beginZap + TAB + zapDuration + TAB + concepts + TAB + device ));

        reduceDriver.withInput(new Text(LIVE_PROGRAM_KEY), values);
        reduceDriver.withCounter(Counters.NB_PROG_VIEW, 0);
        reduceDriver.withCounter(Counters.ZAP_TOO_SHORT, 0);

        reduceDriver.runTest();

        Mockito.verify(mockMosWriter).open(Mockito.any());
        int nbCall = 0;
        Mockito.verify(mockMosWriter, Mockito.times(nbCall)).write(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any());
        Mockito.verify(mockMosWriter).close();
    }

    @Test
    public final void testLiveTooShortLiveReducer() throws IOException, InterruptedException, ClassNotFoundException {
        String beginZap = "**********";
        String zapDuration = "1700"; // less than 50% of BROADCAST_DURATION
        String concepts = "catégories/divertissement,sous-genres/jeu";
        String device = "stb";

        List<Text> values = new ArrayList<Text>();
        values.add(new Text(beginZap + TAB + zapDuration + TAB + concepts + TAB + device ));

        reduceDriver.withInput(new Text(LIVE_PROGRAM_KEY), values);
        reduceDriver.withCounter(Counters.ZAP_TOO_SHORT, 1);

        reduceDriver.runTest();

        Mockito.verify(mockMosWriter).open(Mockito.any());
        int nbCall = 0;
        nbCall += withTooshort(AID, LIVE, BROADCAST_DURATION, ZAP_DAY, beginZap, zapDuration, concepts, LIVE, device);
        Mockito.verify(mockMosWriter, Mockito.times(nbCall)).write(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any());
        Mockito.verify(mockMosWriter).close();
    }

    @Test
    public final void testLiveSplittedZapReducer() throws IOException, InterruptedException, ClassNotFoundException {
        String beginZap1 = "**********"; // first ==> begin zap
        String beginZap2 = "1562856879";
        String dayPeriodTimeslot = "4" + TAB + "3" + TAB + "5";
        String zapDuration1 = "700";
        String zapDuration2 = "1200";
        String zapDurationSum = "1900"; // sum of zap durations is more than 50% of BROADCAST_DURATION
        String concepts = "catégories/divertissement,sous-genres/jeu";
        String averageTime = "1562851679";
        String device = "stb";

        List<Text> values = new ArrayList<Text>();
        values.add(new Text(beginZap1 + TAB + zapDuration1 + TAB + concepts + TAB + device ));
        values.add(new Text(beginZap2 + TAB + zapDuration2 + TAB + concepts + TAB + device ));

        reduceDriver.withInput(new Text(LIVE_PROGRAM_KEY), values);
        reduceDriver.withCounter(Counters.NB_PROG_VIEW, 1);

        reduceDriver.runTest();

        Mockito.verify(mockMosWriter).open(Mockito.any());
        int nbCall = 0;
        nbCall += withSelected(AID, concepts, dayPeriodTimeslot, LIVE, zapDurationSum, averageTime, device);
        Mockito.verify(mockMosWriter, Mockito.times(nbCall)).write(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any());
        Mockito.verify(mockMosWriter).close();
    }

    @Test
    public final void testLiveSplittedZapReducerBadBeginZap() throws IOException, InterruptedException, ClassNotFoundException {
        String beginZap1 = "B562849879"; // ==> bad zap
        String beginZap2 = "1562856879";
        String dayPeriodTimeslot = "4" + TAB + "3" + TAB + "5";
        String zapDuration1 = "700";
        String zapDuration2 = "1200";
        String zapDurationSum = "1900"; // sum of zap durations is more than 50% of BROADCAST_DURATION
        String concepts = "catégories/divertissement,sous-genres/jeu";
        String averageTime = "1562851679";
        String device = "stb";

        List<Text> values = new ArrayList<Text>();
        values.add(new Text(beginZap1 + TAB + zapDuration1 + TAB + concepts + TAB + device ));
        values.add(new Text(beginZap2 + TAB + zapDuration2 + TAB + concepts + TAB + device ));

        reduceDriver.withInput(new Text(LIVE_PROGRAM_KEY), values);
        reduceDriver.withCounter(Counters.NB_PROG_VIEW, 0);
        reduceDriver.withCounter(Counters.ZAP_TOO_SHORT, 1);

        reduceDriver.runTest();

        Mockito.verify(mockMosWriter).open(Mockito.any());
        int nbCall = 0;
        nbCall += withTooshort(AID, LIVE, BROADCAST_DURATION, ZAP_DAY, beginZap2, zapDuration2, concepts, LIVE, device);
        Mockito.verify(mockMosWriter, Mockito.times(nbCall)).write(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any());
        Mockito.verify(mockMosWriter).close();
    }

    @Test
    public final void testLiveSplittedInvertedZapReducer()
            throws IOException, InterruptedException, ClassNotFoundException {

        String beginZap1 = "1562862879";
        String beginZap2 = "1562856879"; // first ==> begin zap
        String dayPeriodTimeslot = "4" + TAB + "3" + TAB + "6";
        String zapDuration1 = "700";
        String zapDuration2 = "1200";
        String zapDurationSum = "1900"; // sum of zap durations is more than 50% of BROADCAST_DURATION
        String concepts = "catégories/divertissement,sous-genres/jeu";
        String averageTime = "1562858679";
        String device = "stb";

        List<Text> values = new ArrayList<Text>();
        values.add(new Text(beginZap1 + TAB + zapDuration1 + TAB + concepts + TAB + device ));
        values.add(new Text(beginZap2 + TAB + zapDuration2 + TAB + concepts + TAB + device ));

        reduceDriver.withInput(new Text(LIVE_PROGRAM_KEY), values);
        reduceDriver.withCounter(Counters.NB_PROG_VIEW, 1);

        reduceDriver.runTest();

        Mockito.verify(mockMosWriter).open(Mockito.any());
        int nbCall = 0;
        nbCall += withSelected(AID, concepts, dayPeriodTimeslot, LIVE, zapDurationSum, averageTime, device);
        Mockito.verify(mockMosWriter, Mockito.times(nbCall)).write(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any());
        Mockito.verify(mockMosWriter).close();
    }

    @Test
    public final void testLiveSplittedInvertedZapUnder50Reducer()
            throws IOException, InterruptedException, ClassNotFoundException {

        String beginZap1 = "1562862879";
        String beginZap2 = "1562856879"; // first ==> begin zap
        String zapDuration1 = "700";
        String zapDuration2 = "900";
        String zapDurationSum = "1600"; // sum of zap durations is less than 50% of BROADCAST_DURATION
        String concepts = "catégories/divertissement,sous-genres/jeu";
        String device = "stb";

        List<Text> values = new ArrayList<Text>();
        values.add(new Text(beginZap1 + TAB + zapDuration1 + TAB + concepts + TAB + device ));
        values.add(new Text(beginZap2 + TAB + zapDuration2 + TAB + concepts + TAB + device ));

        reduceDriver.withInput(new Text(LIVE_PROGRAM_KEY), values);
        reduceDriver.withCounter(Counters.ZAP_TOO_SHORT, 1);

        reduceDriver.runTest();

        Mockito.verify(mockMosWriter).open(Mockito.any());
        int nbCall = 0;
        nbCall += withTooshort(AID, LIVE, BROADCAST_DURATION, ZAP_DAY, beginZap2, zapDurationSum, concepts, LIVE, device);
        Mockito.verify(mockMosWriter, Mockito.times(nbCall)).write(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any());
        Mockito.verify(mockMosWriter).close();
    }

    @Test
    public final void testLiveSplittedInvertedZapUnder50BeginBeforeMinDateReducer()
            throws IOException, InterruptedException, ClassNotFoundException {

        String beginZap1 = "1562862879";
        String beginZap2 = "1562243031"; // first but before min date
        String zapDuration1 = "700";
        String zapDuration2 = "900";
        String zapDurationSum = "1600"; // sum of zap durations is less than 50% of BROADCAST_DURATION
        String concepts = "catégories/divertissement,sous-genres/jeu";
        String device = "stb";

        List<Text> values = new ArrayList<Text>();
        values.add(new Text(beginZap1 + TAB + zapDuration1 + TAB + concepts + TAB + device ));
        values.add(new Text(beginZap2 + TAB + zapDuration2 + TAB + concepts + TAB + device ));

        reduceDriver.withInput(new Text(LIVE_PROGRAM_KEY), values);
        reduceDriver.withCounter(Counters.ZAP_TOO_SHORT, 0);

        reduceDriver.runTest();

        Mockito.verify(mockMosWriter).open(Mockito.any());
        int nbCall = 0;
        Mockito.verify(mockMosWriter, Mockito.times(nbCall)).write(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any());
        Mockito.verify(mockMosWriter).close();
    }

    @Test
    public final void testLiveSplittedInvertedThreeZapReducer()
            throws IOException, InterruptedException, ClassNotFoundException {

        String beginZap1 = "1562866879";
        String beginZap2 = "1562862879"; // first ==> begin zap
        String beginZap3 = "1562868879";
        String dayPeriodTimeslot = "4" + TAB + "4" + TAB + "7";
        String zapDuration1 = "700";
        String zapDuration2 = "600";
        String zapDuration3 = "800";
        String zapDurationSum = "2100"; // sum of zap durations is more than 50% of BROADCAST_DURATION
        String concepts = "catégories/divertissement,sous-genres/jeu";
        String averageTime = "1562864679";
        String device = "stb";

        List<Text> values = new ArrayList<Text>();
        values.add(new Text(beginZap1 + TAB + zapDuration1 + TAB + concepts + TAB + device ));
        values.add(new Text(beginZap2 + TAB + zapDuration2 + TAB + concepts + TAB + device ));
        values.add(new Text(beginZap3 + TAB + zapDuration3 + TAB + concepts + TAB + device ));

        reduceDriver.withInput(new Text(LIVE_PROGRAM_KEY), values);
        reduceDriver.withCounter(Counters.NB_PROG_VIEW, 1);

        reduceDriver.runTest();

        Mockito.verify(mockMosWriter).open(Mockito.any());
        int nbCall = 0;
        nbCall += withSelected(AID, concepts, dayPeriodTimeslot, LIVE, zapDurationSum, averageTime, device);
        Mockito.verify(mockMosWriter, Mockito.times(nbCall)).write(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any());
        Mockito.verify(mockMosWriter).close();
    }

    @Test
    public final void testLiveNoConceptZapReducer() throws IOException, InterruptedException, ClassNotFoundException {
        String beginZap = "**********";
        String dayPeriodTimeslot = "4" + TAB + "3" + TAB + "5";
        String zapDuration = "2000"; // more than 50% of BROADCAST_DURATION
        String averageTime = "1562851679";
        String concepts = "";
        String device = "stb";

        List<Text> values = new ArrayList<Text>();
        values.add(new Text(beginZap + TAB + zapDuration + TAB + concepts + TAB + device ));

        reduceDriver.withInput(new Text(LIVE_PROGRAM_KEY), values);
        reduceDriver.withCounter(Counters.NB_PROG_VIEW, 1);

        reduceDriver.runTest();

        Mockito.verify(mockMosWriter).open(Mockito.any());
        int nbCall = 0;
        nbCall += withSelected(AID, "", dayPeriodTimeslot, LIVE, zapDuration, averageTime, device);
        Mockito.verify(mockMosWriter, Mockito.times(nbCall)).write(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any());
        Mockito.verify(mockMosWriter).close();
    }

    @Test
    public final void testLiveNoSupportTypeZapReducer() throws IOException, InterruptedException, ClassNotFoundException {
        String beginZap = "**********";
        String dayPeriodTimeslot = "4" + TAB + "3" + TAB + "5";
        String zapDuration = "2000"; // more than 50% of BROADCAST_DURATION
        String averageTime = "1562851679";
        String concepts = "catégories/divertissement,sous-genres/jeu";
        String device = "";

        List<Text> values = new ArrayList<Text>();
        values.add(new Text(beginZap + TAB + zapDuration + TAB + concepts + TAB + device ));

        reduceDriver.withInput(new Text(LIVE_PROGRAM_KEY), values);
        reduceDriver.withCounter(Counters.NB_PROG_VIEW, 1);

        reduceDriver.runTest();

        Mockito.verify(mockMosWriter).open(Mockito.any());
        int nbCall = 0;
        nbCall += withSelected(AID, concepts, dayPeriodTimeslot, LIVE, zapDuration, averageTime, "");
        Mockito.verify(mockMosWriter, Mockito.times(nbCall)).write(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any());
        Mockito.verify(mockMosWriter).close();
    }

    @Test
    public final void testLiveBrodcastDurationNullZapReducer() throws IOException, InterruptedException, ClassNotFoundException {
        String beginZap = "**********";
        String dayPeriodTimeslot = "4" + TAB + "3" + TAB + "5";
        String zapDuration = "1000";
        String device = "stb";

        List<Text> values = new ArrayList<Text>();
        values.add(new Text(beginZap + TAB + zapDuration + TAB + "" + TAB + device));

        String liveKeyDurationNull = AID + TAB + LIVE + TAB + "0"
                + TAB + PROVIDER + TAB + CONTENT_ID + TAB + CONTENT_TITLE + TAB +
                EXTERNALENTERTAINMENTID + TAB + SEASONNAME + TAB + SERIESNAME + TAB + LIVE+ TAB + ZAP_DAY;
        reduceDriver.withInput(new Text(liveKeyDurationNull), values);
        reduceDriver.withCounter(Counters.NB_PROG_VIEW, 1);

        reduceDriver.runTest();

        Mockito.verify(mockMosWriter).open(Mockito.any());
        int nbCall = 0;
        nbCall += withSelected(AID, "", dayPeriodTimeslot, LIVE, zapDuration, beginZap, device);
        Mockito.verify(mockMosWriter, Mockito.times(nbCall)).write(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any());
        Mockito.verify(mockMosWriter).close();
    }

    // #################### VOD ##########################

    @Test
    public final void testVodSingleZapReducer() throws IOException, InterruptedException, ClassNotFoundException {
        String beginZap = "**********";
        String dayPeriodTimeslot = "4" + TAB + "3" + TAB + "5";
        String zapDuration = "2900"; // more than 80% of BROADCAST_DURATION
        String concepts = "catégories/divertissement,sous-genres/jeu";
        String averageTime = "1562851679";
        String device = "stb";

        List<Text> values = new ArrayList<Text>();
        values.add(new Text(beginZap + TAB + zapDuration + TAB + concepts + TAB + device ));

        reduceDriver.withInput(new Text(VOD_PROGRAM_KEY), values);
        reduceDriver.withCounter(Counters.NB_PROG_VIEW, 1);

        reduceDriver.runTest();

        Mockito.verify(mockMosWriter).open(Mockito.any());
        int nbCall = 0;
        nbCall += withSelected(AID, concepts, dayPeriodTimeslot, VOD, zapDuration, averageTime, device);
        Mockito.verify(mockMosWriter, Mockito.times(nbCall)).write(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any());
        Mockito.verify(mockMosWriter).close();
    }

    @Test
    public final void testVodSingleZapDuration0Reducer() throws IOException, InterruptedException, ClassNotFoundException {
        String beginZap = "**********";
        String zapDuration = "0";
        String concepts = "catégories/divertissement,sous-genres/jeu";
        String device = "stb";

        List<Text> values = new ArrayList<Text>();
        values.add(new Text(beginZap + TAB + zapDuration + TAB + concepts + TAB + device));

        reduceDriver.withInput(new Text(VOD_PROGRAM_KEY), values);
        reduceDriver.withCounter(Counters.NB_PROG_VIEW, 0);
        reduceDriver.withCounter(Counters.ZAP_TOO_SHORT, 0);

        reduceDriver.runTest();

        Mockito.verify(mockMosWriter).open(Mockito.any());
        int nbCall = 0;
        Mockito.verify(mockMosWriter, Mockito.times(nbCall)).write(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any());
        Mockito.verify(mockMosWriter).close();
    }

    @Test
    public final void testVodTooShortLiveReducer() throws IOException, InterruptedException, ClassNotFoundException {
        String beginZap = "**********";
        String zapDuration = "2800"; // less than 80% of BROADCAST_DURATION
        String concepts = "catégories/divertissement,sous-genres/jeu";
        String device = "stb";

        List<Text> values = new ArrayList<Text>();
        values.add(new Text(beginZap + TAB + zapDuration + TAB + concepts + TAB + device ));

        reduceDriver.withInput(new Text(VOD_PROGRAM_KEY), values);
        reduceDriver.withCounter(Counters.ZAP_TOO_SHORT, 1);

        reduceDriver.runTest();

        Mockito.verify(mockMosWriter).open(Mockito.any());
        int nbCall = 0;
        nbCall += withTooshort(AID, VOD, BROADCAST_DURATION, "0", beginZap, zapDuration, concepts, OFFERNAME, device);
        Mockito.verify(mockMosWriter, Mockito.times(nbCall)).write(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any());
        Mockito.verify(mockMosWriter).close();
    }

    @Test
    public final void testVodSplittedZapReducer() throws IOException, InterruptedException, ClassNotFoundException {
        String beginZap1 = "**********"; // first ==> begin zap
        String beginZap2 = "1562856879";
        String dayPeriodTimeslot = "4" + TAB + "3" + TAB + "5";
        String zapDuration1 = "1300";
        String zapDuration2 = "1600";
        String zapDurationSum = "2900"; // sum of zap durations is more than 80% of BROADCAST_DURATION
        String concepts = "catégories/divertissement,sous-genres/jeu";
        String averageTime = "1562851679";
        String device = "stb";

        List<Text> values = new ArrayList<Text>();
        values.add(new Text(beginZap1 + TAB + zapDuration1 + TAB + concepts + TAB + device ));
        values.add(new Text(beginZap2 + TAB + zapDuration2 + TAB + concepts + TAB + device ));

        reduceDriver.withInput(new Text(VOD_PROGRAM_KEY), values);
        reduceDriver.withCounter(Counters.NB_PROG_VIEW, 1);

        reduceDriver.runTest();

        Mockito.verify(mockMosWriter).open(Mockito.any());
        int nbCall = 0;
        nbCall += withSelected(AID, concepts, dayPeriodTimeslot, VOD, zapDurationSum, averageTime, device);
        Mockito.verify(mockMosWriter, Mockito.times(nbCall)).write(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any());
        Mockito.verify(mockMosWriter).close();
    }

    @Test
    public final void testVodSplittedZapReducerBadBeginZap() throws IOException, InterruptedException, ClassNotFoundException {
        String beginZap1 = "B562849879"; // first ==> begin zap
        String beginZap2 = "1562856879";
        String dayPeriodTimeslot = "4" + TAB + "3" + TAB + "5";
        String zapDuration1 = "1300";
        String zapDuration2 = "1600";
        String zapDurationSum = "2900"; // sum of zap durations is more than 80% of BROADCAST_DURATION
        String concepts = "catégories/divertissement,sous-genres/jeu";
        String averageTime = "1562851679";
        String device = "stb";

        List<Text> values = new ArrayList<Text>();
        values.add(new Text(beginZap1 + TAB + zapDuration1 + TAB + concepts + TAB + device ));
        values.add(new Text(beginZap2 + TAB + zapDuration2 + TAB + concepts + TAB + device ));

        reduceDriver.withInput(new Text(VOD_PROGRAM_KEY), values);
        reduceDriver.withCounter(Counters.NB_PROG_VIEW, 0);
        reduceDriver.withCounter(Counters.ZAP_TOO_SHORT, 1);

        reduceDriver.runTest();

        Mockito.verify(mockMosWriter).open(Mockito.any());
        int nbCall = 0;
        nbCall += withTooshort(AID, VOD, BROADCAST_DURATION, "0", beginZap2, zapDuration2, concepts, OFFERNAME, device);
        Mockito.verify(mockMosWriter, Mockito.times(nbCall)).write(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any());
        Mockito.verify(mockMosWriter).close();
    }

    @Test
    public final void testVodSplittedInvertedZapReducer()
            throws IOException, InterruptedException, ClassNotFoundException {

        String beginZap1 = "1562862879";
        String beginZap2 = "1562856879"; // first ==> begin zap
        String dayPeriodTimeslot = "4" + TAB + "3" + TAB + "6";
        String zapDuration1 = "1300";
        String zapDuration2 = "1600";
        String zapDurationSum = "2900"; // sum of zap durations is more than 80% of BROADCAST_DURATION
        String concepts = "catégories/divertissement,sous-genres/jeu";
        String averageTime = "1562858679";
        String device = "stb";

        List<Text> values = new ArrayList<Text>();
        values.add(new Text(beginZap1 + TAB + zapDuration1 + TAB + concepts + TAB + device ));
        values.add(new Text(beginZap2 + TAB + zapDuration2 + TAB + concepts + TAB + device ));

        reduceDriver.withInput(new Text(VOD_PROGRAM_KEY), values);
        reduceDriver.withCounter(Counters.NB_PROG_VIEW, 1);

        reduceDriver.runTest();

        Mockito.verify(mockMosWriter).open(Mockito.any());
        int nbCall = 0;
        nbCall += withSelected(AID, concepts, dayPeriodTimeslot, VOD, zapDurationSum, averageTime, device);
        Mockito.verify(mockMosWriter, Mockito.times(nbCall)).write(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any());
        Mockito.verify(mockMosWriter).close();
    }

    @Test
    public final void testVodSplittedInvertedZapUnder80Reducer()
            throws IOException, InterruptedException, ClassNotFoundException {

        String beginZap1 = "1562862879";
        String beginZap2 = "1562856879"; // first ==> begin zap
        String zapDuration1 = "1200";
        String zapDuration2 = "1600";
        String zapDurationSum = "2800"; // sum of zap durations is less than 80% of BROADCAST_DURATION
        String concepts = "catégories/divertissement,sous-genres/jeu";
        String device = "stb";

        List<Text> values = new ArrayList<Text>();
        values.add(new Text(beginZap1 + TAB + zapDuration1 + TAB + concepts + TAB + device ));
        values.add(new Text(beginZap2 + TAB + zapDuration2 + TAB + concepts + TAB + device ));

        reduceDriver.withInput(new Text(VOD_PROGRAM_KEY), values);
        reduceDriver.withCounter(Counters.ZAP_TOO_SHORT, 1);

        reduceDriver.runTest();

        Mockito.verify(mockMosWriter).open(Mockito.any());
        int nbCall = 0;
        nbCall += withTooshort(AID, VOD, BROADCAST_DURATION, "0", beginZap2, zapDurationSum, concepts, OFFERNAME, device);
        Mockito.verify(mockMosWriter, Mockito.times(nbCall)).write(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any());
        Mockito.verify(mockMosWriter).close();
    }

    @Test
    public final void testVodSplittedInvertedZapUnder80BeginBeforeMinDateReducer()
            throws IOException, InterruptedException, ClassNotFoundException {

        String beginZap1 = "1562862879";
        String beginZap2 = "1562243031"; // first but before min date
        String zapDuration1 = "1200";
        String zapDuration2 = "1600";
        String zapDurationSum = "2800"; // sum of zap durations is less than 80% of BROADCAST_DURATION
        String concepts = "catégories/divertissement,sous-genres/jeu";
        String device = "stb";

        List<Text> values = new ArrayList<Text>();
        values.add(new Text(beginZap1 + TAB + zapDuration1 + TAB + concepts + TAB + device ));
        values.add(new Text(beginZap2 + TAB + zapDuration2 + TAB + concepts + TAB + device ));

        reduceDriver.withInput(new Text(VOD_PROGRAM_KEY), values);
        reduceDriver.withCounter(Counters.ZAP_TOO_SHORT, 0);

        reduceDriver.runTest();

        Mockito.verify(mockMosWriter).open(Mockito.any());
        int nbCall = 0;
        Mockito.verify(mockMosWriter, Mockito.times(nbCall)).write(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any());
        Mockito.verify(mockMosWriter).close();
    }

    @Test
    public final void testVodSplittedInvertedThreeZapReducer()
            throws IOException, InterruptedException, ClassNotFoundException {

        String beginZap1 = "1562866879";
        String beginZap2 = "1562862879"; // first ==> begin zap
        String beginZap3 = "1562868879";
        String dayPeriodTimeslot = "4" + TAB + "4" + TAB + "7";
        String zapDuration1 = "900";
        String zapDuration2 = "700";
        String zapDuration3 = "1500";
        String zapDurationSum = "3100"; // sum of zap durations is more than 80% of BROADCAST_DURATION
        String concepts = "catégories/divertissement,sous-genres/jeu";
        String averageTime = "1562864679";
        String device = "stb";

        List<Text> values = new ArrayList<Text>();
        values.add(new Text(beginZap1 + TAB + zapDuration1 + TAB + concepts + TAB + device ));
        values.add(new Text(beginZap2 + TAB + zapDuration2 + TAB + concepts + TAB + device ));
        values.add(new Text(beginZap3 + TAB + zapDuration3 + TAB + concepts + TAB + device ));

        reduceDriver.withInput(new Text(VOD_PROGRAM_KEY), values);
        reduceDriver.withCounter(Counters.NB_PROG_VIEW, 1);

        reduceDriver.runTest();

        Mockito.verify(mockMosWriter).open(Mockito.any());
        int nbCall = 0;
        nbCall += withSelected(AID, concepts, dayPeriodTimeslot, VOD, zapDurationSum, averageTime, device);
        Mockito.verify(mockMosWriter, Mockito.times(nbCall)).write(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any());
        Mockito.verify(mockMosWriter).close();
    }

    @Test
    public final void testVodNoConceptZapReducer() throws IOException, InterruptedException, ClassNotFoundException {
        String beginZap = "**********";
        String dayPeriodTimeslot = "4" + TAB + "3" + TAB + "5";
        String zapDuration = "2900"; // more than 80% of BROADCAST_DURATION
        String averageTime = "1562851679";
        String device = "stb";

        List<Text> values = new ArrayList<Text>();
        values.add(new Text(beginZap + TAB + zapDuration + TAB + "" + TAB + device  ));

        reduceDriver.withInput(new Text(VOD_PROGRAM_KEY), values);
        reduceDriver.withCounter(Counters.NB_PROG_VIEW, 1);

        reduceDriver.runTest();

        Mockito.verify(mockMosWriter).open(Mockito.any());
        int nbCall = 0;
        nbCall += withSelected(AID, "", dayPeriodTimeslot, VOD, zapDuration, averageTime, device);
        Mockito.verify(mockMosWriter, Mockito.times(nbCall)).write(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any());
        Mockito.verify(mockMosWriter).close();
    }

    @Test
    public final void testVodNoSupportTypeZapReducer() throws IOException, InterruptedException, ClassNotFoundException {
        String beginZap = "**********";
        String dayPeriodTimeslot = "4" + TAB + "3" + TAB + "5";
        String zapDuration = "2900"; // more than 80% of BROADCAST_DURATION
        String averageTime = "1562851679";
        String concepts = "catégories/divertissement,sous-genres/jeu";
        String device = "";

        List<Text> values = new ArrayList<Text>();
        values.add(new Text(beginZap + TAB + zapDuration + TAB + concepts + TAB + device ));

        reduceDriver.withInput(new Text(VOD_PROGRAM_KEY), values);
        reduceDriver.withCounter(Counters.NB_PROG_VIEW, 1);

        reduceDriver.runTest();

        Mockito.verify(mockMosWriter).open(Mockito.any());
        int nbCall = 0;
        nbCall += withSelected(AID, concepts, dayPeriodTimeslot, VOD, zapDuration, averageTime, "");
        Mockito.verify(mockMosWriter, Mockito.times(nbCall)).write(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any());
        Mockito.verify(mockMosWriter).close();
    }

    @Test
    public final void testVodBrodcastDurationNullZapReducer() throws IOException, InterruptedException, ClassNotFoundException {
        String beginZap = "**********";
        String dayPeriodTimeslot = "4" + TAB + "3" + TAB + "5";
        String zapDuration = "2000";
        String device = "stb";

        List<Text> values = new ArrayList<Text>();
        values.add(new Text(beginZap + TAB + zapDuration + TAB + "" + TAB + device ));

        String vodKeyDurationNull = AID + TAB + VOD + TAB + "0"
                + TAB + PROVIDER + TAB + CONTENT_ID + TAB + CONTENT_TITLE + TAB
                + EXTERNALENTERTAINMENTID + TAB + SEASONNAME + TAB + SERIESNAME + TAB  + OFFERNAME + TAB  + ZAP_DAY;
        reduceDriver.withInput(new Text(vodKeyDurationNull), values);
        reduceDriver.withCounter(Counters.NB_PROG_VIEW, 1);

        reduceDriver.runTest();
        Mockito.verify(mockMosWriter).open(Mockito.any());
        int nbCall = 0;
        nbCall += withSelected(AID, "", dayPeriodTimeslot, VOD, zapDuration, beginZap, device);
        Mockito.verify(mockMosWriter, Mockito.times(nbCall)).write(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any());
        Mockito.verify(mockMosWriter).close();

    }

    // #################### OTHER ##########################

    @Test
    public final void testOtherSingleZapReducer() throws IOException, InterruptedException, ClassNotFoundException {
        String beginZap = "**********";
        String zapDuration = "3600"; // equals to BROADCAST_DURATION
        String concepts = "catégories/divertissement,sous-genres/jeu";
        String device = "stb";

        List<Text> values = new ArrayList<Text>();
        values.add(new Text(beginZap + TAB + zapDuration + TAB + concepts + TAB + device ));

        String otherKey = AID + TAB + "OTHER" + TAB + BROADCAST_DURATION
                + TAB + PROVIDER + TAB + CONTENT_ID + TAB + CONTENT_TITLE + TAB + EXTERNALENTERTAINMENTID
                + TAB + SEASONNAME + TAB + SERIESNAME+ TAB + ZAP_DAY;
        reduceDriver.withInput(new Text(otherKey), values);
        reduceDriver.withCounter(Counters.ZAP_TOO_SHORT, 0);
        reduceDriver.runTest();
    }

    private int withSelected(String aid, String concepts, String dayPeriodTimeslot,
            String vodOrLive, String zapDurationSum, String averageTime, String device)
            throws IOException, InterruptedException {
        String offerName = null;
        if(vodOrLive.equals(LIVE)) offerName = LIVE;
        if(vodOrLive.equals(VOD)) offerName = OFFERNAME;

        String output = String.join(TAB, concepts, dayPeriodTimeslot, vodOrLive, zapDurationSum, averageTime, PROVIDER,
                CONTENT_ID, CONTENT_TITLE, EXTERNALENTERTAINMENTID, SEASONNAME, SERIESNAME, offerName, device);
        Mockito.verify(mockMosWriter).write("selected",aid, output, "selected/part");
        return 1;
    }

    private int withTooshort(String aid, String vodOrLive, String showDuration, String dayOfProgram,
            String beginZap, String zapDurationSum, String concepts, String OfferName, String supportType)
            throws IOException, InterruptedException {

        String key = String.join(TAB, aid, vodOrLive, showDuration, PROVIDER, CONTENT_ID, CONTENT_TITLE,
                EXTERNALENTERTAINMENTID, SEASONNAME, SERIESNAME, OfferName, dayOfProgram);
        String output = String.join(TAB, beginZap, zapDurationSum, concepts, supportType);
        Mockito.verify(mockMosWriter).write("tooshort",key, output, "tooshort/part");
        return 1;
    }
}
