package com.orange.profiling.ute.ravenne.transformer;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.PrintStream;
import java.net.URI;
import java.net.URISyntaxException;

import org.apache.hadoop.io.Text;
import org.apache.hadoop.mrunit.mapreduce.MapDriver;
import org.apache.log4j.BasicConfigurator;
import org.apache.log4j.Level;
import org.apache.log4j.Logger;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

public class TransformProfilingHashMapperTest {

    private URI runtimeRessourceFolder;

    MapDriver<Object, Text, Text, Text> mapDriver;
    private final ByteArrayOutputStream errContent = new ByteArrayOutputStream();
    private static final String TAB = "\t";

    @Before
    public void setUp() throws URISyntaxException {
        runtimeRessourceFolder = TransformProfilingHashMapperTest.class.getResource("/ute-ravenne-transformer/").toURI();
        final String inputPathMapping = runtimeRessourceFolder.resolve("in/mappingott/").getPath();

        TransformProfilingHashMapper inputMapper = new TransformProfilingHashMapper();

        mapDriver = MapDriver.newMapDriver(inputMapper);
        mapDriver.getConfiguration().set(MainTransformProfilingHash.MAPPING_DATA, inputPathMapping);
        BasicConfigurator.configure();
        Logger.getRootLogger().setLevel(Level.ERROR);
    }

    @Before
    public void setUpStreams() {
        System.setErr(new PrintStream(this.errContent));
    }

    @After
    public final void cleanUpStreams() {
        System.setErr(null);
    }

    @Test
    public final void testMapperOk() throws IOException {
        String aid = "*********";
        String concepts = "Genre Orange/émission/football/émission football,sujets/sport/compétitions,sujets/sport,sous-genres/sport,sujets/sport/football";
        String day = "4";
        String period = "3";
        String timeslot = "6";
        String vodorlive = "LIVE";
        String zapduration = "15";
        String zaptime = "**********";
        String provider = "111";
        String content_id = "id123";
        String title = "Title of program";
        String externalEntertainmentID = "episode-Title of serie and episode-season-X-episode-Y-2000";
        String seasonName = "Saison X";
        String serieName = "Serie X";
        String offerName = "LIVE";
        String device = "stb";

        String outputValue = aid + TAB + concepts + TAB + day + TAB + period + TAB + timeslot
                + TAB + vodorlive + TAB + zapduration + TAB + zaptime + TAB + provider
                + TAB + content_id + TAB + title + TAB + externalEntertainmentID + TAB + seasonName + TAB + serieName + TAB + offerName + TAB + device;


        mapDriver.withInput(new Text(),
                new Text(outputValue));

        mapDriver.withOutput(new Text(aid),
                new Text(outputValue));

        mapDriver.runTest();
    }

}