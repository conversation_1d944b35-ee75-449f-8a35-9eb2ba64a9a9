package com.orange.profiling.ute.ravenne.stat.countconcepts;

import java.io.IOException;

import org.apache.hadoop.io.Text;
import org.apache.hadoop.mrunit.mapreduce.MapDriver;
import org.apache.log4j.BasicConfigurator;
import org.apache.log4j.Level;
import org.apache.log4j.Logger;
import org.junit.Before;
import org.junit.Test;

public class PreviousCountConceptsMapperTest {

    private MapDriver<Object, Text, Text, Text> countConceptMapperDriver;

    @Before
    public void setUp() throws Exception {
        countConceptMapperDriver = MapDriver.newMapDriver(new PreviousCountConceptsMapper());

        BasicConfigurator.configure();
        Logger.getRootLogger().setLevel(Level.ERROR);
    }

    @Test
    public void testOk() throws IOException {
        String inputLine = "dt\tconcept/name\t12345";
        countConceptMapperDriver.withInput(new Text(), new Text(inputLine));

        String expectedKey = "dt|concept/name";
        String expectedValue = "Y\t12345";
        countConceptMapperDriver.withOutput(new Text(expectedKey), new Text(expectedValue));

        countConceptMapperDriver.runTest();

    }

    @Test
    public void testBadFormat() throws IOException {
        String inputLine = "dt\tconcept/name";
        countConceptMapperDriver.withInput(new Text(), new Text(inputLine));

        countConceptMapperDriver.runTest();

    }
}
