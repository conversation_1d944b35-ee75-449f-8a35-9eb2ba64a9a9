package com.orange.profiling.ute.ravenne.profiltype.profils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.PrintStream;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.List;

import org.apache.hadoop.io.Text;
import org.apache.hadoop.mrunit.mapreduce.ReduceDriver;
import org.apache.log4j.BasicConfigurator;
import org.apache.log4j.Level;
import org.apache.log4j.Logger;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.runners.MockitoJUnitRunner;

import com.orange.profiling.common.mapred.MosWriter;
import com.orange.profiling.common.utils.FieldsUtils;

@RunWith(MockitoJUnitRunner.class)
public class ProfilsReducerTest {
    private static final String TAB = FieldsUtils.TAB;

    private ReduceDriver<Text, Text, Text, Text> reduceDriver;
    private final ByteArrayOutputStream errContent = new ByteArrayOutputStream();

    @Mock
    private MosWriter mockMosWriter;

    private URI runtimeRessourceFolder;

    @Before
    public void setUp() throws URISyntaxException {

        runtimeRessourceFolder = ProfilsReducerTest.class.getResource("/ute-ravenne-profils/").toURI();
        ProfilsReducer profilReducer = new ProfilsReducer();
        profilReducer.setMosWriter(mockMosWriter);
        reduceDriver = ReduceDriver.newReduceDriver(profilReducer);

        String catalogProfilFile = runtimeRessourceFolder.resolve("in/catalogue/catalog_profils.csv").getPath();
        reduceDriver.getConfiguration().set(MainProfils.CATALOG_PROFILS, catalogProfilFile);

        BasicConfigurator.configure();
        Logger.getRootLogger().setLevel(Level.ERROR);
    }

    @Before
    public void setUpStreams() {
        System.setErr(new PrintStream(this.errContent));
    }

    @After
    public void cleanUpStreams() {
        System.setErr(null);
    }

    @Test
    public final void testAidValuesTasteboxReducer() throws IOException, InterruptedException, ClassNotFoundException {
        String compoAid = "*********-stb";
        String aid = "*********";
        String[] aidValues = {
                "VT"+TAB+"1"+TAB+"d2t3"+TAB+"700",
                "VT"+TAB+"2"+TAB+"d1t5"+TAB+"800",
                "VT"+TAB+"3"+TAB+"d1t5"+TAB+"600",
                "VT"+TAB+"4"+TAB+"d1t5"+TAB+"500",
                "VT"+TAB+"5"+TAB+"d2t3"+TAB+"400",
                "VT"+TAB+"6"+TAB+"d2t3"+TAB+"300",
                "VT"+TAB+"7"+TAB+"d1t5"+TAB+"200",
                "VT"+TAB+"8"+TAB+"d1t5"+TAB+"200",
                "VT"+TAB+"9"+TAB+"d1t5"+TAB+"200",
                "VT"+TAB+"10"+TAB+"d2t3"+TAB+"200",
                "VT"+TAB+"5"+TAB+"d1t5"+TAB+"600"
        };
        reduceDriver.withInput(new Text(compoAid), buildTextValues(aidValues));
        reduceDriver.withCounter(Counters.NB_AID_ALGO_VECTORTYPE, 1);
        reduceDriver.withCounter(Counters.NB_AID_ALGO_TOPCONCEPTS, 0);
        reduceDriver.withCounter(Counters.NB_AID_NO_ALGO, 0);
        reduceDriver.withCounter(Counters.NB_USER_WITH_PROFIL, 1);
        reduceDriver.withCounter(MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS, MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS+"5", 1);
        reduceDriver.withCounter(MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS, MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS+"2", 1);
        reduceDriver.withCounter(MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS, MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS+"1", 1);
        reduceDriver.withCounter(MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS, MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS+"3", 1);
        reduceDriver.withCounter(MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS, MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS+"4", 1);
        reduceDriver.withCounter(MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS, MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS+"7", 0);
        reduceDriver.withCounter(MainProfils.COUNTER_GROUP_NB_USER_HAVING_N_PROFILS,  "NB_FOYERS_HAVING_5_PROFILES", 1);
        reduceDriver.withCounter(MainProfils.COUNTER_GROUP_NB_USER_HAVING_N_PROFILS,  "NB_FOYERS_HAVING_4_PROFILES", 0);

        reduceDriver.runTest(false);

        Mockito.verify(mockMosWriter).open(Mockito.any());
        // le 39 corresponds aux appels pour la calcul des scores de chaque verticales (39 thématiques dans les données de test)
        int nbCall = 39;
        nbCall += withProfilType(aid, "1", "5");
        nbCall += withProfilType(aid, "2", "2");
        nbCall += withProfilType(aid, "3", "1");
        nbCall += withProfilType(aid, "4", "3");
        nbCall += withProfilType(aid, "5", "4");

        Mockito.verify(mockMosWriter, Mockito.times(nbCall)).write(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any());
        Mockito.verify(mockMosWriter).close();

    }


    @Test
    public final void testAidValuesTopConceptsTasteboxReducer() throws IOException, InterruptedException, ClassNotFoundException {
        String compoAid = "*********-stb";
        String aid = "*********";
        String[] aidValues = {
                "TC"+TAB+"1"+TAB+"d2t3"+TAB+"700",
                "TC"+TAB+"2"+TAB+"d1t5"+TAB+"800",
                "TC"+TAB+"3"+TAB+"d1t5"+TAB+"600",
                "TC"+TAB+"4"+TAB+"d1t5"+TAB+"500",
                "TC"+TAB+"5"+TAB+"d2t3"+TAB+"400",
                "TC"+TAB+"6"+TAB+"d2t3"+TAB+"300",
                "TC"+TAB+"7"+TAB+"d1t5"+TAB+"200",
                "TC"+TAB+"8"+TAB+"d1t5"+TAB+"200",
                "TC"+TAB+"9"+TAB+"d1t5"+TAB+"200",
                "TC"+TAB+"10"+TAB+"d2t3"+TAB+"200",
                "TC"+TAB+"5"+TAB+"d1t5"+TAB+"600"
        };
        reduceDriver.withInput(new Text(compoAid), buildTextValues(aidValues));
        reduceDriver.withCounter(Counters.NB_AID_ALGO_VECTORTYPE, 0);
        reduceDriver.withCounter(Counters.NB_AID_ALGO_TOPCONCEPTS, 1);
        reduceDriver.withCounter(Counters.NB_AID_NO_ALGO, 0);
        reduceDriver.withCounter(Counters.NB_USER_WITH_PROFIL, 1);
        reduceDriver.withCounter(MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS, MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS+"5", 1);
        reduceDriver.withCounter(MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS, MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS+"2", 1);
        reduceDriver.withCounter(MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS, MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS+"1", 1);
        reduceDriver.withCounter(MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS, MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS+"3", 1);
        reduceDriver.withCounter(MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS, MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS+"4", 1);
        reduceDriver.withCounter(MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS, MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS+"7", 0);
        reduceDriver.withCounter(MainProfils.COUNTER_GROUP_NB_USER_HAVING_N_PROFILS,  "NB_FOYERS_HAVING_5_PROFILES", 1);
        reduceDriver.withCounter(MainProfils.COUNTER_GROUP_NB_USER_HAVING_N_PROFILS,  "NB_FOYERS_HAVING_4_PROFILES", 0);

        reduceDriver.runTest(false);

        Mockito.verify(mockMosWriter).open(Mockito.any());
        // le 39 corresponds aux appels pour la calcul des scores de chaque verticales (39 thématiques dans les données de test)
        int nbCall = 39;
        nbCall += withProfilType(aid, "1", "5");
        nbCall += withProfilType(aid, "2", "2");
        nbCall += withProfilType(aid, "3", "1");
        nbCall += withProfilType(aid, "4", "3");
        nbCall += withProfilType(aid, "5", "4");

        Mockito.verify(mockMosWriter, Mockito.times(nbCall)).write(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any());
        Mockito.verify(mockMosWriter).close();

    }

    @Test
    public final void testAidValuesBothAlgoTasteboxReducer() throws IOException, InterruptedException, ClassNotFoundException {
        String compoAid = "*********-stb";
        String aid = "*********";
        String[] aidValues = {
                "VT"+TAB+"1"+TAB+"d2t3"+TAB+"700",
                "VT"+TAB+"2"+TAB+"d1t5"+TAB+"800",
                "TC"+TAB+"3"+TAB+"d1t5"+TAB+"600",
                "TC"+TAB+"4"+TAB+"d1t5"+TAB+"500",
                "VT"+TAB+"5"+TAB+"d2t3"+TAB+"400",
                "VT"+TAB+"6"+TAB+"d2t3"+TAB+"300",
                "TC"+TAB+"7"+TAB+"d1t5"+TAB+"200",
                "VT"+TAB+"8"+TAB+"d1t5"+TAB+"200",
                "TC"+TAB+"9"+TAB+"d1t5"+TAB+"200",
                "TC"+TAB+"10"+TAB+"d2t3"+TAB+"200",
                "VT"+TAB+"5"+TAB+"d1t5"+TAB+"600"
        };

        reduceDriver.withInput(new Text(compoAid), buildTextValues(aidValues));
        reduceDriver.withCounter(Counters.NB_AID_ALGO_VECTORTYPE, 1);
        reduceDriver.withCounter(Counters.NB_AID_ALGO_TOPCONCEPTS, 0);
        reduceDriver.withCounter(Counters.NB_AID_NO_ALGO, 0);
        reduceDriver.withCounter(Counters.NB_USER_WITH_PROFIL, 1);
        reduceDriver.withCounter(MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS, MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS+"5", 1);
        reduceDriver.withCounter(MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS, MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS+"2", 1);
        reduceDriver.withCounter(MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS, MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS+"1", 1);
        reduceDriver.withCounter(MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS, MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS+"6", 1);
        reduceDriver.withCounter(MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS, MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS+"8", 1);
        reduceDriver.withCounter(MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS, MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS+"3", 0);
        reduceDriver.withCounter(MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS, MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS+"4", 0);
        reduceDriver.withCounter(MainProfils.COUNTER_GROUP_NB_USER_HAVING_N_PROFILS,  "NB_FOYERS_HAVING_5_PROFILES", 1);
        reduceDriver.withCounter(MainProfils.COUNTER_GROUP_NB_USER_HAVING_N_PROFILS,  "NB_FOYERS_HAVING_4_PROFILES", 0);

        reduceDriver.runTest(false);

        Mockito.verify(mockMosWriter).open(Mockito.any());
        // le 39 corresponds aux appels pour la calcul des scores de chaque verticales (39 thématiques dans les données de test)
        int nbCall = 39;
        nbCall += withProfilType(aid, "1", "5");
        nbCall += withProfilType(aid, "2", "2");
        nbCall += withProfilType(aid, "3", "1");
        nbCall += withProfilType(aid, "4", "6");
        nbCall += withProfilType(aid, "5", "8");

        Mockito.verify(mockMosWriter, Mockito.times(nbCall)).write(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any());
        Mockito.verify(mockMosWriter).close();

    }

    @Test
    public final void testAidValuesNoAlgoTasteboxReducer() throws IOException, InterruptedException, ClassNotFoundException {
        String aid = "*********-stb";
        String[] aidValues = {
                "BAD"+TAB+"1"+TAB+"d2t3"+TAB+"700"
        };

        reduceDriver.withInput(new Text(aid), buildTextValues(aidValues));

        reduceDriver.withCounter(Counters.NB_AID_ALGO_VECTORTYPE, 0);
        reduceDriver.withCounter(Counters.NB_AID_ALGO_TOPCONCEPTS, 0);
        reduceDriver.withCounter(Counters.NB_AID_NO_ALGO, 1);
        reduceDriver.withCounter(Counters.NB_USER_WITH_PROFIL, 0);
        reduceDriver.withCounter(MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS, MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS+"7", 0);
        reduceDriver.withCounter(MainProfils.COUNTER_GROUP_NB_USER_HAVING_N_PROFILS,  "NB_FOYERS_HAVING_4_PROFILES", 0);

        reduceDriver.runTest(false);

        Mockito.verify(mockMosWriter).open(Mockito.any());
        // le 39 corresponds aux appels pour la calcul des scores de chaque verticales (39 thématiques dans les données de test)
        int nbCall = 39;
        Mockito.verify(mockMosWriter, Mockito.times(nbCall)).write(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any());
        Mockito.verify(mockMosWriter).close();

    }

    @Test
    public final void testVectorValuesTasteboxReducer() throws IOException, InterruptedException, ClassNotFoundException {
        String vector1 = "root/vector/concept1=10.0,root/vector/concept2=8.0";
        String vector2 = "root/vector/concept3=10.0,root/vector/concept4=8.0";
        String[] vectorValues = {
                "VT"+TAB+vector1+TAB+"1",
                "VT"+TAB+vector1+TAB+"2",
                "VT"+TAB+vector1+TAB+"1",
                "VT"+TAB+vector2+TAB+"1",
                "VT"+TAB+vector2+TAB+"1",
                "VT"+TAB+vector1+TAB+"2" ,
                "VT"+TAB+vector1+TAB+"1",
                "VT"+TAB+vector1+TAB+"3",
                "VT"+TAB+vector2+TAB+"4",
                "VT"+TAB+vector2+TAB+"4",
                "VT"+TAB+vector1+TAB+"1"
            };
        reduceDriver.withInput(new Text("VECTOR"), buildTextValues(vectorValues));

        reduceDriver.withCounter(Counters.NB_VECTOR, 2);
        reduceDriver.withCounter(MainProfils.COUNTER_GROUP_NB_VECTOR_BY_PROFIL,
                MainProfils.COUNTER_GROUP_NB_VECTOR_BY_PROFIL+"1", 2);
        reduceDriver.withCounter(MainProfils.COUNTER_GROUP_NB_VECTOR_BY_PROFIL,
                MainProfils.COUNTER_GROUP_NB_VECTOR_BY_PROFIL+"2", 1);
        reduceDriver.withCounter(MainProfils.COUNTER_GROUP_NB_VECTOR_BY_PROFIL,
                MainProfils.COUNTER_GROUP_NB_VECTOR_BY_PROFIL+"3", 1);
        reduceDriver.withCounter(MainProfils.COUNTER_GROUP_NB_VECTOR_BY_PROFIL,
                MainProfils.COUNTER_GROUP_NB_VECTOR_BY_PROFIL+"4", 1);

        reduceDriver.runTest(false);

        Mockito.verify(mockMosWriter).open(Mockito.any());
        int nbCall = 0;
        nbCall += withVector("VT",vector1, "1", 4);
        nbCall += withVector("VT",vector2, "1", 2);
        nbCall += withVector("VT",vector1, "2", 2);
        nbCall += withVector("VT",vector1, "3", 1);
        nbCall += withVector("VT",vector2, "4", 2);

        Mockito.verify(mockMosWriter, Mockito.times(nbCall)).write(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any());
        Mockito.verify(mockMosWriter).close();
    }

    @Test
    public final void testAidAndVectorValuesTasteboxReducer() throws IOException, InterruptedException, ClassNotFoundException {
        String compoAid1 = "*********-stb";
        String aid1 = "*********";
        String[] aid1Values = {
                "VT"+TAB+"1"+TAB+"d2t3"+TAB+"700",
                "VT"+TAB+"2"+TAB+"d1t5"+TAB+"800",
                "VT"+TAB+"1"+TAB+"d1t5"+TAB+"600",
                "VT"+TAB+"3"+TAB+"d1t5"+TAB+"500"
        };
        reduceDriver.withInput(new Text(compoAid1), buildTextValues(aid1Values));

        String compoAid2 = "987654321-stb";
        String aid2 = "987654321";
        String[] aid2Values = {
                "VT"+TAB+"2"+TAB+"d2t3"+TAB+"700",
                "VT"+TAB+"2"+TAB+"d1t5"+TAB+"800",
                "VT"+TAB+"4"+TAB+"d1t5"+TAB+"600",
                "VT"+TAB+"1"+TAB+"d1t5"+TAB+"500"
        };
        reduceDriver.withInput(new Text(compoAid2), buildTextValues(aid2Values));

        String compoAid3 = "987654321-stb";
        String aid3 = "987654321";
        String[] aid3Values = {
                "TC"+TAB+"3"+TAB+"d2t3"+TAB+"700",
                "TC"+TAB+"3"+TAB+"d1t5"+TAB+"800",
                "TC"+TAB+"2"+TAB+"d1t5"+TAB+"600",
                "TC"+TAB+"2"+TAB+"d1t5"+TAB+"500"
        };
        reduceDriver.withInput(new Text(compoAid3), buildTextValues(aid3Values));

        String vector1 = "root/vector/concept1=10.0,root/vector/concept2=8.0";
        String vector2 = "root/vector/concept2=10.0,root/vector/concept3=6.0";
        String vector3 = "root/vector/concept1=10.0,root/vector/concept4=5.0";
        String vectorTC = "root/vector/concept5=0.0,root/vector/concept5=0.0";
        String[] vectorValues = {
                "VT"+TAB+vector1+TAB+"1",
                "VT"+TAB+vector1+TAB+"2",
                "VT"+TAB+vector1+TAB+"1",
                "VT"+TAB+vector2+TAB+"1",
                "VT"+TAB+vector2+TAB+"3",
                "VT"+TAB+vector2+TAB+"1",
                "VT"+TAB+vector2+TAB+"1",
                "VT"+TAB+vector3+TAB+"4",
                "VT"+TAB+vector3+TAB+"4",
                "VT"+TAB+vector3+TAB+"3",
                "TC"+TAB+vectorTC+TAB+"2"
        };
        reduceDriver.withInput(new Text("VECTOR"), buildTextValues(vectorValues));
        reduceDriver.withCounter(Counters.NB_AID_ALGO_VECTORTYPE, 2);
        reduceDriver.withCounter(Counters.NB_AID_ALGO_TOPCONCEPTS, 1);
        reduceDriver.withCounter(Counters.NB_AID_NO_ALGO, 0);
        reduceDriver.withCounter(Counters.NB_USER_WITH_PROFIL, 3);
        reduceDriver.withCounter(MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS, MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS+"1", 2);
        reduceDriver.withCounter(MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS, MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS+"2", 3);
        reduceDriver.withCounter(MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS, MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS+"3", 2);
        reduceDriver.withCounter(MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS, MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS+"4", 1);
        reduceDriver.withCounter(MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS, MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS+"5", 0);
        reduceDriver.withCounter(MainProfils.COUNTER_GROUP_NB_USER_HAVING_N_PROFILS,  "NB_FOYERS_HAVING_2_PROFILES", 1);
        reduceDriver.withCounter(MainProfils.COUNTER_GROUP_NB_USER_HAVING_N_PROFILS,  "NB_FOYERS_HAVING_3_PROFILES", 2);
        reduceDriver.withCounter(MainProfils.COUNTER_GROUP_NB_USER_HAVING_N_PROFILS,  "NB_FOYERS_HAVING_4_PROFILES", 0);

        reduceDriver.withCounter(Counters.NB_VECTOR, 4);
        reduceDriver.withCounter(MainProfils.COUNTER_GROUP_NB_VECTOR_BY_PROFIL,
                MainProfils.COUNTER_GROUP_NB_VECTOR_BY_PROFIL+"1", 2);
        reduceDriver.withCounter(MainProfils.COUNTER_GROUP_NB_VECTOR_BY_PROFIL,
                MainProfils.COUNTER_GROUP_NB_VECTOR_BY_PROFIL+"2", 2);
        reduceDriver.withCounter(MainProfils.COUNTER_GROUP_NB_VECTOR_BY_PROFIL,
                MainProfils.COUNTER_GROUP_NB_VECTOR_BY_PROFIL+"3", 2);
        reduceDriver.withCounter(MainProfils.COUNTER_GROUP_NB_VECTOR_BY_PROFIL,
                MainProfils.COUNTER_GROUP_NB_VECTOR_BY_PROFIL+"4", 1);
        reduceDriver.withCounter(MainProfils.COUNTER_GROUP_NB_VECTOR_BY_PROFIL,
                MainProfils.COUNTER_GROUP_NB_VECTOR_BY_PROFIL+"5", 0);

        reduceDriver.runTest(false);

        Mockito.verify(mockMosWriter).open(Mockito.any());
        // les appels aussi des scores des verticales, 3 aid * 39 verticales = 117
        int nbCall = 117;
        nbCall += withVector("VT",vector1, "1", 2);
        nbCall += withVector("VT",vector1, "2", 1);
        nbCall += withVector("VT",vector2, "1", 3);
        nbCall += withVector("VT",vector2, "3", 1);
        nbCall += withVector("VT",vector3, "3", 1);
        nbCall += withVector("VT",vector3, "4", 2);
        nbCall += withVector("TC",vectorTC, "2", 1);
        nbCall += withProfilType(aid1, "1", "1");
        nbCall += withProfilType(aid1, "2", "2");
        nbCall += withProfilType(aid1, "3", "3");
        nbCall += withProfilType(aid2, "1", "2");
        nbCall += withProfilType(aid2, "2", "4");
        nbCall += withProfilType(aid2, "3", "1");
        nbCall += withProfilType(aid3, "1", "3");
        nbCall += withProfilType(aid3, "2", "2");
        Mockito.verify(mockMosWriter, Mockito.times(nbCall)).write(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any());
        Mockito.verify(mockMosWriter).close();
    }

    @Test
    public final void testAidValuesScoresVerticaleTasteboxReducer() throws IOException, InterruptedException, ClassNotFoundException {
        String compoAid = "*********-stb";
        String aid = "*********";
        String[] aidValues = {
                "VT"+TAB+"1"+TAB+"d2t3"+TAB+"700",
                "VT"+TAB+"2"+TAB+"d2t5"+TAB+"800",
                "VT"+TAB+"3"+TAB+"d3t5"+TAB+"600",
                "VT"+TAB+"4"+TAB+"d4t5"+TAB+"500",
        };

        String outputDirVertical = runtimeRessourceFolder.resolve("outVert").getPath();
        reduceDriver.getConfiguration().set("outputDirVertical", outputDirVertical);

        reduceDriver.withInput(new Text(compoAid), buildTextValues(aidValues));

        reduceDriver.withCounter(Counters.NB_AID_ALGO_VECTORTYPE, 1);
        reduceDriver.withCounter(Counters.NB_AID_ALGO_TOPCONCEPTS, 0);
        reduceDriver.withCounter(Counters.NB_AID_NO_ALGO, 0);
        reduceDriver.withCounter(Counters.NB_USER_WITH_PROFIL, 1);
        reduceDriver.withCounter(MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS, MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS+"2", 1);
        reduceDriver.withCounter(MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS, MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS+"1", 1);
        reduceDriver.withCounter(MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS, MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS+"3", 1);
        reduceDriver.withCounter(MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS, MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS+"4", 1);
        reduceDriver.withCounter(MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS, MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS+"7", 0);
        reduceDriver.withCounter(MainProfils.COUNTER_GROUP_NB_USER_HAVING_N_PROFILS,  "NB_FOYERS_HAVING_4_PROFILES", 1);

        reduceDriver.runTest(false);

        Mockito.verify(mockMosWriter).open(Mockito.any());
        int nbCall = 0;
        nbCall += withProfilType(aid, "1", "2");
        nbCall += withProfilType(aid, "2", "1");
        nbCall += withProfilType(aid, "3", "3");
        nbCall += withProfilType(aid, "4", "4");
        nbCall += ScoreVertical(aid, "divertissem", "0.0000", outputDirVertical);
        nbCall += ScoreVertical(aid, "gros_bras", "0.0000", outputDirVertical);
        nbCall += ScoreVertical(aid, "historia", "0.0000", outputDirVertical);
        nbCall += ScoreVertical(aid, "zero_neuron", "0.0000", outputDirVertical);
        nbCall += ScoreVertical(aid, "anticipatio", "0.0000", outputDirVertical);
        nbCall += ScoreVertical(aid, "dram_traged", "0.0000", outputDirVertical);
        nbCall += ScoreVertical(aid, "debat_idee", "0.0000", outputDirVertical);
        nbCall += ScoreVertical(aid, "medecin", "0.0000", outputDirVertical);
        nbCall += ScoreVertical(aid, "scienc_tech", "0.0000", outputDirVertical);
        nbCall += ScoreVertical(aid, "monstr_crea", "0.0000", outputDirVertical);
        nbCall += ScoreVertical(aid, "susp_frisso", "0.0000", outputDirVertical);
        nbCall += ScoreVertical(aid, "fan_tvachat", "0.0000", outputDirVertical);
        nbCall += ScoreVertical(aid, "horiz_loint", "0.0000", outputDirVertical);
        nbCall += ScoreVertical(aid, "evnmnt", "0.0000", outputDirVertical);
        nbCall += ScoreVertical(aid, "nat_vie_sau", "0.0000", outputDirVertical);
        nbCall += ScoreVertical(aid, "fan_foot", "0.0000", outputDirVertical);
        nbCall += ScoreVertical(aid, "rire_amitie", "0.0000", outputDirVertical);
        nbCall += ScoreVertical(aid, "aventure", "0.0000", outputDirVertical);
        nbCall += ScoreVertical(aid, "fan_jeutv", "0.0000", outputDirVertical);
        nbCall += ScoreVertical(aid, "fan_rugby", "0.0000", outputDirVertical);
        nbCall += ScoreVertical(aid, "cinevore", "0.0000", outputDirVertical);
        nbCall += ScoreVertical(aid, "fan_velo", "0.0000", outputDirVertical);
        nbCall += ScoreVertical(aid, "curiosite", "0.0000", outputDirVertical);
        nbCall += ScoreVertical(aid, "divers", "0.0000", outputDirVertical);
        nbCall += ScoreVertical(aid, "fiction_man", "0.0000", outputDirVertical);
        nbCall += ScoreVertical(aid, "fan_basket", "0.0000", outputDirVertical);
        nbCall += ScoreVertical(aid, "art_culture", "0.0000", outputDirVertical);
        nbCall += ScoreVertical(aid, "rev_realite", "0.0000", outputDirVertical);
        nbCall += ScoreVertical(aid, "serie_mania", "0.2692", outputDirVertical);
        nbCall += ScoreVertical(aid, "info_addict", "0.3077", outputDirVertical);
        nbCall += ScoreVertical(aid, "suj_societe", "0.2308", outputDirVertical);
        nbCall += ScoreVertical(aid, "fan_sport", "0.1923", outputDirVertical);
        nbCall += ScoreVertical(aid, "fan_animati", "0.0000", outputDirVertical);
        nbCall += ScoreVertical(aid, "kids", "0.0000", outputDirVertical);
        nbCall += ScoreVertical(aid, "lyrics_musi", "0.0000", outputDirVertical);
        nbCall += ScoreVertical(aid, "super_heros", "0.0000", outputDirVertical);
        nbCall += ScoreVertical(aid, "rire_comedi", "0.0000", outputDirVertical);
        nbCall += ScoreVertical(aid, "venge_justi", "0.0000", outputDirVertical);
        nbCall += ScoreVertical(aid, "amour_seduc", "0.0000", outputDirVertical);

        Mockito.verify(mockMosWriter, Mockito.times(nbCall)).write(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any());
        Mockito.verify(mockMosWriter).close();

    }

    @Test
    public final void testAidOttValuesTasteboxReducer() throws IOException, InterruptedException, ClassNotFoundException {
        String compoAid = "*********-mobile";
        String aid = "*********";
        String device = "mobile";
        String[] aidValues = {
                "VT"+TAB+"1"+TAB+"d2t3"+TAB+"700",
                "VT"+TAB+"2"+TAB+"d1t5"+TAB+"800",
                "VT"+TAB+"3"+TAB+"d1t5"+TAB+"600",
                "VT"+TAB+"4"+TAB+"d1t5"+TAB+"500",
                "VT"+TAB+"5"+TAB+"d2t3"+TAB+"400",
                "VT"+TAB+"6"+TAB+"d2t3"+TAB+"300",
                "VT"+TAB+"7"+TAB+"d1t5"+TAB+"200",
                "VT"+TAB+"8"+TAB+"d1t5"+TAB+"200",
                "VT"+TAB+"9"+TAB+"d1t5"+TAB+"200",
                "VT"+TAB+"10"+TAB+"d2t3"+TAB+"200",
                "VT"+TAB+"5"+TAB+"d1t5"+TAB+"600"
        };
        reduceDriver.withInput(new Text(compoAid), buildTextValues(aidValues));
        reduceDriver.withCounter(Counters.NB_AID_ALGO_VECTORTYPE, 1);
        reduceDriver.withCounter(Counters.NB_AID_ALGO_TOPCONCEPTS, 0);
        reduceDriver.withCounter(Counters.NB_AID_NO_ALGO, 0);
        reduceDriver.withCounter(Counters.NB_USER_WITH_PROFIL, 1);
        reduceDriver.withCounter(MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS, MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS+"5", 1);
        reduceDriver.withCounter(MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS, MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS+"2", 1);
        reduceDriver.withCounter(MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS, MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS+"1", 1);
        reduceDriver.withCounter(MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS, MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS+"3", 1);
        reduceDriver.withCounter(MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS, MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS+"4", 1);
        reduceDriver.withCounter(MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS, MainProfils.COUNTER_GROUP_NB_USER_BY_PROFILS+"7", 0);
        reduceDriver.withCounter(MainProfils.COUNTER_GROUP_NB_USER_HAVING_N_PROFILS,  "NB_FOYERS_HAVING_5_PROFILES", 1);
        reduceDriver.withCounter(MainProfils.COUNTER_GROUP_NB_USER_HAVING_N_PROFILS,  "NB_FOYERS_HAVING_4_PROFILES", 0);

        reduceDriver.runTest(false);

        Mockito.verify(mockMosWriter).open(Mockito.any());
        int nbCall = 0;
        nbCall += withProfilTypeOtt(aid, "1", "5", device);
        nbCall += withProfilTypeOtt(aid, "2", "2", device);
        nbCall += withProfilTypeOtt(aid, "3", "1", device);
        nbCall += withProfilTypeOtt(aid, "4", "3", device);
        nbCall += withProfilTypeOtt(aid, "5", "4", device);

        Mockito.verify(mockMosWriter, Mockito.times(nbCall)).write(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any());
        Mockito.verify(mockMosWriter).close();

    }

    private List<Text> buildTextValues(String[] values) {
        List<Text> textValues = new ArrayList<Text>();
        for(String value: values) {
            textValues.add(new Text(value));
        }
        return textValues;
    }

    private int withProfilType(String aid, String rank, String profilId)
            throws IOException, InterruptedException {

        String output = "th_profiltype0"+rank+ TAB + profilId;
        Mockito.verify(mockMosWriter).write("profils",aid, output, "profils/part");
        return 1;
    }

    private int withVector(String algo, String vector, String profilId, int nb)
            throws IOException, InterruptedException {

        String algoVector = algo + FieldsUtils.SEMICOLON+vector;
        Mockito.verify(mockMosWriter).write("vectors",algoVector, profilId+TAB+nb, "vectors/part");
        return 1;
    }

    private int ScoreVertical(String aid, String verticalName, String score, String outputDirVert)
            throws IOException, InterruptedException {

        String output = "th_vert_"+verticalName+ TAB + score;
        Mockito.verify(mockMosWriter).write("scoresVertical",aid, output, outputDirVert + "/" + "scoresVertical/part");
        return 1;
    }

    private int withProfilTypeOtt(String aid, String rank, String profilId, String device)
            throws IOException, InterruptedException {

        String output = "th_profiltype_"+ device + "0" + rank + TAB + profilId;
        Mockito.verify(mockMosWriter).write("profilsOtt",aid, output, "profilsOtt/part");
        return 1;
    }
}
