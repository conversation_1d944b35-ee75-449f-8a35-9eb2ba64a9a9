package com.orange.profiling.ute.ravenne.profiltype.profils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.PrintStream;
import java.net.URI;
import java.net.URISyntaxException;
import org.apache.hadoop.io.LongWritable;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mrunit.mapreduce.MapDriver;
import org.apache.log4j.BasicConfigurator;
import org.apache.log4j.Level;
import org.apache.log4j.Logger;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

import com.orange.profiling.common.utils.FieldsUtils;

/**
 * <AUTHOR>
 */
public class TasteboxMapperTest {
    private static final String TAB = FieldsUtils.TAB;

    private URI runtimeRessourceFolder;

    MapDriver<Object, Text, Text, Text> tasteboxMapDriver;

    private final ByteArrayOutputStream errContent = new ByteArrayOutputStream();

    @Before
    public void setUp() throws URISyntaxException {
        runtimeRessourceFolder = TasteboxMapperTest.class.getResource("/ute-ravenne-profils/").toURI();

        TasteboxMapper tasteboxMapper = new TasteboxMapper();
        tasteboxMapDriver = MapDriver.newMapDriver(tasteboxMapper);

        String catalogProfilFile = runtimeRessourceFolder.resolve("in/catalogue/catalog_profils.csv").getPath();
        String catalogProfilVectorFile = runtimeRessourceFolder.resolve("in/catalogue/catalog_profils_vecteur.json").getPath();
        String catalogCombiCoeur = runtimeRessourceFolder.resolve("in/catalogue/scoreLTconf.json").getPath();

        tasteboxMapDriver.getConfiguration().set(MainProfils.CATALOG_PROFILS, catalogProfilFile);
        tasteboxMapDriver.getConfiguration().set(MainProfils.CATALOG_PROFILVECTORS, catalogProfilVectorFile);
        tasteboxMapDriver.getConfiguration().set(MainProfils.CATALOG_COMBICOEUR, catalogCombiCoeur);

        BasicConfigurator.configure();
        Logger.getRootLogger().setLevel(Level.ERROR);
    }

    @Before
    public void setUpStreams() {
        System.setErr(new PrintStream(this.errContent));
    }

    @After
    public void cleanUpStreams() {
        System.setErr(null);
    }

    @Test
    public void testTasteboxMapperOneMatch() throws IOException {
        // match on profile 4 : 1.0
        String weightedConcept1 = "sujets/rugby=10, sujets/ovalie=10, catégories/sport=10";
        String matchingRule1 = "catégories/sport=10.0";

        tasteboxMapDriver.withInput(new LongWritable(),
                new Text("123456789" + TAB + weightedConcept1 + TAB + "d1t5" + TAB + "8"));

        tasteboxMapDriver.withOutput(new Text("123456789"), new Text("VT"+TAB+"4"+TAB+"d1t5"+TAB+"8"));
        tasteboxMapDriver.withOutput(new Text("VECTOR"), new Text("VT"+TAB+matchingRule1+TAB+"4"));
        tasteboxMapDriver.runTest();
    }

    @Test
    public void testTasteboxMapperMoreMatch() throws IOException {
        // match on profile 4 : 1.0
        String weightedConcept1 = "sujets/rugby=10, sujets/ovalie=10, catégories/sport=10";
        String matchingRuleProfil4 = "catégories/sport=10.0";

        // match on profile 1
        String weightedConcept2 = "catégories/série=10,genres/action=8,sous-genres/science-fiction=10";
        // Match many rule
        // On profil1, three rules (maybe more) are verified, but we keep the best similarity
        // Profil 23 and 26 also match
        String matchingRuleProfil1 = "catégories/série=10.0";
        String matchingRuleProfil23 = "genres/action=10.0";
        String matchingRuleProfil26 = "catégories/série=10.0,sous-genres/science-fiction=10.0";


        tasteboxMapDriver.withInput(new LongWritable(),
                new Text("123456789" + TAB + weightedConcept1+","+weightedConcept2 + TAB + "d1t5" + TAB + "8"));
        tasteboxMapDriver.withOutput(new Text("123456789"), new Text("VT"+TAB+"4"+TAB+"d1t5"+TAB+"8"));
        tasteboxMapDriver.withOutput(new Text("123456789"), new Text("VT"+TAB+"1"+TAB+"d1t5"+TAB+"8"));
        tasteboxMapDriver.withOutput(new Text("123456789"), new Text("VT"+TAB+"23"+TAB+"d1t5"+TAB+"8"));
        tasteboxMapDriver.withOutput(new Text("123456789"), new Text("VT"+TAB+"26"+TAB+"d1t5"+TAB+"8"));
        tasteboxMapDriver.withOutput(new Text("VECTOR"), new Text("VT"+TAB+matchingRuleProfil4+TAB+"4"));
        tasteboxMapDriver.withOutput(new Text("VECTOR"), new Text("VT"+TAB+matchingRuleProfil1+TAB+"1"));
        tasteboxMapDriver.withOutput(new Text("VECTOR"), new Text("VT"+TAB+matchingRuleProfil23+TAB+"23"));
        tasteboxMapDriver.withOutput(new Text("VECTOR"), new Text("VT"+TAB+matchingRuleProfil26+TAB+"26"));

        tasteboxMapDriver.withCounter(Counters.NB_PROFIL, 4);
        tasteboxMapDriver.withCounter(Counters.NB_PROFIL_ALGO_VECTORTYPE, 4);
        tasteboxMapDriver.withCounter(Counters.NB_PROFIL_ALGO_TOPCONCEPTS, 0);
        tasteboxMapDriver.withCounter(Counters.NO_PROFIL_FOUND, 0);

        tasteboxMapDriver.runTest(false);;
    }


    @Test
    public void testTasteboxMapperTopConceptsAllMatch() throws IOException {
        // match on profile 3

        String weightedConcept1 = "catégories/information=10, catégories/magazine=6, sujets/société=7";
        String matchingRule1 = "catégories/information=0.0,catégories/magazine=0.0,sujets/société=0.0";

        tasteboxMapDriver.withInput(new LongWritable(),
                new Text("123456789" + TAB + weightedConcept1 + TAB + "d1t5" + TAB + "8"));

        tasteboxMapDriver.withOutput(new Text("123456789"), new Text("TC"+TAB+"3"+TAB+"d1t5"+TAB+"8"));
        tasteboxMapDriver.withOutput(new Text("VECTOR"), new Text("TC"+TAB+matchingRule1+TAB+"3"));

        tasteboxMapDriver.withCounter(Counters.NB_PROFIL, 1);
        tasteboxMapDriver.withCounter(Counters.NB_PROFIL_ALGO_VECTORTYPE, 0);
        tasteboxMapDriver.withCounter(Counters.NB_PROFIL_ALGO_TOPCONCEPTS, 1);
        tasteboxMapDriver.withCounter(Counters.NO_PROFIL_FOUND, 0);

        tasteboxMapDriver.runTest();
    }

    @Test
    public void testTasteboxMapperTopConceptsTwoMatch() throws IOException {
        // match on profile 3

        String weightedConcept1 = "sujets/actualité=10, catégories/magazine=6, sujets/société=7";
        String matchingRule1 = "catégories/information=0.0,catégories/magazine=0.0,sujets/société=0.0";

        tasteboxMapDriver.withInput(new LongWritable(),
                new Text("123456789" + TAB + weightedConcept1 + TAB + "d1t5" + TAB + "8"));

        tasteboxMapDriver.withOutput(new Text("123456789"), new Text("TC"+TAB+"3"+TAB+"d1t5"+TAB+"8"));
        tasteboxMapDriver.withOutput(new Text("VECTOR"), new Text("TC"+TAB+matchingRule1+TAB+"3"));

        tasteboxMapDriver.withCounter(Counters.NB_PROFIL, 1);
        tasteboxMapDriver.withCounter(Counters.NB_PROFIL_ALGO_VECTORTYPE, 0);
        tasteboxMapDriver.withCounter(Counters.NB_PROFIL_ALGO_TOPCONCEPTS, 1);
        tasteboxMapDriver.withCounter(Counters.NO_PROFIL_FOUND, 0);

        tasteboxMapDriver.runTest();
    }

    @Test
    public void testTasteboxMapperTopConceptsTwoMatchButNotTheFirst() throws IOException {
        // not match on profile 3

        String weightedConcept1 = "catégories/information=10, sujets/actualités=6, sujets/société=3";

        tasteboxMapDriver.withInput(new LongWritable(),
                new Text("123456789" + TAB + weightedConcept1 + TAB + "d1t5" + TAB + "8"));

        tasteboxMapDriver.withCounter(Counters.NB_PROFIL, 0);
        tasteboxMapDriver.withCounter(Counters.NB_PROFIL_ALGO_VECTORTYPE, 0);
        tasteboxMapDriver.withCounter(Counters.NB_PROFIL_ALGO_TOPCONCEPTS, 0);
        tasteboxMapDriver.withCounter(Counters.NO_PROFIL_FOUND, 1);

        tasteboxMapDriver.runTest();
    }

    @Test
    public void testTasteboxMapperTopConceptsOneMatch() throws IOException {
        // not match on profile 3

        String weightedConcept1 = "catégories/information=10, catégories/magazin=6, sujets/sociéty=3";

        tasteboxMapDriver.withInput(new LongWritable(),
                new Text("123456789" + TAB + weightedConcept1 + TAB + "d1t5" + TAB + "8"));

        tasteboxMapDriver.withCounter(Counters.NB_PROFIL, 0);
        tasteboxMapDriver.withCounter(Counters.NB_PROFIL_ALGO_VECTORTYPE, 0);
        tasteboxMapDriver.withCounter(Counters.NB_PROFIL_ALGO_TOPCONCEPTS, 0);
        tasteboxMapDriver.withCounter(Counters.NO_PROFIL_FOUND, 1);

        tasteboxMapDriver.runTest();
    }

    @Test
    public void testTasteboxTooShortMapper() throws IOException {
        tasteboxMapDriver.withInput(new LongWritable(),
                new Text("123456789"+TAB+"sujets/rugby=10"+TAB+"sujets/ovalie=10, catégories/sport=10"));

        tasteboxMapDriver.withCounter(Counters.NB_PROFIL, 0);
        tasteboxMapDriver.withCounter(Counters.NB_PROFIL_ALGO_VECTORTYPE, 0);
        tasteboxMapDriver.withCounter(Counters.NB_PROFIL_ALGO_TOPCONCEPTS, 0);
        tasteboxMapDriver.withCounter(Counters.BAD_AID_CSV_LENGTH, 1);

        tasteboxMapDriver.runTest();
    }

    @Test
    public void testTasteboxNoFoundMapper() throws IOException {
        tasteboxMapDriver.withInput(new LongWritable(),
                new Text("123456789"+TAB+"sujets/rugby:10, sujets/ovalie:10"+TAB+"d1t5"+TAB+"9"));

        tasteboxMapDriver.withCounter(Counters.NB_PROFIL, 0);
        tasteboxMapDriver.withCounter(Counters.NB_PROFIL_ALGO_VECTORTYPE, 0);
        tasteboxMapDriver.withCounter(Counters.NB_PROFIL_ALGO_TOPCONCEPTS, 0);
        tasteboxMapDriver.withCounter(Counters.NO_PROFIL_FOUND, 1);

        tasteboxMapDriver.runTest();
    }

}
