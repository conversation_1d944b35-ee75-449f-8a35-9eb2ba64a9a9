package com.orange.profiling.ute.ravenne.ponderation;

import org.junit.Test;

import java.net.URI;
import java.net.URISyntaxException;

import static org.junit.Assert.assertEquals;

public class ConditionalConceptsFilterTest {

    public URI runtimeRessourceFolder;

    @Test
    public final void testFilter1() throws URISyntaxException {

        //conceptFilterMappingTable contains audience/pour les tout-petits:type/série, so type/série should be removed if there is already audience/pour les tout-petits as concept
        runtimeRessourceFolder = ConditionalConceptsFilterTest.class.getResource("/ute-ravenne-ponderation/").toURI();
        final String conceptFilterMappingTablePath = runtimeRessourceFolder.resolve("in/conceptFilterMappingTable").getPath();

        String inputConcepts = "audience/pour les tout-petits,genres/aventure,thèmes/entraide et solidarité,type/série,genres/animation";

        ConditionalConceptsFilter conditionalConceptsFilter = new ConditionalConceptsFilter(conceptFilterMappingTablePath);
        String outputConcepts = conditionalConceptsFilter.filter(inputConcepts);

        String exceptedOutputConcepts = "audience/pour les tout-petits,genres/aventure,thèmes/entraide et solidarité,genres/animation";

        assertEquals(exceptedOutputConcepts, outputConcepts);
    }

    @Test
    public final void testFilter2() throws URISyntaxException {

        //conceptFilterMappingTable contains audience/pour enfants:type/série, so type/série should be removed if there is already audience/pour enfants as concept
        runtimeRessourceFolder = ConditionalConceptsFilterTest.class.getResource("/ute-ravenne-ponderation/").toURI();
        final String conceptFilterMappingTablePath = runtimeRessourceFolder.resolve("in/conceptFilterMappingTable").getPath();

        String inputConcepts = "personnages/animaux/chat,ambiance et ton/humoristique,type/série,thèmes/chasse à l'homme et poursuite,audience/pour enfants,genres/dessin animé,personnages/animaux,ambiance et ton/burlesque,genres/animation,personnages/animaux/rongeurs";

        ConditionalConceptsFilter conditionalConceptsFilter = new ConditionalConceptsFilter(conceptFilterMappingTablePath);
        String outputConcepts = conditionalConceptsFilter.filter(inputConcepts);

        String exceptedOutputConcepts = "personnages/animaux/chat,ambiance et ton/humoristique,thèmes/chasse à l'homme et poursuite,audience/pour enfants,genres/dessin animé,personnages/animaux,ambiance et ton/burlesque,genres/animation,personnages/animaux/rongeurs";

        assertEquals(exceptedOutputConcepts, outputConcepts);
    }


    @Test
    public final void testFilter3() throws URISyntaxException {


        runtimeRessourceFolder = ConditionalConceptsFilterTest.class.getResource("/ute-ravenne-ponderation/").toURI();
        final String conceptFilterMappingTablePath = runtimeRessourceFolder.resolve("in/conceptFilterMappingTableOneToMany").getPath();

        String inputConcepts = "personnages/animaux/chat,ambiance et ton/humoristique,type/série,thèmes/chasse à l'homme et poursuite,audience/pour enfants,genres/dessin animé,personnages/animaux,ambiance et ton/burlesque,genres/animation,personnages/animaux/rongeurs";

        ConditionalConceptsFilter conditionalConceptsFilter = new ConditionalConceptsFilter(conceptFilterMappingTablePath);
        String outputConcepts = conditionalConceptsFilter.filter(inputConcepts);

        String exceptedOutputConcepts = "personnages/animaux/chat,ambiance et ton/humoristique,thèmes/chasse à l'homme et poursuite,audience/pour enfants,genres/dessin animé,personnages/animaux,genres/animation,personnages/animaux/rongeurs";

        assertEquals(exceptedOutputConcepts, outputConcepts);
    }

}
