package com.orange.profiling.ute.ravenne.genetic.dao;

import static org.junit.Assert.*;

import java.util.Arrays;
import java.util.Collection;

import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.Parameterized;

@RunWith(Parameterized.class)
public class TimeboxFilterTest {
    @Parameterized.Parameter(0)
    public Integer processDay;
    @Parameterized.Parameter(1)
    public String timebox;
    @Parameterized.Parameter(2)
    public Boolean expected;
    @Parameterized.Parameter(3)
    public Boolean isOptoutAid;

    /** Generates Test Data
    *
    * @return data to use in test
    */
    @Parameterized.Parameters(name = "{index}: TimeboxFilter for processDay = {0} with timebox = {1} and isOptoutAid = {3} should return {2}")
    public static Collection<Object[]> data() {
        Object[][] data = new Object[][]{
            { 0, "d0p3od", true, false }, { 0, "d1p3od", false, false }, { 0, "d0p1od", true, false }, { 0, "d1p1od", false, false },
            { 1, "d1p3od", true, false }, { 1, "d2p3od", false, false }, { 1, "d1p2od", true, false }, { 1, "d2p2od", false, false },
            { 2, "d2p3od", true, false }, { 2, "d3p3od", false, false }, { 2, "d2p2od", true, false }, { 2, "d3p2od", false, false },
            { 3, "d3p3od", true, false }, { 3, "d4p4od", false, false }, { 3, "d3p3od", true, false }, { 3, "d4p3od", false, false },
            { 8, "d8p8od", true, false }, { 8, "d7p7od", false, false },                
            { 0, "d0t3", true, false }, { 0, "d1t3", false, false }, { 0, "d0p1", true, false }, { 0, "d1p1", false, false },
            { 0, "d0", true, false }, { 0, "d1", false, false }, { 0, "w", true, false }, { 0, "d0h5", true, false }, { 0, "g0", false, false },
            { 0, "d0od", true, false }, { 0, "d7od", false, false }, { 0, "wod", true, false },
            { 1, "d1t3", true, false }, { 1, "d2t3", false, false }, { 1, "d1p2", true, false }, { 1, "d2p2", false, false },
            { 1, "d1", true, false }, { 1, "d2", false, false }, { 1, "w", true, false }, { 1, "d1h5", true, false }, { 1, "b1", false, false },
            { 1, "d1od", true, false }, { 1, "d7od", false, false }, { 1, "wod", true, false },
            { 2, "d2t3", true, false }, { 2, "d3t3", false, false }, { 2, "d2p2", true, false }, { 2, "d3p2", false, false },
            { 2, "d2", true, false }, { 2, "d3", false, false }, { 2, "w", true, false }, { 2, "d2h5", true, false }, { 2, "wt2", false, false },
            { 2, "wt2", true, true },  { 2, "wt5", true, true }, { 2, "wt10", true, true },
            { 2, "wp2", true, true },  { 2, "wp5", true, true }, { 2, "wp1", true, true },
            { 2, "t2", false, true },  { 2, "p5", false, true }, { 2, "t10", false, true },
            { 2, "d2od", true, false }, { 2, "d1od", false, false }, { 2, "wod", true, false },
            { 3, "d3t3", true, false }, { 3, "d4t4", false, false }, { 3, "d3p3", true, false }, { 3, "d4p3", false, false },
            { 3, "d3", true, false }, { 3, "d4", false, false }, { 3, "w", true, false }, { 3, "d3h5", true, false }, { 3, "p3", false, false },
            { 3, "d3od", true, false }, { 3, "d2od", false, false }, { 3, "wod", true, false },
            { 4, "d4t3", true, false }, { 4, "d5t3", false, false }, { 4, "d4p4", true, false }, { 4, "d5p2", false, false },
            { 4, "d4", true, false }, { 4, "d5", false, false }, { 4, "w", true, false }, { 4, "d4h5", true, false }, { 4, "s4", false, false },
            { 4, "d4od", true, false }, { 4, "d3od", false, false }, { 4, "wod", true, false },
            { 5, "d5t3", true, false }, { 5, "d6t5", false, false }, { 5, "d5p2", true, false }, { 5, "d6p5", false, false },
            { 5, "d5", true, false }, { 5, "d6", false, false }, { 5, "w", true, false }, { 5, "d5h5", true, false }, { 5, "k5", false, false },
            { 5, "d5od", true, false }, { 5, "d4od", false, false }, { 5, "wod", true, false },
            { 6, "d6t3", true, false }, { 6, "d7t3", false, false }, { 6, "d6p2", true, false }, { 6, "d7p7", false, false },
            { 6, "d6", true, false }, { 6, "d7", false, false }, { 6, "w", true, false }, { 6, "d6h5", true, false }, { 6, "l6", false, false },
            { 6, "d6od", true, false }, { 6, "d5od", false, false }, { 6, "wod", true, false },
            { 7, "d7t3", true, false }, { 7, "d0t3", false, false }, { 7, "d7p2", true, false }, { 7, "d0p1", false, false },
            { 7, "d7", true, false }, { 7, "d0", false, false }, { 7, "w", true, false }, { 7, "d7h5", true, false }, { 7, "m7", false, false },
            { 7, "d7od", true, false }, { 7, "d6od", false, false }, { 7, "wod", true, false },
            { 8, "d8t3", true, false }, { 8, "d1t3", false, false }, { 8, "d8p2", true, false }, { 8, "d1p1", false, false },
            { 8, "d8", true, false }, { 8, "d1", false, false }, { 8, "w", true, false }, { 8, "d8h5", true, false }, { 8, "v8", false, false },
            { 8, "d8od", true, false }, { 8, "d7od", false, false }, { 8, "wod", true, false },{ 8, "w_kids", true, false }
            
        };
        return Arrays.asList(data);
    }


    @BeforeClass
    public static void setUpBeforeClass() throws Exception {
    }

    @Before
    public void setUp() throws Exception {
    }

    @Test
    public void test() {
        TimeboxFilter tf = new TimeboxFilter(processDay.toString());
        boolean actual = tf.isAllowed(timebox, isOptoutAid);
        assertEquals(expected, actual);
    }

}
