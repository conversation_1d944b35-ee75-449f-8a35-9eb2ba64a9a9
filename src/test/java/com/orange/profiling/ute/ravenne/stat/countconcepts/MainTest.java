package com.orange.profiling.ute.ravenne.stat.countconcepts;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.fail;

import java.io.File;
import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.Collections;
import java.util.List;

import org.apache.commons.io.FileUtils;
import org.apache.log4j.BasicConfigurator;
import org.apache.log4j.Level;
import org.apache.log4j.Logger;
import org.junit.Before;
import org.junit.Test;

import com.orange.profiling.common.exceptions.FailedJobException;
import com.orange.profiling.common.utils.unit_tests.TestUtils;

public class MainTest {
    private static URI runtimeResourceFolder;

    @Before
    public void setUp() throws URISyntaxException {
        runtimeResourceFolder = MainTest.class.getResource("/ute-ravenne-countconcepts/").toURI();

        BasicConfigurator.configure();
        Logger.getRootLogger().setLevel(Level.ERROR);
    }

    @Test
    public final void testMainKo() {

        String[] args = new String[1];
        args[0] = "";
        try {
            MainCountConcepts.main(args);
            fail("Should have thrown an IllegalArgumentException because no parameters");
        }
        catch (Exception e) {
            assertEquals(e.getMessage(),
                    "Takes 5 arguments : inputRavenneConceptsWeights deltaThreshold outputDir previousYearWeek processDate");
        }
    }

    @Test
    public final void testMainOk() throws IOException, ClassNotFoundException, InterruptedException, FailedJobException {

        final String profilGeneticInput = runtimeResourceFolder.resolve("in/ponderation/2019/11").getPath();
        final String threshold= "10";
        final String resultOutput = runtimeResourceFolder.resolve("out/countconcepts/2019/11").getPath();
        final String previousOutput = runtimeResourceFolder.resolve("previous/countconcepts/2019/10").getPath();
        final String processDate = "20190312";
        final String resultExpected = runtimeResourceFolder.resolve("expected").getPath();

        // arguments
        String[] cmdArgs = new String[] {
                profilGeneticInput,
                threshold,
                resultOutput,
                previousOutput,
                processDate
        };

        // delete output
        FileUtils.deleteDirectory(new File(resultOutput));

        // Run
        MainCountConcepts.main(cmdArgs);

        List<String> outputLines = TestUtils.getLinesFromPath(resultOutput, "part*");
        List<String> expectedLines = TestUtils.getLinesFromPath(resultExpected, "part*");

        assertFalse(outputLines.isEmpty());

        Collections.sort(expectedLines);
        Collections.sort(outputLines);

        assertEquals(expectedLines, outputLines);

    }

}
