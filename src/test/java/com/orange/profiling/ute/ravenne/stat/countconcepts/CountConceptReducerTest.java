package com.orange.profiling.ute.ravenne.stat.countconcepts;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.PrintStream;
import java.util.ArrayList;
import java.util.List;

import org.apache.hadoop.io.NullWritable;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mrunit.mapreduce.ReduceDriver;
import org.apache.log4j.BasicConfigurator;
import org.apache.log4j.Level;
import org.apache.log4j.Logger;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.runners.MockitoJUnitRunner;

import com.orange.profiling.common.utils.FieldsUtils;
import com.orange.profiling.ute.ravenne.logger.RavenneKpiLogger;

@RunWith(MockitoJUnitRunner.class)
public class CountConceptReducerTest {
    private static final String TAB = FieldsUtils.TAB;
    private static final String PIPE = FieldsUtils.PIPE;
    private static final String OUT_ID_TODAY = "T";
    private static final String OUT_ID_YESTERDAY = "Y";

    ReduceDriver<Text, Text, NullWritable, Text> reduceDriver;
    private final ByteArrayOutputStream errContent = new ByteArrayOutputStream();

    @Mock
    private RavenneKpiLogger mockLogger;

    @Before
    public void setUp() {

        CountConceptReducer reducer = new CountConceptReducer();
        reducer.setKpiLogger(mockLogger);
        reduceDriver = ReduceDriver.newReduceDriver(reducer);

        reduceDriver.getConfiguration().set(MainCountConcepts.CONF_THRESHOLD, "5");
        reduceDriver.getConfiguration().set(MainCountConcepts.CONF_PROCESS_DATE, "20190904");

        BasicConfigurator.configure();
        Logger.getRootLogger().setLevel(Level.ERROR);
    }

    @Before
    public void setUpStreams() {
        System.setErr(new PrintStream(this.errContent));
    }

    @After
    public final void cleanUpStreams() {
        System.setErr(null);
    }

    @Test
    public final void testSimpleReducerMore() throws IOException, InterruptedException, ClassNotFoundException {

        String timebox = "dt";
        String concept = "sujet/test/reducer";



        List<Text> values = new ArrayList<Text>();
        values.add(new Text(OUT_ID_TODAY+TAB+"500"));
        values.add(new Text(OUT_ID_TODAY+TAB+"300"));
        values.add(new Text(OUT_ID_TODAY+TAB+"200"));
        values.add(new Text(OUT_ID_TODAY+TAB+"250"));
        values.add(new Text(OUT_ID_YESTERDAY+TAB+"1000"));

        reduceDriver.withInput(new Text(timebox+PIPE+concept), values);

        reduceDriver.withOutput(NullWritable.get(),
                new Text(String.join(TAB, timebox, concept, "1250", "25", "250")));

        reduceDriver.runTest();

        withKpiLoggerSend(timebox, concept, 250.0, 25.0);

    }

    @Test
    public final void testSimpleReducerMoreDeltaThreshold() throws IOException, InterruptedException, ClassNotFoundException {

        String timebox = "dt";
        String concept = "sujet/test/reducer";



        List<Text> values = new ArrayList<Text>();
        values.add(new Text(OUT_ID_TODAY+TAB+"500"));
        values.add(new Text(OUT_ID_TODAY+TAB+"300"));
        values.add(new Text(OUT_ID_TODAY+TAB+"200"));
        values.add(new Text(OUT_ID_TODAY+TAB+"40"));
        values.add(new Text(OUT_ID_YESTERDAY+TAB+"1000"));

        reduceDriver.withInput(new Text(timebox+PIPE+concept), values);

        reduceDriver.withOutput(NullWritable.get(),
                new Text(String.join(TAB, timebox, concept, "1040", "4", "40")));

        reduceDriver.runTest();

        Mockito.verifyZeroInteractions(mockLogger);
    }

    @Test
    public final void testSimpleReducerLess() throws IOException, InterruptedException, ClassNotFoundException {

        String timebox = "dt";
        String concept = "sujet/test/reducer";



        List<Text> values = new ArrayList<Text>();
        values.add(new Text(OUT_ID_TODAY+TAB+"500"));
        values.add(new Text(OUT_ID_TODAY+TAB+"250"));
        values.add(new Text(OUT_ID_YESTERDAY+TAB+"1000"));

        reduceDriver.withInput(new Text(timebox+PIPE+concept), values);

        reduceDriver.withOutput(NullWritable.get(),
                new Text(String.join(TAB, timebox, concept, "750", "-25", "-250")));

        reduceDriver.runTest();

        withKpiLoggerSend(timebox, concept, -250.0, -25.0);
    }

    @Test
    public final void testSimpleReducerLessUnderThreshold() throws IOException, InterruptedException, ClassNotFoundException {

        String timebox = "dt";
        String concept = "sujet/test/reducer";



        List<Text> values = new ArrayList<Text>();
        values.add(new Text(OUT_ID_TODAY+TAB+"500"));
        values.add(new Text(OUT_ID_TODAY+TAB+"300"));
        values.add(new Text(OUT_ID_TODAY+TAB+"160"));
        values.add(new Text(OUT_ID_YESTERDAY+TAB+"1000"));

        reduceDriver.withInput(new Text(timebox+PIPE+concept), values);

        reduceDriver.withOutput(NullWritable.get(),
                new Text(String.join(TAB, timebox, concept, "960", "-4", "-40")));

        reduceDriver.runTest();
        Mockito.verifyZeroInteractions(mockLogger);
    }

    @Test
    public final void testReducerZeroTodayZeroYesterday() throws IOException, InterruptedException, ClassNotFoundException {

        String timebox = "dt";
        String concept = "sujet/test/reducer";



        List<Text> values = new ArrayList<Text>();
        values.add(new Text(OUT_ID_TODAY+TAB+"0"));
        values.add(new Text(OUT_ID_YESTERDAY+TAB+"0"));

        reduceDriver.withInput(new Text(timebox+PIPE+concept), values);

        reduceDriver.runTest();
        Mockito.verifyZeroInteractions(mockLogger);
    }

    @Test
    public final void testReducerEmptyTodayZeroYesterday() throws IOException, InterruptedException, ClassNotFoundException {

        String timebox = "dt";
        String concept = "sujet/test/reducer";



        List<Text> values = new ArrayList<Text>();
        values.add(new Text(OUT_ID_YESTERDAY+TAB+"0"));

        reduceDriver.withInput(new Text(timebox+PIPE+concept), values);

        reduceDriver.runTest();
        Mockito.verifyZeroInteractions(mockLogger);
    }

    @Test
    public final void testReducerNewTodayEmptyYesterday() throws IOException, InterruptedException, ClassNotFoundException {

        String timebox = "dt";
        String concept = "sujet/test/reducer";



        List<Text> values = new ArrayList<Text>();
        values.add(new Text(OUT_ID_TODAY+TAB+"500"));
        values.add(new Text(OUT_ID_TODAY+TAB+"300"));
        values.add(new Text(OUT_ID_TODAY+TAB+"200"));

        reduceDriver.withInput(new Text(timebox+PIPE+concept), values);

        reduceDriver.withOutput(NullWritable.get(),
                new Text(String.join(TAB, timebox, concept, "1000", "100", "1000")));

        reduceDriver.runTest();

        withKpiLoggerSend(timebox, concept, 1000.0, 100.0);
    }

    @Test
    public final void testReducerEmptyTodayExistingYesterday() throws IOException, InterruptedException, ClassNotFoundException {

        String timebox = "dt";
        String concept = "sujet/test/reducer";



        List<Text> values = new ArrayList<Text>();
        values.add(new Text(OUT_ID_YESTERDAY+TAB+"1000"));

        reduceDriver.withInput(new Text(timebox+PIPE+concept), values);

        reduceDriver.withOutput(NullWritable.get(),
                new Text(String.join(TAB, timebox, concept, "0", "-100", "-1000")));

        reduceDriver.runTest();

        withKpiLoggerSend(timebox, concept, -1000.0, -100.0);

    }

    @Test
    public final void testReducerBadNumber() throws IOException, InterruptedException, ClassNotFoundException {

        String timebox = "dt";
        String concept = "sujet/test/reducer";



        List<Text> values = new ArrayList<Text>();
        values.add(new Text(OUT_ID_TODAY+TAB+"500"));
        values.add(new Text(OUT_ID_TODAY+TAB+"300"));
        values.add(new Text(OUT_ID_TODAY+TAB+"200"));
        values.add(new Text(OUT_ID_TODAY+TAB+"A"));
        values.add(new Text(OUT_ID_YESTERDAY+TAB+"1000"));

        reduceDriver.withInput(new Text(timebox+PIPE+concept), values);

        reduceDriver.withOutput(NullWritable.get(),
                new Text(String.join(TAB, timebox, concept, "1000", "0", "0")));

        reduceDriver.runTest();

        Mockito.verifyZeroInteractions(mockLogger);
    }

    @Test
    public final void testReducerBadIdOut() throws IOException, InterruptedException, ClassNotFoundException {

        String timebox = "dt";
        String concept = "sujet/test/reducer";



        List<Text> values = new ArrayList<Text>();
        values.add(new Text("W"+TAB+"2000"));

        reduceDriver.withInput(new Text(timebox+PIPE+concept), values);

        reduceDriver.runTest();
        Mockito.verifyZeroInteractions(mockLogger);
    }

    @Test
    public final void testReducerBadFormat() throws IOException, InterruptedException, ClassNotFoundException {

        String timebox = "dt";
        String concept = "sujet/test/reducer";



        List<Text> values = new ArrayList<Text>();
        values.add(new Text(OUT_ID_TODAY));

        reduceDriver.withInput(new Text(timebox+PIPE+concept), values);

        reduceDriver.runTest();
        Mockito.verifyZeroInteractions(mockLogger);
    }

    @Test
    public final void testReducerBadKeyFormat() throws IOException, InterruptedException, ClassNotFoundException {

        String timebox = "dt";
        String concept = "sujet/test/reducer";



        List<Text> values = new ArrayList<Text>();
        values.add(new Text(OUT_ID_TODAY+TAB+"100"));

        reduceDriver.withInput(new Text(timebox+"-"+concept), values);

        reduceDriver.runTest();
        Mockito.verifyZeroInteractions(mockLogger);
    }

    private void withKpiLoggerSend(String timebox, String concept, Double deltaCount, Double deltaPct) {
        Mockito.verify(mockLogger).sendDeltaCountConcept(timebox, concept, deltaCount, deltaPct);
    }
}
