package com.orange.profiling.ute.ravenne.genetic.pnssend;

import static org.junit.Assert.*;

import java.io.IOException;
import org.apache.hadoop.mapreduce.Reducer.Context;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.runners.MockitoJUnitRunner;

import com.orange.profiling.common.utils.DatesUtils;
import com.orange.profiling.common.utils.FieldsUtils;
import com.orange.profiling.ute.ravenne.genetic.pnssend.Counters;
import com.orange.profiling.common.pns.file.PnsPig;

@RunWith(MockitoJUnitRunner.class)
public class PigDiffRavenneGeneticTest {
    private static final String AID = "123456789";
    private static final String TIMEBOX = "d3t4";
    private static final String DATANAME = "IA|TimeSlots";
    private static final String AID_TIMEBOX_PIGV2_KEY =
            AID + FieldsUtils.SEMICOLON + FieldsUtils.SEMICOLON + TIMEBOX;

    private static final String OPTOUT_AID = "999999999";
    private static final String OPTOUT_AID_TIMEBOX_PIGV2_KEY =
            OPTOUT_AID + FieldsUtils.SEMICOLON + FieldsUtils.SEMICOLON + TIMEBOX;

    private static final String PROFIL_FORMAT = "{%s,%s,%s,%s}";
    private static final String PROFIL_NORATIO_FORMAT = "{%s,%s,%s}";
    private static final String PROFIL_CONCEPTS_LIST_FORMAT = "\"th_rav\":[%s,%s,%s]";
    private static final String PROFIL_CONCEPTS_FORMAT = "{\"concept\":\"%s\",\"weight\":\"%d\"}";
    private static final String PROFIL_CHANNELS_FORMAT = "\"th_ch\":[%d,%d,%d]";
    private static final String PROFIL_PROVIDERS_FORMAT = "\"th_prov\":[%s,%s,%s]";
    private static final String PROFIL_RATIO_FORMAT = "\"th_rvl\":\"%d\"";

    private static final long FRESHNESS_DATE = 1579514400000L;
    private static final long EXPIRATION_DATE = FRESHNESS_DATE +
            30 * DatesUtils.ONE_DAY_SECONDS * DatesUtils.MILLISECONDS;
    private static final long LAST_DELIVERY_NOT_EXPIRED = FRESHNESS_DATE +
            5 * DatesUtils.ONE_DAY_SECONDS * DatesUtils.MILLISECONDS;
    // last delivery is considered expired if it expire less than 5 days after todays fresshness date
    private static final long LAST_DELIVERY_EXPIRED = FRESHNESS_DATE +
            3 * DatesUtils.ONE_DAY_SECONDS * DatesUtils.MILLISECONDS;

    private static final long FRESHNESS_PREVIOUS_DATE = FRESHNESS_DATE -
            7 * DatesUtils.ONE_DAY_SECONDS * DatesUtils.MILLISECONDS;
    private static final long EXPIRATION_PREVIOUS_DATE = EXPIRATION_DATE -
            7 * DatesUtils.ONE_DAY_SECONDS * DatesUtils.MILLISECONDS;

    @Mock
    private Context contextMock;

    private PigDiffRavenneGenetic pigDiff;

    @Before
    public void setUp() throws Exception {
        pigDiff = new PigDiffRavenneGenetic();
        prepareMock();
        pigDiff.setUp(contextMock);
    }

    private void prepareMock() {
        CounterMock.reinit();
        Mockito.when(contextMock.getCounter(Mockito.any()))
            .then(a-> CounterMock.getCounterMock(a.getArguments()[0].toString()));
    }

    @Test
    public void test() {
        PnsPig pigToday = new PnsPig();
        pigToday.setValue("");
        PnsPig pigYesterday = new PnsPig();
        pigYesterday.setValue("");
        boolean result = pigDiff.pnsValueAreDifferent(pigToday, pigYesterday);
        assertFalse(result);
    }

    @Test
    public final void testReducerOptoutProfil() throws IOException, InterruptedException, ClassNotFoundException {
        String todayProfil =
                String.format(PROFIL_FORMAT,
                        String.format(PROFIL_CONCEPTS_LIST_FORMAT,
                                String.format(PROFIL_CONCEPTS_FORMAT, "sujets/sport", 345),
                                String.format(PROFIL_CONCEPTS_FORMAT, "catégories/émissions", 457),
                                String.format(PROFIL_CONCEPTS_FORMAT, "ambiance et ton/humorisitique", 183)
                        ),
                        String.format(PROFIL_CHANNELS_FORMAT, 23, 192, 27),
                        String.format(PROFIL_PROVIDERS_FORMAT, "2424VIDEO", "SVODMYTF1","SVODFTV"),
                        String.format(PROFIL_RATIO_FORMAT, 42)
                );
        String todayPigValue = String.join(FieldsUtils.PIPE, OPTOUT_AID_TIMEBOX_PIGV2_KEY,
                DATANAME, todayProfil,
                Long.toString(EXPIRATION_DATE), Long.toString(FRESHNESS_DATE));
        PnsPig todayPig = new PnsPig();
        todayPig.setValue(todayPigValue);

        // Profil didn't changed
        String previousPigValue = String.join(FieldsUtils.PIPE, OPTOUT_AID_TIMEBOX_PIGV2_KEY,
                DATANAME, todayProfil,
                Long.toString(LAST_DELIVERY_NOT_EXPIRED), Long.toString(FRESHNESS_PREVIOUS_DATE));
        PnsPig previousPig = new PnsPig();
        previousPig.setValue(previousPigValue);

        boolean result = pigDiff.pnsValueAreDifferent(todayPig, previousPig);
        assertTrue(result);

        withCounter(Counters.OPTOUT_UID_999999999, 1);
    }

    @Test
    public final void testReducerNoChange() throws IOException, InterruptedException, ClassNotFoundException {
        String todayProfil =
                String.format(PROFIL_FORMAT,
                        String.format(PROFIL_CONCEPTS_LIST_FORMAT,
                                String.format(PROFIL_CONCEPTS_FORMAT, "sujets/sport", 345),
                                String.format(PROFIL_CONCEPTS_FORMAT, "catégories/émissions", 457),
                                String.format(PROFIL_CONCEPTS_FORMAT, "ambiance et ton/humorisitique", 183)
                        ),
                        String.format(PROFIL_CHANNELS_FORMAT, 23, 192, 27),
                        String.format(PROFIL_PROVIDERS_FORMAT, "2424VIDEO", "SVODMYTF1","SVODFTV"),
                        String.format(PROFIL_RATIO_FORMAT, 42)
                );
        String todayPigValue = String.join(FieldsUtils.PIPE, AID_TIMEBOX_PIGV2_KEY,
                DATANAME, todayProfil,
                Long.toString(EXPIRATION_DATE), Long.toString(FRESHNESS_DATE));
        PnsPig todayPig = new PnsPig();
        todayPig.setValue(todayPigValue);

        // Profil didn't changed
        String previousPigValue = String.join(FieldsUtils.PIPE, AID_TIMEBOX_PIGV2_KEY,
                DATANAME, todayProfil,
                Long.toString(LAST_DELIVERY_NOT_EXPIRED), Long.toString(FRESHNESS_PREVIOUS_DATE));
        PnsPig previousPig = new PnsPig();
        previousPig.setValue(previousPigValue);

        boolean result = pigDiff.pnsValueAreDifferent(todayPig, previousPig);
        assertFalse(result);

        withCounter(Counters.OPTOUT_UID_999999999, 0);
        withCounter(Counters.RAVENNE_DONT_EQUALS, 0);
        withCounter(Counters.CHANNEL_DONT_EQUALS, 0);
        withCounter(Counters.PROVIDER_DONT_EQUALS, 0);
        withCounter(Counters.RATIO_DONT_EQUALS, 0);
    }

    @Test
    public final void testReducerDifferentRavenneProfil() throws IOException, InterruptedException, ClassNotFoundException {
        String todayProfil =
                String.format(PROFIL_FORMAT,
                        String.format(PROFIL_CONCEPTS_LIST_FORMAT,
                                String.format(PROFIL_CONCEPTS_FORMAT, "sujets/sport", 345),
                                String.format(PROFIL_CONCEPTS_FORMAT, "catégories/émissions", 457),
                                String.format(PROFIL_CONCEPTS_FORMAT, "ambiance et ton/humorisitique", 183)
                        ),
                        String.format(PROFIL_CHANNELS_FORMAT, 23, 192, 27),
                        String.format(PROFIL_PROVIDERS_FORMAT, "2424VIDEO", "SVODMYTF1","SVODFTV"),
                        String.format(PROFIL_RATIO_FORMAT, 42)
                );
        String todayPigValue = String.join(FieldsUtils.PIPE, AID_TIMEBOX_PIGV2_KEY,
                DATANAME, todayProfil,
                Long.toString(EXPIRATION_DATE), Long.toString(FRESHNESS_DATE));
        PnsPig todayPig = new PnsPig();
        todayPig.setValue(todayPigValue);

        // Profil changed for th_rav
        String previousProfil =
                String.format(PROFIL_FORMAT,
                        String.format(PROFIL_CONCEPTS_LIST_FORMAT,
                                String.format(PROFIL_CONCEPTS_FORMAT, "sujets/sport", 445),
                                String.format(PROFIL_CONCEPTS_FORMAT, "catégories/émissions", 457),
                                String.format(PROFIL_CONCEPTS_FORMAT, "ambiance et ton/humorisitique", 183)
                        ),
                        String.format(PROFIL_CHANNELS_FORMAT, 23, 192, 27),
                        String.format(PROFIL_PROVIDERS_FORMAT, "2424VIDEO", "SVODMYTF1","SVODFTV"),
                        String.format(PROFIL_RATIO_FORMAT, 42)
                );
        String previousPigValue = String.join(FieldsUtils.PIPE, AID_TIMEBOX_PIGV2_KEY,
                DATANAME, previousProfil,
                Long.toString(LAST_DELIVERY_NOT_EXPIRED), Long.toString(FRESHNESS_PREVIOUS_DATE));
        PnsPig previousPig = new PnsPig();
        previousPig.setValue(previousPigValue);

        boolean result = pigDiff.pnsValueAreDifferent(todayPig, previousPig);
        assertTrue(result);

        withCounter(Counters.OPTOUT_UID_999999999, 0);
        withCounter(Counters.RAVENNE_DONT_EQUALS, 1);
        withCounter(Counters.CHANNEL_DONT_EQUALS, 0);
        withCounter(Counters.PROVIDER_DONT_EQUALS, 0);
        withCounter(Counters.RATIO_DONT_EQUALS, 0);
    }

    @Test
    public final void testReducerDifferentChannelProfil() throws IOException, InterruptedException, ClassNotFoundException {
        String todayProfil =
                String.format(PROFIL_FORMAT,
                        String.format(PROFIL_CONCEPTS_LIST_FORMAT,
                                String.format(PROFIL_CONCEPTS_FORMAT, "sujets/sport", 345),
                                String.format(PROFIL_CONCEPTS_FORMAT, "catégories/émissions", 457),
                                String.format(PROFIL_CONCEPTS_FORMAT, "ambiance et ton/humorisitique", 183)
                        ),
                        String.format(PROFIL_CHANNELS_FORMAT, 23, 192, 27),
                        String.format(PROFIL_PROVIDERS_FORMAT, "2424VIDEO", "SVODMYTF1","SVODFTV"),
                        String.format(PROFIL_RATIO_FORMAT, 42)
                );
        String todayPigValue = String.join(FieldsUtils.PIPE, AID_TIMEBOX_PIGV2_KEY,
                DATANAME, todayProfil,
                Long.toString(EXPIRATION_DATE), Long.toString(FRESHNESS_DATE));
        PnsPig todayPig = new PnsPig();
        todayPig.setValue(todayPigValue);

        // Profil changed for th_rav
        String previousProfil =
                String.format(PROFIL_FORMAT,
                        String.format(PROFIL_CONCEPTS_LIST_FORMAT,
                                String.format(PROFIL_CONCEPTS_FORMAT, "sujets/sport", 345),
                                String.format(PROFIL_CONCEPTS_FORMAT, "catégories/émissions", 457),
                                String.format(PROFIL_CONCEPTS_FORMAT, "ambiance et ton/humorisitique", 183)
                        ),
                        String.format(PROFIL_CHANNELS_FORMAT, 192, 23, 27),
                        String.format(PROFIL_PROVIDERS_FORMAT, "2424VIDEO", "SVODMYTF1","SVODFTV"),
                        String.format(PROFIL_RATIO_FORMAT, 42)
                );
        String previousPigValue = String.join(FieldsUtils.PIPE, AID_TIMEBOX_PIGV2_KEY,
                DATANAME, previousProfil,
                Long.toString(LAST_DELIVERY_NOT_EXPIRED), Long.toString(FRESHNESS_PREVIOUS_DATE));
        PnsPig previousPig = new PnsPig();
        previousPig.setValue(previousPigValue);

        boolean result = pigDiff.pnsValueAreDifferent(todayPig, previousPig);
        assertTrue(result);

        withCounter(Counters.OPTOUT_UID_999999999, 0);
        withCounter(Counters.RAVENNE_DONT_EQUALS, 0);
        withCounter(Counters.CHANNEL_DONT_EQUALS, 1);
        withCounter(Counters.PROVIDER_DONT_EQUALS, 0);
        withCounter(Counters.RATIO_DONT_EQUALS, 0);
    }

    @Test
    public final void testReducerDifferentProvidersProfil() throws IOException, InterruptedException, ClassNotFoundException {
        String todayProfil =
                String.format(PROFIL_FORMAT,
                        String.format(PROFIL_CONCEPTS_LIST_FORMAT,
                                String.format(PROFIL_CONCEPTS_FORMAT, "sujets/sport", 345),
                                String.format(PROFIL_CONCEPTS_FORMAT, "catégories/émissions", 457),
                                String.format(PROFIL_CONCEPTS_FORMAT, "ambiance et ton/humorisitique", 183)
                        ),
                        String.format(PROFIL_CHANNELS_FORMAT, 23, 192, 27),
                        String.format(PROFIL_PROVIDERS_FORMAT, "2424VIDEO", "SVODM6","SVODFTV"),
                        String.format(PROFIL_RATIO_FORMAT, 42)
                );
        String todayPigValue = String.join(FieldsUtils.PIPE, AID_TIMEBOX_PIGV2_KEY,
                DATANAME, todayProfil,
                Long.toString(EXPIRATION_DATE), Long.toString(FRESHNESS_DATE));
        PnsPig todayPig = new PnsPig();
        todayPig.setValue(todayPigValue);

        // Profil changed for th_rav
        String previousProfil =
                String.format(PROFIL_FORMAT,
                        String.format(PROFIL_CONCEPTS_LIST_FORMAT,
                                String.format(PROFIL_CONCEPTS_FORMAT, "sujets/sport", 345),
                                String.format(PROFIL_CONCEPTS_FORMAT, "catégories/émissions", 457),
                                String.format(PROFIL_CONCEPTS_FORMAT, "ambiance et ton/humorisitique", 183)
                        ),
                        String.format(PROFIL_CHANNELS_FORMAT, 23, 192, 27),
                        String.format(PROFIL_PROVIDERS_FORMAT, "2424VIDEO", "SVODMYTF1","SVODFTV"),
                        String.format(PROFIL_RATIO_FORMAT, 42)
                );
        String previousPigValue = String.join(FieldsUtils.PIPE, AID_TIMEBOX_PIGV2_KEY,
                DATANAME, previousProfil,
                Long.toString(LAST_DELIVERY_NOT_EXPIRED), Long.toString(FRESHNESS_PREVIOUS_DATE));
        PnsPig previousPig = new PnsPig();
        previousPig.setValue(previousPigValue);

        boolean result = pigDiff.pnsValueAreDifferent(todayPig, previousPig);
        assertTrue(result);

        withCounter(Counters.OPTOUT_UID_999999999, 0);
        withCounter(Counters.RAVENNE_DONT_EQUALS, 0);
        withCounter(Counters.CHANNEL_DONT_EQUALS, 0);
        withCounter(Counters.PROVIDER_DONT_EQUALS, 0);
        withCounter(Counters.RATIO_DONT_EQUALS, 0);
    }

    @Test
    public final void testReducerDifferentRatioProfil() throws IOException, InterruptedException, ClassNotFoundException {
        String todayProfil =
                String.format(PROFIL_FORMAT,
                        String.format(PROFIL_CONCEPTS_LIST_FORMAT,
                                String.format(PROFIL_CONCEPTS_FORMAT, "sujets/sport", 345),
                                String.format(PROFIL_CONCEPTS_FORMAT, "catégories/émissions", 457),
                                String.format(PROFIL_CONCEPTS_FORMAT, "ambiance et ton/humorisitique", 183)
                        ),
                        String.format(PROFIL_CHANNELS_FORMAT, 23, 192, 27),
                        String.format(PROFIL_PROVIDERS_FORMAT, "2424VIDEO", "SVODMYTF1","SVODFTV"),
                        String.format(PROFIL_RATIO_FORMAT, 42)
                );
        String todayPigValue = String.join(FieldsUtils.PIPE, AID_TIMEBOX_PIGV2_KEY,
                DATANAME, todayProfil,
                Long.toString(EXPIRATION_DATE), Long.toString(FRESHNESS_DATE));
        PnsPig todayPig = new PnsPig();
        todayPig.setValue(todayPigValue);

        // Profil changed for th_rav
        String previousProfil =
                String.format(PROFIL_FORMAT,
                        String.format(PROFIL_CONCEPTS_LIST_FORMAT,
                                String.format(PROFIL_CONCEPTS_FORMAT, "sujets/sport", 345),
                                String.format(PROFIL_CONCEPTS_FORMAT, "catégories/émissions", 457),
                                String.format(PROFIL_CONCEPTS_FORMAT, "ambiance et ton/humorisitique", 183)
                        ),
                        String.format(PROFIL_CHANNELS_FORMAT, 23, 192, 27),
                        String.format(PROFIL_PROVIDERS_FORMAT, "2424VIDEO", "SVODMYTF1","SVODFTV"),
                        String.format(PROFIL_RATIO_FORMAT, 36)
                );
        String previousPigValue = String.join(FieldsUtils.PIPE, AID_TIMEBOX_PIGV2_KEY,
                DATANAME, previousProfil,
                Long.toString(LAST_DELIVERY_NOT_EXPIRED), Long.toString(FRESHNESS_PREVIOUS_DATE));
        PnsPig previousPig = new PnsPig();
        previousPig.setValue(previousPigValue);

        boolean result = pigDiff.pnsValueAreDifferent(todayPig, previousPig);
        assertTrue(result);

        withCounter(Counters.OPTOUT_UID_999999999, 0);
        withCounter(Counters.RAVENNE_DONT_EQUALS, 0);
        withCounter(Counters.CHANNEL_DONT_EQUALS, 0);
        withCounter(Counters.PROVIDER_DONT_EQUALS, 0);
        withCounter(Counters.RATIO_DONT_EQUALS, 1);
    }

    @Test
    public final void testReducerDifferentRatioProfilTodayHasNoKey() throws IOException, InterruptedException, ClassNotFoundException {
        String todayProfil =
                String.format(PROFIL_NORATIO_FORMAT,
                        String.format(PROFIL_CONCEPTS_LIST_FORMAT,
                                String.format(PROFIL_CONCEPTS_FORMAT, "sujets/sport", 345),
                                String.format(PROFIL_CONCEPTS_FORMAT, "catégories/émissions", 457),
                                String.format(PROFIL_CONCEPTS_FORMAT, "ambiance et ton/humorisitique", 183)
                        ),
                        String.format(PROFIL_CHANNELS_FORMAT, 23, 192, 27),
                        String.format(PROFIL_PROVIDERS_FORMAT, "2424VIDEO", "SVODMYTF1","SVODFTV")
                );
        String todayPigValue = String.join(FieldsUtils.PIPE, AID_TIMEBOX_PIGV2_KEY,
                DATANAME, todayProfil,
                Long.toString(EXPIRATION_DATE), Long.toString(FRESHNESS_DATE));
        PnsPig todayPig = new PnsPig();
        todayPig.setValue(todayPigValue);

        // Profil changed for th_rav
        String previousProfil =
                String.format(PROFIL_FORMAT,
                        String.format(PROFIL_CONCEPTS_LIST_FORMAT,
                                String.format(PROFIL_CONCEPTS_FORMAT, "sujets/sport", 345),
                                String.format(PROFIL_CONCEPTS_FORMAT, "catégories/émissions", 457),
                                String.format(PROFIL_CONCEPTS_FORMAT, "ambiance et ton/humorisitique", 183)
                        ),
                        String.format(PROFIL_CHANNELS_FORMAT, 23, 192, 27),
                        String.format(PROFIL_PROVIDERS_FORMAT, "2424VIDEO", "SVODMYTF1","SVODFTV"),
                        String.format(PROFIL_RATIO_FORMAT, 36)
                );
        String previousPigValue = String.join(FieldsUtils.PIPE, AID_TIMEBOX_PIGV2_KEY,
                DATANAME, previousProfil,
                Long.toString(LAST_DELIVERY_NOT_EXPIRED), Long.toString(FRESHNESS_PREVIOUS_DATE));
        PnsPig previousPig = new PnsPig();
        previousPig.setValue(previousPigValue);

        boolean result = pigDiff.pnsValueAreDifferent(todayPig, previousPig);
        assertTrue(result);

        withCounter(Counters.OPTOUT_UID_999999999, 0);
        withCounter(Counters.RAVENNE_DONT_EQUALS, 0);
        withCounter(Counters.CHANNEL_DONT_EQUALS, 0);
        withCounter(Counters.PROVIDER_DONT_EQUALS, 0);
        withCounter(Counters.RATIO_DONT_EQUALS, 1);
    }

    @Test
    public final void testReducerDifferentRatioProfilYesterdayHasNoKey() throws IOException, InterruptedException, ClassNotFoundException {
        String todayProfil =
                String.format(PROFIL_FORMAT,
                        String.format(PROFIL_CONCEPTS_LIST_FORMAT,
                                String.format(PROFIL_CONCEPTS_FORMAT, "sujets/sport", 345),
                                String.format(PROFIL_CONCEPTS_FORMAT, "catégories/émissions", 457),
                                String.format(PROFIL_CONCEPTS_FORMAT, "ambiance et ton/humorisitique", 183)
                        ),
                        String.format(PROFIL_CHANNELS_FORMAT, 23, 192, 27),
                        String.format(PROFIL_PROVIDERS_FORMAT, "2424VIDEO", "SVODMYTF1","SVODFTV"),
                        String.format(PROFIL_RATIO_FORMAT, 42)
                );
        String todayPigValue = String.join(FieldsUtils.PIPE, AID_TIMEBOX_PIGV2_KEY,
                DATANAME, todayProfil,
                Long.toString(EXPIRATION_DATE), Long.toString(FRESHNESS_DATE));
        PnsPig todayPig = new PnsPig();
        todayPig.setValue(todayPigValue);

        // Profil changed for th_rav
        String previousProfil =
                String.format(PROFIL_NORATIO_FORMAT,
                        String.format(PROFIL_CONCEPTS_LIST_FORMAT,
                                String.format(PROFIL_CONCEPTS_FORMAT, "sujets/sport", 345),
                                String.format(PROFIL_CONCEPTS_FORMAT, "catégories/émissions", 457),
                                String.format(PROFIL_CONCEPTS_FORMAT, "ambiance et ton/humorisitique", 183)
                        ),
                        String.format(PROFIL_CHANNELS_FORMAT, 23, 192, 27),
                        String.format(PROFIL_PROVIDERS_FORMAT, "2424VIDEO", "SVODMYTF1","SVODFTV")
                );
        String previousPigValue = String.join(FieldsUtils.PIPE, AID_TIMEBOX_PIGV2_KEY,
                DATANAME, previousProfil,
                Long.toString(LAST_DELIVERY_NOT_EXPIRED), Long.toString(FRESHNESS_PREVIOUS_DATE));
        PnsPig previousPig = new PnsPig();
        previousPig.setValue(previousPigValue);

        boolean result = pigDiff.pnsValueAreDifferent(todayPig, previousPig);
        assertTrue(result);

        withCounter(Counters.OPTOUT_UID_999999999, 0);
        withCounter(Counters.RAVENNE_DONT_EQUALS, 0);
        withCounter(Counters.CHANNEL_DONT_EQUALS, 0);
        withCounter(Counters.PROVIDER_DONT_EQUALS, 0);
        withCounter(Counters.RATIO_DONT_EQUALS, 1);
    }

    @Test
    public final void testReducerDifferentChannelProfilNobodyHasKey() throws IOException, InterruptedException, ClassNotFoundException {
        String todayProfil =
                String.format(PROFIL_NORATIO_FORMAT,
                        String.format(PROFIL_CONCEPTS_LIST_FORMAT,
                                String.format(PROFIL_CONCEPTS_FORMAT, "sujets/sport", 345),
                                String.format(PROFIL_CONCEPTS_FORMAT, "catégories/émissions", 457),
                                String.format(PROFIL_CONCEPTS_FORMAT, "ambiance et ton/humorisitique", 183)
                        ),
                        String.format(PROFIL_CHANNELS_FORMAT, 192, 27, 23),
                        String.format(PROFIL_PROVIDERS_FORMAT, "2424VIDEO", "SVODMYTF1","SVODFTV")
                );
        String todayPigValue = String.join(FieldsUtils.PIPE, AID_TIMEBOX_PIGV2_KEY,
                DATANAME, todayProfil,
                Long.toString(EXPIRATION_DATE), Long.toString(FRESHNESS_DATE));
        PnsPig todayPig = new PnsPig();
        todayPig.setValue(todayPigValue);

        // Profil changed for th_rav
        String previousProfil =
                String.format(PROFIL_NORATIO_FORMAT,
                        String.format(PROFIL_CONCEPTS_LIST_FORMAT,
                                String.format(PROFIL_CONCEPTS_FORMAT, "sujets/sport", 345),
                                String.format(PROFIL_CONCEPTS_FORMAT, "catégories/émissions", 457),
                                String.format(PROFIL_CONCEPTS_FORMAT, "ambiance et ton/humorisitique", 183)
                        ),
                        String.format(PROFIL_CHANNELS_FORMAT, 23, 192, 27),
                        String.format(PROFIL_PROVIDERS_FORMAT, "2424VIDEO", "SVODMYTF1","SVODFTV")
                );
        String previousPigValue = String.join(FieldsUtils.PIPE, AID_TIMEBOX_PIGV2_KEY,
                DATANAME, previousProfil,
                Long.toString(LAST_DELIVERY_NOT_EXPIRED), Long.toString(FRESHNESS_PREVIOUS_DATE));
        PnsPig previousPig = new PnsPig();
        previousPig.setValue(previousPigValue);

        boolean result = pigDiff.pnsValueAreDifferent(todayPig, previousPig);
        assertTrue(result);

        withCounter(Counters.OPTOUT_UID_999999999, 0);
        withCounter(Counters.RAVENNE_DONT_EQUALS, 0);
        withCounter(Counters.CHANNEL_DONT_EQUALS, 1);
        withCounter(Counters.PROVIDER_DONT_EQUALS, 0);
        withCounter(Counters.RATIO_DONT_EQUALS, 0);
    }

    @Test
    public final void testReducerDifferentMalformedJson() throws IOException, InterruptedException, ClassNotFoundException {
        String todayProfil = "today";
        String todayPigValue = String.join(FieldsUtils.PIPE, AID_TIMEBOX_PIGV2_KEY,
                DATANAME, todayProfil,
                Long.toString(EXPIRATION_DATE), Long.toString(FRESHNESS_DATE));
        PnsPig todayPig = new PnsPig();
        todayPig.setValue(todayPigValue);

        // Profil changed for th_rav
        String previousProfil = "previous";
        String previousPigValue = String.join(FieldsUtils.PIPE, AID_TIMEBOX_PIGV2_KEY,
                DATANAME, previousProfil,
                Long.toString(LAST_DELIVERY_NOT_EXPIRED), Long.toString(FRESHNESS_PREVIOUS_DATE));
        PnsPig previousPig = new PnsPig();
        previousPig.setValue(previousPigValue);

        boolean result = pigDiff.pnsValueAreDifferent(todayPig, previousPig);
        assertTrue(result);

        withCounter(Counters.OPTOUT_UID_999999999, 0);
        withCounter(Counters.RAVENNE_DONT_EQUALS, 0);
        withCounter(Counters.CHANNEL_DONT_EQUALS, 0);
        withCounter(Counters.PROVIDER_DONT_EQUALS, 0);
        withCounter(Counters.RATIO_DONT_EQUALS, 0);
        withCounter(Counters.MALFORMED_JSON, 1);
    }

    private void withCounter(Counters counter, long val) {
        String name = counter.toString();
        CounterMock counterMock = CounterMock.getCounterMock(name);
        assertEquals(counter.toString(), val, counterMock.getCount());
    }

}
