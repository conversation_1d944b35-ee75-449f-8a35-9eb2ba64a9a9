package com.orange.profiling.ute.ravenne.ponderation;

import static org.junit.Assert.*;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

import org.junit.Test;

public class ConceptPonderatorTest {

    @Test
    public void testBuildConceptFromRootToIndexIndex1() {
        String[] nodes = { "root", "parent1", "parent2", "child" };
        String result = ConceptPonderator.buildConceptFromRootToIndex(nodes, 1);
        String expected = "root/parent1";
        assertEquals(expected, result);
    }

    @Test
    public void testBuildConceptFromRootToIndexIndex2() {
        String[] nodes = { "root", "parent1", "parent2", "child" };
        String result = ConceptPonderator.buildConceptFromRootToIndex(nodes, 2);
        String expected = "root/parent1/parent2";
        assertEquals(expected, result);
    }


    @Test
    public void testGetNbChildrenByConceptSimple() {
        // LinkedHashMap to keep insertion order (important for the tests)
        Map<String, Long> weightedConcepts = new LinkedHashMap<>();
        weightedConcepts.put("root/pa1/pb1/c1", 5L);
        weightedConcepts.put("root/pa1/pb1/c2", 5L);
        weightedConcepts.put("root/pa1/pb1/c3", 5L);
        weightedConcepts.put("root/pa1/pb2/c1", 5L);
        weightedConcepts.put("root/pa1/pb2/c2", 5L);
        weightedConcepts.put("root/pa2/pb1/c1", 5L);
        weightedConcepts.put("root/pa2/pb1/c2", 5L);

        ConceptPonderator conceptPonderator = new ConceptPonderator(weightedConcepts);

        Map<String, Integer> nbChildrenByConcept = conceptPonderator.getNbChildrenByConcept();

        Integer expected = 3;
        assertEquals(expected, nbChildrenByConcept.get("root/pa1/pb1"));
        expected = 2;
        assertEquals(expected, nbChildrenByConcept.get("root/pa1/pb2"));
        expected = 2;
        assertEquals(expected, nbChildrenByConcept.get("root/pa2/pb1"));
        expected = 2;
        assertEquals(expected, nbChildrenByConcept.get("root/pa1"));
        expected = 1;
        assertEquals(expected, nbChildrenByConcept.get("root/pa2"));

    }

    @Test
    public void testGetNbChildrenByConceptWithParentPresent() {
        // LinkedHashMap to keep insertion order (important for the tests)
        Map<String, Long> weightedConcepts = new LinkedHashMap<>();
        weightedConcepts.put("root/pa1", 2L);
        weightedConcepts.put("root/pa1/pb1", 2L);
        weightedConcepts.put("root/pa1/pb1/c1", 5l);
        weightedConcepts.put("root/pa1/pb1/c2", 5L);
        weightedConcepts.put("root/pa1/pb1/c3", 5L);
        weightedConcepts.put("root/pa1/pb2/c1", 5L);
        weightedConcepts.put("root/pa1/pb2/c2", 5L);
        weightedConcepts.put("root/pa1/pb3", 3L);
        weightedConcepts.put("root/pa2/pb1", 4L);
        weightedConcepts.put("root/pa2/pb2", 4L);
        weightedConcepts.put("root/pa2/pb1/c1", 5L);
        weightedConcepts.put("root/pa2/pb1/c2", 5L);

        ConceptPonderator conceptPonderator = new ConceptPonderator(weightedConcepts);

        Map<String, Integer> nbChildrenByConcept = conceptPonderator.getNbChildrenByConcept();

        Integer expected = 3;
        assertEquals(expected, nbChildrenByConcept.get("root/pa1/pb1"));
        expected = 2;
        assertEquals(expected, nbChildrenByConcept.get("root/pa1/pb2"));
        expected = 2;
        assertEquals(expected, nbChildrenByConcept.get("root/pa2/pb1"));
        expected = 3;
        assertEquals(expected, nbChildrenByConcept.get("root/pa1"));
        expected = 2;
        assertEquals(expected, nbChildrenByConcept.get("root/pa2"));
    }

    @Test
    public void testGetNbChildrenByConceptWithBadOrder() {
        // LinkedHashMap to keep insertion order (important for the tests)
        Map<String, Long> weightedConcepts = new LinkedHashMap<>();
        weightedConcepts.put("root/pa1/pb1/c1", 5L);
        weightedConcepts.put("root/pa1/pb1/c2", 5L);
        weightedConcepts.put("root/pa1", 2L);
        weightedConcepts.put("root/pa1/pb1/c3", 5L);
        weightedConcepts.put("root/pa1/pb2/c1", 5L);
        weightedConcepts.put("root/pa2/pb1", 4L);
        weightedConcepts.put("root/pa1/pb2/c2", 5L);
        weightedConcepts.put("root/pa1/pb1", 2L);
        weightedConcepts.put("root/pa2/pb2", 4L);
        weightedConcepts.put("root/pa1/pb3", 3L);
        weightedConcepts.put("root/pa2/pb1/c1", 5L);
        weightedConcepts.put("root/pa2/pb1/c2", 5L);

        ConceptPonderator conceptPonderator = new ConceptPonderator(weightedConcepts);

        Map<String, Integer> nbChildrenByConcept = conceptPonderator.getNbChildrenByConcept();

        Integer expected = 3;
        assertEquals(expected, nbChildrenByConcept.get("root/pa1/pb1"));
        expected = 2;
        assertEquals(expected, nbChildrenByConcept.get("root/pa1/pb2"));
        expected = 2;
        assertEquals(expected, nbChildrenByConcept.get("root/pa2/pb1"));
        expected = 3;
        assertEquals(expected, nbChildrenByConcept.get("root/pa1"));
        expected = 2;
        assertEquals(expected, nbChildrenByConcept.get("root/pa2"));
    }

    @Test
    public void testAddLeafWeightToAllParent() {
        // Initialize with empty map as it's note used in this method
        ConceptPonderator conceptPonderator = new ConceptPonderator(new HashMap<String, Long>());
        Map<String, Integer> nbChildrenByConcept = new HashMap<>();
        nbChildrenByConcept.put("root/pa1/pb1", 3);
        nbChildrenByConcept.put("root/pa1", 2);

        Map<String, Long> weightedConceptMap = new HashMap<>();
        weightedConceptMap.put("root/pa1/pb1",  200L);
        weightedConceptMap.put("root/pa1",  100L);

        conceptPonderator.addLeafWeightToAllParent(weightedConceptMap, nbChildrenByConcept, "root/pa1/pb1/c1", 600L);

        Long expected = 500L;
        assertEquals(expected, weightedConceptMap.get("root/pa1/pb1/c1"));
        expected = 275L;
        assertEquals(expected, weightedConceptMap.get("root/pa1/pb1"));
        expected = 125L;
        assertEquals(expected, weightedConceptMap.get("root/pa1"));

    }

    @Test
    public void testPonderatesParentsWhithChildWeightOrderedWithoutParents() {
        // LinkedHashMap to keep insertion order (important for the tests)
        Map<String, Long> weightedConcepts = new LinkedHashMap<>();
        weightedConcepts.put("root/pa1/pb1/c1", 600L);
        weightedConcepts.put("root/pa1/pb1/c2", 800L);
        weightedConcepts.put("root/pa1/pb1/c3", 1000L);
        weightedConcepts.put("root/pa1/pb2/c1", 400L);
        weightedConcepts.put("root/pa1/pb2/c2", 800L);
        weightedConcepts.put("root/pa1/pb3", 300L);
        weightedConcepts.put("root/pa2/pb1/c1", 600L);
        weightedConcepts.put("root/pa2/pb1/c2", 700L);
        weightedConcepts.put("root/pa2/pb2", 400L);

        ConceptPonderator conceptPonderator = new ConceptPonderator(weightedConcepts);

        Map<String, Long> ponderatedWeightedConcept = conceptPonderator.ponderatesParentsWithChildWeight();

        checkExpectedValues(ponderatedWeightedConcept);
    }

    @Test
    public void testPonderatesParentsWhithChildWeightUnorderedWithParents() {
        // Order should not change anything
        // Parents weight is not used
        // LinkedHashMap to keep insertion order (important for the tests)
        Map<String, Long> weightedConcepts = new LinkedHashMap<>();
        weightedConcepts.put("root/pa2", 600L);
        weightedConcepts.put("root/pa1/pb1/c1", 600L);
        weightedConcepts.put("root/pa1/pb1/c2", 800L);
        weightedConcepts.put("root/pa1", 500L);
        weightedConcepts.put("root/pa1/pb1/c3", 1000L);
        weightedConcepts.put("root/pa1/pb2/c2", 800L);
        weightedConcepts.put("root/pa1/pb2", 1200L);
        weightedConcepts.put("root/pa1/pb2/c1", 400L);
        weightedConcepts.put("root/pa2/pb1", 600L);
        weightedConcepts.put("root/pa2/pb1/c2", 700L);
        weightedConcepts.put("root/pa1/pb1", 200L);
        weightedConcepts.put("root/pa2/pb2", 400L);
        weightedConcepts.put("root/pa1/pb3", 300L);
        weightedConcepts.put("root/pa2/pb1/c1", 600L);

        ConceptPonderator conceptPonderator = new ConceptPonderator(weightedConcepts);

        Map<String, Long> ponderatedWeightedConcept = conceptPonderator.ponderatesParentsWithChildWeight();

        checkExpectedValues(ponderatedWeightedConcept);
    }

    private void checkExpectedValues(Map<String, Long> ponderatedWeightedConcept) {
        /** CALCULS
        *
        * root/pa1/pb1/c1 : 600
        *     pa1/pb1/c1 : 600 - 600/6 = 500
        *     pa1/pb1 : 100 - 100/6 = 84
        *     pa1 = 16
        *
        * root/pa1/pb1/c2 : 800
        *     pa1/pb1/c2 : 800 - 800/6 = 667
        *     pa1/pb1 : 133 - 133/6 = 111 + pa1/pb1 = 195
        *     pa1 = 22 + pa1 = 38
        *
        * root/pa1/pb1/c3 : 1000
        *     pa1/pb1/c3 : 1000 - 1000/6 = 834
        *     pa1/pb1 : 166 - 166/6 = 139 + pa1/pb1 = 334
        *     pa1 = 27 + pa1 = 65
        *
        * root/pa1/pb2/c1 : 400
        *     pa1/pb2/c1 : 400 - 400/4 = 300
        *     pa1/pb2 : 100 - 100/6 = 84
        *     pa1 = 16 + pa1 = 81
        *
        * root/pa1/pb2/c2 : 800
        *     pa1/pb2/c2 : 800 - 800/4 = 600
        *     pa1/pb2 : 200 - 200/6 = 167 + pa1/pb2 = 251
        *     pa1 = 33 + pa1 = 114
        *
        * root/pa1/pb3 : 300
        *     pa1/pb3 : 300 - 300/6 = 250
        *     pa1 : 50 + pa1 = 164
        *
        * root/pa2/pb1/c1 : 600
        *     pa2/pb1/c1 : 600 - 600/4 = 450
        *     pa2/pb1 : 150 - 150/4 = 113
        *     pa2 = 37
        *
        * root/pa2/pb1/c2 : 700
        *     pa2/pb1/c2 : 700 - 700/4 = 525
        *     pa2/pb1 : 175 - 175/4 = 132 + pa2/pb1 = 245
        *     pa2 = 43 + pa2 = 80
        *
        * root/pa2/pb2 : 400
        *     pa2/pb2 : 400 - 400/4 = 300
        *     pa2 = 100 + pa2 = 180
        */

        Long expected = 164L;
        assertEquals(expected, ponderatedWeightedConcept.get("root/pa1"));
        expected = 334L;
        assertEquals(expected, ponderatedWeightedConcept.get("root/pa1/pb1"));
        expected = 500L;
        assertEquals(expected, ponderatedWeightedConcept.get("root/pa1/pb1/c1"));
        expected = 667L;
        assertEquals(expected, ponderatedWeightedConcept.get("root/pa1/pb1/c2"));
        expected = 834L;
        assertEquals(expected, ponderatedWeightedConcept.get("root/pa1/pb1/c3"));
        expected = 251L;
        assertEquals(expected, ponderatedWeightedConcept.get("root/pa1/pb2"));
        expected = 300L;
        assertEquals(expected, ponderatedWeightedConcept.get("root/pa1/pb2/c1"));
        expected = 600L;
        assertEquals(expected, ponderatedWeightedConcept.get("root/pa1/pb2/c2"));
        expected = 250L;
        assertEquals(expected, ponderatedWeightedConcept.get("root/pa1/pb3"));
        expected = 180L;
        assertEquals(expected, ponderatedWeightedConcept.get("root/pa2"));
        expected = 245L;
        assertEquals(expected, ponderatedWeightedConcept.get("root/pa2/pb1"));
        expected = 450L;
        assertEquals(expected, ponderatedWeightedConcept.get("root/pa2/pb1/c1"));
        expected = 525L;
        assertEquals(expected, ponderatedWeightedConcept.get("root/pa2/pb1/c2"));
        expected = 300L;
        assertEquals(expected, ponderatedWeightedConcept.get("root/pa2/pb2"));
    }

    @Test
    public void testPonderatesParentsWhithChildWeightManyChildren() {
        // Order should not change anything
        // Parents weight is not used
        // LinkedHashMap to keep insertion order (important for the tests)
        Map<String, Long> weightedConcepts = new LinkedHashMap<>();
        weightedConcepts.put("root/pa1/pb1/c1", 200L);
        weightedConcepts.put("root/pa1/pb1/c2", 200L);
        weightedConcepts.put("root/pa1/pb1/c3", 200L);
        weightedConcepts.put("root/pa1/pb1/c4", 200L);
        weightedConcepts.put("root/pa1/pb1/c5", 200L);
        weightedConcepts.put("root/pa1/pb1/c6", 200L);
        weightedConcepts.put("root/pa1/pb1/c7", 200L);
        weightedConcepts.put("root/pa1/pb1/c8", 200L);
        weightedConcepts.put("root/pa1/pb1/c9", 200L);
        weightedConcepts.put("root/pa1/pb1", 800L);
        weightedConcepts.put("root/pa1", 1000L);

        ConceptPonderator conceptPonderator = new ConceptPonderator(weightedConcepts);

        Map<String, Long> ponderatedWeightedConcept = conceptPonderator.ponderatesParentsWithChildWeight();

        Long expected = 189L;
        assertEquals(expected, ponderatedWeightedConcept.get("root/pa1/pb1/c1"));
        assertEquals(expected, ponderatedWeightedConcept.get("root/pa1/pb1/c2"));
        assertEquals(expected, ponderatedWeightedConcept.get("root/pa1/pb1/c3"));
        assertEquals(expected, ponderatedWeightedConcept.get("root/pa1/pb1/c4"));
        assertEquals(expected, ponderatedWeightedConcept.get("root/pa1/pb1/c5"));
        assertEquals(expected, ponderatedWeightedConcept.get("root/pa1/pb1/c6"));
        assertEquals(expected, ponderatedWeightedConcept.get("root/pa1/pb1/c7"));
        assertEquals(expected, ponderatedWeightedConcept.get("root/pa1/pb1/c8"));
        assertEquals(expected, ponderatedWeightedConcept.get("root/pa1/pb1/c9"));
        expected = 54L;
        assertEquals(expected, ponderatedWeightedConcept.get("root/pa1/pb1"));
        expected = 45L;
        assertEquals(expected, ponderatedWeightedConcept.get("root/pa1"));
    }

}
