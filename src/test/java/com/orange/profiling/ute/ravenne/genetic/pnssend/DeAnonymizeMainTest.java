package com.orange.profiling.ute.ravenne.genetic.pnssend;

import com.orange.profiling.common.utils.unit_tests.TestUtils;
import org.apache.log4j.BasicConfigurator;
import org.apache.log4j.Level;
import org.apache.log4j.Logger;
import org.junit.Before;
import org.junit.Test;

import java.io.File;
import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.fail;

public class DeAnonymizeMainTest {

	private static URI runtimeResourceFolder;

	 @Before
	    public void setUp() throws URISyntaxException {
	        System.setProperty("hadoop.home.dir", "/");
	        runtimeResourceFolder = DeAnonymizeMainTest.class.getResource("/ute-ravenne-genetic/").toURI();

	        BasicConfigurator.configure();
	        Logger.getRootLogger().setLevel(Level.ERROR);
	    }

	@Test
	public void testMainKo() {

		String[] args = new String[1];
		args[0] = "";
		try {
			DesAnonymizeMain.main(args);
			fail("Should have thrown an IllegalArgumentException because no parameters");
		} catch (Exception e) {
			assertEquals(e.getMessage(), "Takes 4 arguments : genetic optinPath gdprFlagValue outputDir");
		}
	}
//
	@Test
	public void testMainOk() throws IOException {

		final String inputGenetic = runtimeResourceFolder.resolve("pnssend/in/geneticAnonymized").getPath();
		final String optinCalculation = runtimeResourceFolder.resolve("pnssend/in/optin/optincalculation-r-00000").getPath();
		final String output = runtimeResourceFolder.resolve("out").getPath();
		final File geneticExpected = new File(runtimeResourceFolder.resolve("pnssend/expected"));

		String[] args = new String[4];
		args[0] = inputGenetic;
		args[1] = optinCalculation;
		args[2] = "1";
		args[3] = output;

		try {
			DesAnonymizeMain.main(args);
		} catch (Exception e) {
			e.printStackTrace();
		}
		List<String> expectedLines = TestUtils.getLinesFromPath(geneticExpected.getPath());
		List<String> outputLines = TestUtils.getLinesFromPath(output, "part*");
		assertEquals("Result has not the same lines as expected : \nExpected folder : " + geneticExpected
				+ "\nResult folder: " + output, expectedLines, outputLines);
	}
}
