package com.orange.profiling.ute.ravenne.util;

import static org.junit.Assert.*;

import org.junit.Before;
import org.junit.Test;

public class DefaultPredicateTest {
    private DefaultPredicate predicate;

    @Before
    public void setUp() {
        predicate = new DefaultPredicate();
        predicate.addAuthorizedPattern("sujet:sport:");
        predicate.addAuthorizedPattern("personnages:");
        predicate.addExcludedPattern("personnages:datascientist:");
        predicate.addExcludedPattern("categorie:dailyscrum:");
    }

    @Test
    public void testConceptsInAcceptedNotExcluded() {
        String concept = "sujet/sport";
        assertTrue(predicate.test(concept));
    }

    @Test
    public void testConceptsNotInAcceptedInExcluded() {
        String concept = "categorie/dailyscrum";
        assertFalse(predicate.test(concept));
    }

    @Test
    public void testConceptsInAcceptedInExcluded() {
        String concept = "personnages/datascientist";
        assertFalse(predicate.test(concept));
    }

    @Test
    public void testConceptsInAcceptedOtherInExcluded() {
        String concept = "personnages/developper";
        assertTrue(predicate.test(concept));
    }

    @Test
    public void testConceptsNotMentionned() {
        String concept = "ambiance et ton/super serieux";
        assertFalse(predicate.test(concept));
    }
}
