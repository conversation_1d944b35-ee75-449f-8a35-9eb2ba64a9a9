package com.orange.profiling.ute.ravenne.transformer;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.fail;

import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.BasicConfigurator;
import org.apache.log4j.Level;
import org.apache.log4j.Logger;
import org.junit.Before;
import org.junit.Test;

import com.orange.profiling.common.exceptions.FailedJobException;
import com.orange.profiling.common.utils.unit_tests.TestUtils;

public class MainTransformProfilingHashTest {

    private URI runtimeRessourceFolder;

    @Before
    public void setUp() throws URISyntaxException {

        runtimeRessourceFolder = MainTransformProfilingHashTest.class.getResource("/ute-ravenne-transformer/").toURI();

        BasicConfigurator.configure();
        Logger.getRootLogger().setLevel(Level.ERROR);
    }

    @Test
    public void testMainKo() {

        String[] args = { "" };
        try {
            MainTransformProfilingHash.main(args);
            fail("Should have thrown an IllegalArgumentException because no parameters");
        } catch (Exception e) {
            assertEquals(
                    "Takes 5 arguments : inputPathRavenneFiltered inputPathMappingProfilingHash beginYearWeek endYearWeek" +
                            " outputDir",
                    e.getMessage());
        }
    }

    @Test
    public final void testMainOk() throws ClassNotFoundException, IOException, InterruptedException, FailedJobException {

        final String inputFilterSelected = runtimeRessourceFolder.resolve("in/filtered/*/*/selected/*").getPath();
        final String inputPathMapping = runtimeRessourceFolder.resolve("in/mappingott/part-r-00001").getPath();
        final String outputDir = runtimeRessourceFolder.resolve("out").getPath();

        String[] args = { inputFilterSelected, inputPathMapping,
                "2019/16", "2019/17", outputDir};

        MainTransformProfilingHash.main(args);

        final String expected = runtimeRessourceFolder.resolve("expected").getPath();

        String[] weeks = { "2019/16", "2019/17" };

        for (String week : weeks) {

            // Test _Counters
            List<String> expectedCounters = TestUtils.getLinesFromPath(expected + "/" + week, "_COUNTERS");
            List<String> actualCounters = TestUtils.getLinesFromPath(outputDir + "/" + week , "_COUNTERS");
            assertEquals("Counters "+week, expectedCounters, actualCounters);

            // Test result selected transform zap
            List<String> expectedSelectedTransform = TestUtils.getLinesFromPath(expected + "/" + week, "part-*");
            List<String> actualSelectedTransform = TestUtils.getLinesFromPath(outputDir + "/" + week + "/" + MainTransformProfilingHash.OUT_SELECTED_TRANSFORM, "part-*");
            assertEquals("Selected Transforme zaps "+week, expectedSelectedTransform, actualSelectedTransform);

        }

    }

}