package com.orange.profiling.ute.ravenne.stat.countbytimeslot;

import java.io.IOException;

import org.apache.hadoop.io.Text;
import org.apache.hadoop.mrunit.mapreduce.MapDriver;
import org.apache.log4j.BasicConfigurator;
import org.apache.log4j.Level;
import org.apache.log4j.Logger;
import org.junit.Before;
import org.junit.Test;

public class ConceptsWeightsMapperTest {

    private static final String CONCEPTS = "c1=500,c2=450,c3=400,c4=350,c5=300,c6=250,"
            +"c7=200,c8=150,c9=100,c10=50,c11=45,c12=42";
    private static final String EXPECTED_VALUE = "1";

    private MapDriver<Object, Text, Text, Text> geneticProfilMapperDriver;

    @Before
    public void setUp() throws Exception {
        geneticProfilMapperDriver = MapDriver.newMapDriver(new ConceptsWeightsMapper());

        BasicConfigurator.configure();
        Logger.getRootLogger().setLevel(Level.ERROR);

    }

    @Test
    public void testWeekTimebox() throws IOException {
        String inputLine1 = "123456789\tw\t"+CONCEPTS+"\t1000\t200";
        geneticProfilMapperDriver.withInput(new Text(), new Text(inputLine1));

        String expectedKey = "w|12";
        geneticProfilMapperDriver.withOutput(new Text(expectedKey), new Text(EXPECTED_VALUE));

        geneticProfilMapperDriver.withCounter(NbProfilCounter.COUNTER_NB_GENETIC_PROFILES_GROUP,
                String.format(NbProfilCounter.COUNTER_NB_GENETIC_PROFILES_FORMAT, "w"), 1);
        geneticProfilMapperDriver.withCounter(NbProfilCounter.COUNTER_NB_BY_CONCEPTS_GROUP,
                String.format(NbProfilCounter.COUNTER_NB_BY_CONCEPTS_FORMAT, "w", 12), 0);

        geneticProfilMapperDriver.runTest();
    }

    @Test
    public void testDayTimebox() throws IOException {
        String inputLine1 = "123456789\td3\t"+CONCEPTS+"\t1000\t200";
        geneticProfilMapperDriver.withInput(new Text(), new Text(inputLine1));

        String expectedKey = "d3|12";
        geneticProfilMapperDriver.withOutput(new Text(expectedKey), new Text(EXPECTED_VALUE));

        geneticProfilMapperDriver.withCounter(NbProfilCounter.COUNTER_NB_GENETIC_PROFILES_GROUP,
                String.format(NbProfilCounter.COUNTER_NB_GENETIC_PROFILES_FORMAT, "d"), 1);
        geneticProfilMapperDriver.withCounter(NbProfilCounter.COUNTER_NB_BY_CONCEPTS_GROUP,
                String.format(NbProfilCounter.COUNTER_NB_BY_CONCEPTS_FORMAT, "d", 12), 0);

        geneticProfilMapperDriver.runTest();
    }

    @Test
    public void testPeriodTimebox() throws IOException {
        String inputLine1 = "123456789\td2p4\t"+CONCEPTS+"\t1000\t200";
        geneticProfilMapperDriver.withInput(new Text(), new Text(inputLine1));

        String expectedKey = "d2p4|12";
        geneticProfilMapperDriver.withOutput(new Text(expectedKey), new Text(EXPECTED_VALUE));

        geneticProfilMapperDriver.withCounter(NbProfilCounter.COUNTER_NB_GENETIC_PROFILES_GROUP,
                String.format(NbProfilCounter.COUNTER_NB_GENETIC_PROFILES_FORMAT, "dp"), 1);
        geneticProfilMapperDriver.withCounter(NbProfilCounter.COUNTER_NB_BY_CONCEPTS_GROUP,
                String.format(NbProfilCounter.COUNTER_NB_BY_CONCEPTS_FORMAT, "dp", 12), 0);

        geneticProfilMapperDriver.runTest();
    }

    @Test
    public void testTimeslotTimebox() throws IOException {
        String inputLine1 = "123456789\td5t8\t"+CONCEPTS+"\t1000\t200";
        geneticProfilMapperDriver.withInput(new Text(), new Text(inputLine1));

        String expectedKey = "d5t8|12";
        geneticProfilMapperDriver.withOutput(new Text(expectedKey), new Text(EXPECTED_VALUE));

        geneticProfilMapperDriver.withCounter(NbProfilCounter.COUNTER_NB_GENETIC_PROFILES_GROUP,
                String.format(NbProfilCounter.COUNTER_NB_GENETIC_PROFILES_FORMAT, "dt"), 1);
        geneticProfilMapperDriver.withCounter(NbProfilCounter.COUNTER_NB_BY_CONCEPTS_GROUP,
                String.format(NbProfilCounter.COUNTER_NB_BY_CONCEPTS_FORMAT, "dt", 12), 1);

        geneticProfilMapperDriver.runTest();
    }

    @Test
    public void testBadTimebox() throws IOException {
        String inputLine1 = "123456789\ttd\t"+CONCEPTS+"\t1000\t200";
        geneticProfilMapperDriver.withInput(new Text(), new Text(inputLine1));

        geneticProfilMapperDriver.runTest();
    }

    @Test
    public void testEmptyTimebox() throws IOException {
        String inputLine1 = "123456789\t\t"+CONCEPTS+"\t1000\t200";
        geneticProfilMapperDriver.withInput(new Text(), new Text(inputLine1));
        geneticProfilMapperDriver.runTest();
    }

    @Test
    public void testBadLengthTimebox() throws IOException {
        String inputLine1 = "123456789\td12\t"+CONCEPTS+"\t1000\t200";
        geneticProfilMapperDriver.withInput(new Text(), new Text(inputLine1));

        geneticProfilMapperDriver.runTest();
    }

    @Test
    public void testBadTimeslotTimebox() throws IOException {
        String inputLine1 = "123456789\td2r1\t"+CONCEPTS+"\t1000\t200";
        geneticProfilMapperDriver.withInput(new Text(), new Text(inputLine1));

        geneticProfilMapperDriver.runTest();
    }


    @Test
    public void testBadFormat() throws IOException {
        String inputLine1 = "123456789\td5t8\t"+CONCEPTS+"\t1000";
        geneticProfilMapperDriver.withInput(new Text(), new Text(inputLine1));

        geneticProfilMapperDriver.runTest();
    }

    @Test
    public void testNoConcepts() throws IOException {
        String inputLine1 = "123456789\td5t8\t\t1000\t200";
        geneticProfilMapperDriver.withInput(new Text(), new Text(inputLine1));

        String expectedKey = "d5t8|0";
        geneticProfilMapperDriver.withOutput(new Text(expectedKey), new Text(EXPECTED_VALUE));

        geneticProfilMapperDriver.withCounter(NbProfilCounter.COUNTER_NB_GENETIC_PROFILES_GROUP,
                String.format(NbProfilCounter.COUNTER_NB_GENETIC_PROFILES_FORMAT, "dt"), 1);
        geneticProfilMapperDriver.withCounter(NbProfilCounter.COUNTER_NB_BY_CONCEPTS_GROUP,
                String.format(NbProfilCounter.COUNTER_NB_BY_CONCEPTS_FORMAT, "dt", 0), 1);

        geneticProfilMapperDriver.runTest();
    }
}
