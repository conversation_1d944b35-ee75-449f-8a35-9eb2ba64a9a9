package com.orange.profiling.ute.ravenne.genetic.concat;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.PrintStream;
import java.util.ArrayList;
import java.util.List;

import org.apache.hadoop.io.Text;
import org.apache.hadoop.mrunit.mapreduce.ReduceDriver;
import org.apache.log4j.BasicConfigurator;
import org.apache.log4j.Level;
import org.apache.log4j.Logger;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

import com.orange.profiling.common.utils.FieldsUtils;
import com.orange.profiling.ute.ravenne.dao.TimeboxZapAggregation;

public class ConcatCombinerTest {
    private static final String DASH = FieldsUtils.DASH;
    private static final String TAB = FieldsUtils.TAB;
    private static final String OPTOUT_AID = "999999999";
    private static final String AID = "123456789";
    private static final String TIMEBOX = "d4t2";
    private ReduceDriver<Text, Text, Text, Text> combineDriver;
    private final ByteArrayOutputStream errContent = new ByteArrayOutputStream();

    @Before
    public void setUp() {

        BasicConfigurator.configure();
        Logger.getRootLogger().setLevel(Level.ERROR);

        ConcatCombiner combiner = new ConcatCombiner();
        combineDriver = ReduceDriver.newReduceDriver(combiner);

        setUpStreams();
    }

    public void setUpStreams() {
        System.setErr(new PrintStream(this.errContent));
        combineDriver.resetOutput();
    }

    @After
    public final void cleanUpStreams() {
        System.setErr(null);
    }

    @Test
    public void testAggregateOneConceptsWeightWithoutNb() throws IOException {
        Text key = new Text(OPTOUT_AID+DASH+TIMEBOX);

        String concepts = "ambiance et ton/humoristique=300,sujet/sport=200,type/live=100";
        long ratioVodLive = 50;
        int nbValues = 1;

        List<Text> values = new ArrayList<Text>();
        values.add(new Text(String.join(TAB, ConceptsWeightsMapper.CONCEPTS_MARK,
                concepts, Long.toString(ratioVodLive))));

        combineDriver.withInput(key, values);

        combineDriver.withOutput(key,
                new Text(String.join(TAB, ConceptsWeightsMapper.CONCEPTS_MARK,
                concepts, Long.toString(ratioVodLive), Integer.toString(nbValues)))
        );

        combineDriver.runTest();
    }

    @Test
    public void testAggregateOneConceptsWeightWithNb() throws IOException {
        Text key = new Text(OPTOUT_AID+DASH+TIMEBOX);

        String concepts = "ambiance et ton/humoristique=300,sujet/sport=200,type/live=100";
        long ratioVodLive = 50;
        int nbValues = 2;

        List<Text> values = new ArrayList<Text>();
        values.add(new Text(String.join(TAB, ConceptsWeightsMapper.CONCEPTS_MARK,
                concepts, Long.toString(ratioVodLive), Integer.toString(nbValues))));

        combineDriver.withInput(key, values);

        combineDriver.withOutput(key,
                new Text(String.join(TAB, ConceptsWeightsMapper.CONCEPTS_MARK,
                concepts, Long.toString(ratioVodLive), Integer.toString(nbValues)))
        );

        combineDriver.runTest();
    }

    @Test
    public void testAggregateOneConceptsWeightWithBadNb() throws IOException {
        Text key = new Text(OPTOUT_AID+DASH+TIMEBOX);

        String concepts = "ambiance et ton/humoristique=300,sujet/sport=200,type/live=100";
        long ratioVodLive = 50;
        int nbValues = 1;

        List<Text> values = new ArrayList<Text>();
        values.add(new Text(String.join(TAB, ConceptsWeightsMapper.CONCEPTS_MARK,
                concepts, Long.toString(ratioVodLive), "1A")));

        combineDriver.withInput(key, values);

        combineDriver.withOutput(key,
                new Text(String.join(TAB, ConceptsWeightsMapper.CONCEPTS_MARK,
                concepts, Long.toString(ratioVodLive), Integer.toString(nbValues)))
        );

        combineDriver.runTest();
    }

    @Test
    public void testAggregateThreeConceptsWeight() throws IOException {
        Text key = new Text(OPTOUT_AID+DASH+TIMEBOX);

        List<Text> values = new ArrayList<Text>();

        String concepts1 = "ambiance et ton/humoristique=300,sujet/sport=200,type/live=100";
        long ratioVodLive1 = 60;
        int nbValues1 = 2;
        values.add(new Text(String.join(TAB, ConceptsWeightsMapper.CONCEPTS_MARK,
                concepts1, Long.toString(ratioVodLive1), Integer.toString(nbValues1))));

        String concepts2 = "ambiance et ton/suspens=100,sujet/sport=100,type/série=200";
        long ratioVodLive2 = 40;
        int nbValues2 = 1;
        values.add(new Text(String.join(TAB, ConceptsWeightsMapper.CONCEPTS_MARK,
                concepts2, Long.toString(ratioVodLive2), Integer.toString(nbValues2))));

        String concepts3 = "genre/comédie=300,sujet/sport=100,type/film=100";
        long ratioVodLive3 = 80;
        int nbValues3 = 3;
        values.add(new Text(String.join(TAB, ConceptsWeightsMapper.CONCEPTS_MARK,
                concepts3, Long.toString(ratioVodLive3), Integer.toString(nbValues3))));


        String conceptsExpected = "sujet/sport=400,ambiance et ton/humoristique=300,genre/comédie=300,"
                + "type/série=200,ambiance et ton/suspens=100,type/film=100,type/live=100";
        long ratioVodLiveExpected = 180;
        int nbValuesExpected = 6;

        combineDriver.withInput(key, values);

        combineDriver.withOutput(key,
                new Text(String.join(TAB, ConceptsWeightsMapper.CONCEPTS_MARK,
                        conceptsExpected, Long.toString(ratioVodLiveExpected),
                        Integer.toString(nbValuesExpected)))
        );

        combineDriver.runTest();
    }

    @Test
    public void testAggregateOneLiveChannelsDurationsWithoutNb() throws IOException {
        Text key = new Text(OPTOUT_AID+DASH+TIMEBOX);

        String liveChannels = "1234=300,5678=200,7654=100";
        int nbLives = 1;

        List<Text> values = new ArrayList<Text>();
        values.add(new Text(String.join(TAB, TimeboxZapAggregation.LIVE,
                liveChannels)));

        combineDriver.withInput(key, values);

        combineDriver.withOutput(key,
                new Text(String.join(TAB, TimeboxZapAggregation.LIVE,
                        liveChannels, Integer.toString(nbLives)))
        );

        combineDriver.runTest();
    }

    @Test
    public void testAggregateOneLiveChannelsDurationsWithNb() throws IOException {
        Text key = new Text(OPTOUT_AID+DASH+TIMEBOX);

        String liveChannels = "1234=300,5678=200,7654=100";
        int nbLives = 2;

        List<Text> values = new ArrayList<Text>();
        values.add(new Text(String.join(TAB, TimeboxZapAggregation.LIVE,
                liveChannels, Integer.toString(nbLives))));

        combineDriver.withInput(key, values);

        combineDriver.withOutput(key,
                new Text(String.join(TAB, TimeboxZapAggregation.LIVE,
                        liveChannels, Integer.toString(nbLives)))
        );

        combineDriver.runTest();
    }

    @Test
    public void testAggregateOneLiveChannelsDurationsWithBadNb() throws IOException {
        Text key = new Text(OPTOUT_AID+DASH+TIMEBOX);

        String liveChannels = "1234=300,5678=200,7654=100";
        int nbLives = 1;

        List<Text> values = new ArrayList<Text>();
        values.add(new Text(String.join(TAB, TimeboxZapAggregation.LIVE,
                liveChannels, "1A")));

        combineDriver.withInput(key, values);

        combineDriver.withOutput(key,
                new Text(String.join(TAB, TimeboxZapAggregation.LIVE,
                        liveChannels, Integer.toString(nbLives)))
        );

        combineDriver.runTest();
    }

    @Test
    public void testAggregateThreeLiveChannelsDurations() throws IOException {
        Text key = new Text(OPTOUT_AID+DASH+TIMEBOX);

        List<Text> values = new ArrayList<Text>();

        String liveChannels1 = "1234=300,5678=200,7654=100";
        int nbLives1 = 2;
        values.add(new Text(String.join(TAB, TimeboxZapAggregation.LIVE,
                liveChannels1, Integer.toString(nbLives1))));

        String liveChannels2 = "2222=100,5678=100,9876=200";
        int nbLives2 = 1;
        values.add(new Text(String.join(TAB, TimeboxZapAggregation.LIVE,
                liveChannels2, Integer.toString(nbLives2))));

        String liveChannels3 = "3579=300,5678=100,6543=100";
        int nbLives3 = 3;
        values.add(new Text(String.join(TAB, TimeboxZapAggregation.LIVE,
                liveChannels3, Integer.toString(nbLives3))));


        String liveChannelsExpected = "5678=400,1234=300,3579=300,9876=200,"
                + "2222=100,6543=100,7654=100";
        int nbLivesExpected = 6;

        combineDriver.withInput(key, values);

        combineDriver.withOutput(key,
                new Text(String.join(TAB, TimeboxZapAggregation.LIVE,
                        liveChannelsExpected, Integer.toString(nbLivesExpected)))
        );

        combineDriver.runTest();
    }

    @Test
    public void testAggregateOneOdProvidersDurationsWithoutNb() throws IOException {
        Text key = new Text(OPTOUT_AID+DASH+TIMEBOX);

        String odProviders = "FTVOD=300,MYM6=200,TF1REPLAY=100";
        int nbOds = 1;

        List<Text> values = new ArrayList<Text>();
        values.add(new Text(String.join(TAB, TimeboxZapAggregation.VOD,
                odProviders)));

        combineDriver.withInput(key, values);

        combineDriver.withOutput(key,
                new Text(String.join(TAB, TimeboxZapAggregation.VOD,
                        odProviders, Integer.toString(nbOds)))
        );

        combineDriver.runTest();
    }

    @Test
    public void testAggregateOneOdProvidersDurationsWithNb() throws IOException {
        Text key = new Text(OPTOUT_AID+DASH+TIMEBOX);

        String odProviders = "FTVOD=300,MYM6=200,TF1REPLAY=100";
        int nbOds = 2;

        List<Text> values = new ArrayList<Text>();
        values.add(new Text(String.join(TAB, TimeboxZapAggregation.VOD,
                odProviders, Integer.toString(nbOds))));

        combineDriver.withInput(key, values);

        combineDriver.withOutput(key,
                new Text(String.join(TAB, TimeboxZapAggregation.VOD,
                        odProviders, Integer.toString(nbOds)))
        );

        combineDriver.runTest();
    }

    @Test
    public void testAggregateOneOdProvidersDurationsWithBadNb() throws IOException {
        Text key = new Text(OPTOUT_AID+DASH+TIMEBOX);

        String odProviders = "FTVOD=300,MYM6=200,TF1REPLAY=100";
        int nbOds = 1;

        List<Text> values = new ArrayList<Text>();
        values.add(new Text(String.join(TAB, TimeboxZapAggregation.VOD,
                odProviders, "1A")));

        combineDriver.withInput(key, values);

        combineDriver.withOutput(key,
                new Text(String.join(TAB, TimeboxZapAggregation.VOD,
                        odProviders, Integer.toString(nbOds)))
        );

        combineDriver.runTest();
    }

    @Test
    public void testAggregateThreeOdProvidersDurations() throws IOException {
        Text key = new Text(OPTOUT_AID+DASH+TIMEBOX);

        List<Text> values = new ArrayList<Text>();

        String odProviders1 = "FTVOD=300,MYM6=200,TF1REPLAY=100";
        int nbOds1 = 2;
        values.add(new Text(String.join(TAB, TimeboxZapAggregation.VOD,
                odProviders1, Integer.toString(nbOds1))));

        String odProviders2 = "GVU=100,MYM6=100,VOD24=200";
        int nbOds2 = 1;
        values.add(new Text(String.join(TAB, TimeboxZapAggregation.VOD,
                odProviders2, Integer.toString(nbOds2))));

        String odProviders3 = "ISEE=300,MYM6=100,SVOD=100";
        int nbOds3 = 3;
        values.add(new Text(String.join(TAB, TimeboxZapAggregation.VOD,
                odProviders3, Integer.toString(nbOds3))));

        String odProvidersExpected = "MYM6=400,FTVOD=300,ISEE=300,VOD24=200,"
                + "GVU=100,SVOD=100,TF1REPLAY=100";
        int nbOdsExpected = 6;

        combineDriver.withInput(key, values);

        combineDriver.withOutput(key,
                new Text(String.join(TAB, TimeboxZapAggregation.VOD,
                        odProvidersExpected, Integer.toString(nbOdsExpected)))
        );

        combineDriver.runTest();
    }


    /**
     * The goal of this test is to verify we add different information in the same zapAggregation
     * @throws IOException
     */
    @Test
    public void testAggregateDifferent() throws IOException {
        Text key = new Text(OPTOUT_AID+DASH+TIMEBOX);

        List<Text> values = new ArrayList<Text>();

        String concepts1 = "ambiance et ton/humoristique=300,sujet/sport=200,type/live=100";
        long ratioVodLive1 = 60;
        int nbValues1 = 2;
        values.add(new Text(String.join(TAB, ConceptsWeightsMapper.CONCEPTS_MARK,
                concepts1, Long.toString(ratioVodLive1), Integer.toString(nbValues1))));

        String concepts2 = "ambiance et ton/suspens=100,sujet/sport=100,type/série=200";
        long ratioVodLive2 = 40;
        int nbValues2 = 1;
        values.add(new Text(String.join(TAB, ConceptsWeightsMapper.CONCEPTS_MARK,
                concepts2, Long.toString(ratioVodLive2), Integer.toString(nbValues2))));

        String concepts3 = "genre/comédie=300,sujet/sport=100,type/film=100";
        long ratioVodLive3 = 80;
        int nbValues3 = 3;
        values.add(new Text(String.join(TAB, ConceptsWeightsMapper.CONCEPTS_MARK,
                concepts3, Long.toString(ratioVodLive3), Integer.toString(nbValues3))));


        String liveChannels1 = "1234=300,5678=200,7654=100";
        int nbLives1 = 2;
        values.add(new Text(String.join(TAB, TimeboxZapAggregation.LIVE,
                liveChannels1, Integer.toString(nbLives1))));

        String liveChannels2 = "2222=100,5678=100,9876=200";
        int nbLives2 = 1;
        values.add(new Text(String.join(TAB, TimeboxZapAggregation.LIVE,
                liveChannels2, Integer.toString(nbLives2))));

        String liveChannels3 = "3579=300,5678=100,6543=100";
        int nbLives3 = 3;
        values.add(new Text(String.join(TAB, TimeboxZapAggregation.LIVE,
                liveChannels3, Integer.toString(nbLives3))));


        String odProviders1 = "FTVOD=300,MYM6=200,TF1REPLAY=100";
        int nbOds1 = 2;
        values.add(new Text(String.join(TAB, TimeboxZapAggregation.VOD,
                odProviders1, Integer.toString(nbOds1))));

        String odProviders2 = "GVU=100,MYM6=100,VOD24=200";
        int nbOds2 = 1;
        values.add(new Text(String.join(TAB, TimeboxZapAggregation.VOD,
                odProviders2, Integer.toString(nbOds2))));

        String odProviders3 = "ISEE=300,MYM6=100,SVOD=100";
        int nbOds3 = 3;
        values.add(new Text(String.join(TAB, TimeboxZapAggregation.VOD,
                odProviders3, Integer.toString(nbOds3))));

        combineDriver.withInput(key, values);

        String conceptsExpected = "sujet/sport=400,ambiance et ton/humoristique=300,genre/comédie=300,"
                + "type/série=200,ambiance et ton/suspens=100,type/film=100,type/live=100";
        long ratioVodLiveExpected = 180;
        int nbValuesExpected = 6;

        combineDriver.withOutput(key,
                new Text(String.join(TAB, ConceptsWeightsMapper.CONCEPTS_MARK,
                        conceptsExpected,
                        Long.toString(ratioVodLiveExpected), Integer.toString(nbValuesExpected)))
        );

        String liveChannelsExpected = "5678=400,1234=300,3579=300,9876=200,"
                + "2222=100,6543=100,7654=100";
        int nbLivesExpected = 6;

        combineDriver.withOutput(key,
                new Text(String.join(TAB, TimeboxZapAggregation.LIVE,
                        liveChannelsExpected, Integer.toString(nbLivesExpected)))
        );

        String odProvidersExpected = "MYM6=400,FTVOD=300,ISEE=300,VOD24=200,"
                + "GVU=100,SVOD=100,TF1REPLAY=100";
        int nbOdsExpected = 6;

        combineDriver.withOutput(key,
                new Text(String.join(TAB, TimeboxZapAggregation.VOD,
                        odProvidersExpected, Integer.toString(nbOdsExpected)))
        );

        combineDriver.runTest();
    }

    /**
     * This test deals with aid not optout.
     * For these aid no aggregation is made as there should be only one value per aid-timebox
     * @throws IOException
     */
    @Test
    public void testNotOptout() throws IOException {
        Text key = new Text(AID+DASH+TIMEBOX);

        List<Text> values = new ArrayList<Text>();

        String concepts1 = "ambiance et ton/humoristique=300,sujet/sport=200,type/live=100";
        long ratioVodLive1 = 60;
        int nbValues1 = 2;
        values.add(new Text(String.join(TAB, ConceptsWeightsMapper.CONCEPTS_MARK,
                concepts1, Long.toString(ratioVodLive1), Integer.toString(nbValues1))));

        String concepts2 = "ambiance et ton/suspens=100,sujet/sport=100,type/série=200";
        long ratioVodLive2 = 40;
        values.add(new Text(String.join(TAB, ConceptsWeightsMapper.CONCEPTS_MARK,
                concepts2, Long.toString(ratioVodLive2))));

        String concepts3 = "genre/comédie=300,sujet/sport=100,type/film=100";
        long ratioVodLive3 = 80;
        int nbValues3 = 3;
        values.add(new Text(String.join(TAB, ConceptsWeightsMapper.CONCEPTS_MARK,
                concepts3, Long.toString(ratioVodLive3), Integer.toString(nbValues3))));


        String liveChannels1 = "1234=300,5678=200,7654=100";
        int nbLives1 = 2;
        values.add(new Text(String.join(TAB, TimeboxZapAggregation.LIVE,
                liveChannels1, Integer.toString(nbLives1))));

        String liveChannels2 = "2222=100,5678=100,9876=200";
        values.add(new Text(String.join(TAB, TimeboxZapAggregation.LIVE,
                liveChannels2)));

        String liveChannels3 = "3579=300,5678=100,6543=100";
        int nbLives3 = 3;
        values.add(new Text(String.join(TAB, TimeboxZapAggregation.LIVE,
                liveChannels3, Integer.toString(nbLives3))));


        String odProviders1 = "FTVOD=300,MYM6=200,TF1REPLAY=100";
        int nbOds1 = 2;
        values.add(new Text(String.join(TAB, TimeboxZapAggregation.VOD,
                odProviders1, Integer.toString(nbOds1))));

        String odProviders2 = "GVU=100,MYM6=100,VOD24=200";
        values.add(new Text(String.join(TAB, TimeboxZapAggregation.VOD,
                odProviders2)));

        String odProviders3 = "ISEE=300,MYM6=100,SVOD=100";
        int nbOds3 = 3;
        values.add(new Text(String.join(TAB, TimeboxZapAggregation.VOD,
                odProviders3, Integer.toString(nbOds3))));

        combineDriver.withInput(key, values);

        combineDriver.withOutput(key,
                new Text(String.join(TAB, ConceptsWeightsMapper.CONCEPTS_MARK,
                        concepts1, Long.toString(ratioVodLive1), Integer.toString(nbValues1)))
        );
        combineDriver.withOutput(key,
                new Text(String.join(TAB, ConceptsWeightsMapper.CONCEPTS_MARK,
                        concepts2, Long.toString(ratioVodLive2)))
        );
        combineDriver.withOutput(key,
                new Text(String.join(TAB, ConceptsWeightsMapper.CONCEPTS_MARK,
                        concepts3, Long.toString(ratioVodLive3), Integer.toString(nbValues3)))
        );

        combineDriver.withOutput(key,
                new Text(String.join(TAB, TimeboxZapAggregation.LIVE,
                        liveChannels1, Integer.toString(nbLives1)))
        );
        combineDriver.withOutput(key,
                new Text(String.join(TAB, TimeboxZapAggregation.LIVE,
                        liveChannels2))
        );
        combineDriver.withOutput(key,
                new Text(String.join(TAB, TimeboxZapAggregation.LIVE,
                        liveChannels3, Integer.toString(nbLives3)))
        );

        combineDriver.withOutput(key,
                new Text(String.join(TAB, TimeboxZapAggregation.VOD,
                        odProviders1, Integer.toString(nbOds1)))
        );
        combineDriver.withOutput(key,
                new Text(String.join(TAB, TimeboxZapAggregation.VOD,
                        odProviders2))
        );
        combineDriver.withOutput(key,
                new Text(String.join(TAB, TimeboxZapAggregation.VOD,
                        odProviders3, Integer.toString(nbOds3)))
        );

        combineDriver.runTest();
    }

    @Test
    public void testAggregateBadMark() throws IOException {
        Text key = new Text(OPTOUT_AID+DASH+TIMEBOX);

        String val = "FTVOD=300,sujet/sport=200,1234=100";
        int nb = 1;

        List<Text> values = new ArrayList<Text>();
        values.add(new Text(String.join(TAB, "ANY",
                val, "42")));

        combineDriver.withInput(key, values);

        combineDriver.runTest();
    }
}
