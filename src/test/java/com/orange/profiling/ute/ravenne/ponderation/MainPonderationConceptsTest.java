package com.orange.profiling.ute.ravenne.ponderation;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.fail;

import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;

import org.apache.log4j.BasicConfigurator;
import org.apache.log4j.Level;
import org.apache.log4j.Logger;
import org.junit.Before;
import org.junit.Test;

import com.orange.profiling.common.exceptions.FailedJobException;
import com.orange.profiling.common.utils.unit_tests.TestUtils;

public class MainPonderationConceptsTest {

    private URI runtimeRessourceFolder;
    private  String optinCalculationFile ;

    @Before
    public void setUp() throws URISyntaxException {
        runtimeRessourceFolder = MainPonderationConceptsTest.class.getResource("/ute-ravenne-ponderation/").toURI();

        optinCalculationFile = runtimeRessourceFolder.resolve("in/optincalculation-r-00000").getPath();

        BasicConfigurator.configure();
        Logger.getRootLogger().setLevel(Level.ERROR);
    }

    @Test
    public void testMainKo() {

        String[] args = { "" };
        try {
            MainPonderationConcepts.main(args);
            fail("Should have thrown an IllegalArgumentException because no parameters");
        } catch (Exception e) {
            assertEquals(
                    "Takes 10 arguments : inputPathRavenneFiltered processDate beginYearWeek endYearWeek " +
                            "predicateClass catalogConcepts filterMappingTable  inputOptinCalculation " +
                            "gdprFlagValue outputDir [topConcepts topLiveChannels topVodProviders]",
                            e.getMessage());
        }
    }

    @Test
    public final void testMainOkBestChannelAndBestCatchupID() throws ClassNotFoundException, IOException, InterruptedException, FailedJobException {

        final String inputFilterAndTimeslot = runtimeRessourceFolder.resolve("in2/filtered/*/*/selected/*").getPath();
        final String predicateClass = "com.orange.profiling.ute.ravenne.util.DefaultPredicate";
        final String catalogConcepts = runtimeRessourceFolder.resolve("in/acceptedExcludedConcepts.txt").getPath();
        final String conceptFilterMappingTablePath = runtimeRessourceFolder.resolve("in/conceptFilterMappingTableEmpty").getPath();

        final String output = runtimeRessourceFolder.resolve("out2").getPath();
        String[] args = { inputFilterAndTimeslot,
                "20200702", "2020/27", "2020/27",
                predicateClass, catalogConcepts,
                conceptFilterMappingTablePath,
                optinCalculationFile,
                "0",
                output, "20", "5", "5"};

        MainPonderationConcepts.main(args);

        final String expected = runtimeRessourceFolder.resolve("expected2/standard").getPath();

        // Test _Counters
        List<String> expectedCounters = TestUtils.getLinesFromPath(expected, "_COUNTERS");
        List<String> actualCounters = TestUtils.getLinesFromPath(output, "_COUNTERS");
        assertEquals("Counters", expectedCounters, actualCounters);

        // Test nbprogram
        List<String> expectedNbProgram = TestUtils.getLinesFromPath(expected, "part-nbprogram");
        List<String> actualNbProgram = TestUtils.getLinesFromPath(output+"/nbprogram", "part-*");
        assertEquals("NbProgram", expectedNbProgram, actualNbProgram);

        // Test conceptsweights
        List<String> expectedConceptsWeights = TestUtils.getLinesFromPath(expected, "part-conceptsweights");
        List<String> actualConceptsWeights = TestUtils.getLinesFromPath(output+"/conceptsweights", "d*", "w*");
        assertEquals("ConceptsWeights dt", expectedConceptsWeights, actualConceptsWeights);

        // Test bestLiveChannels
        List<String> expectedBestLiveChannels = TestUtils.getLinesFromPath(expected, "part-bestlivechannels");
        List<String> actualBestLiveChannels = TestUtils.getLinesFromPath(output+"/bestchannelsproviders/LIVE", "d*", "w*");
        assertEquals("BestLiveChannels dt", expectedBestLiveChannels, actualBestLiveChannels);

        // Test bestOdProviders
        List<String> expectedBestOdProviders = TestUtils.getLinesFromPath(expected, "part-bestodproviders");
        List<String> actualBestOdProviders = TestUtils.getLinesFromPath(output+"/bestchannelsproviders/VOD", "d*", "w*");
        assertEquals("BestOdProviders", expectedBestOdProviders, actualBestOdProviders);

    }

    @Test
    public final void testMainOk() throws ClassNotFoundException, IOException, InterruptedException, FailedJobException {

        final String inputFilterAndTimeslot = runtimeRessourceFolder.resolve("in/filtered/*/*/selected/*").getPath();
        final String predicateClass = "com.orange.profiling.ute.ravenne.util.DefaultPredicate";
        final String catalogConcepts = runtimeRessourceFolder.resolve("in/acceptedExcludedConcepts.txt").getPath();
        final String conceptFilterMappingTablePath = runtimeRessourceFolder.resolve("in/conceptFilterMappingTableEmpty").getPath();

        final String output = runtimeRessourceFolder.resolve("out").getPath();
        String[] args = { inputFilterAndTimeslot,
                "20190428", "2019/16", "2019/17",
                predicateClass, catalogConcepts,
                conceptFilterMappingTablePath,
                optinCalculationFile,
                "0",
                output, "20", "5", "5"};

        MainPonderationConcepts.main(args);

        final String expected = runtimeRessourceFolder.resolve("expected/standard").getPath();

        // Test _Counters
        List<String> expectedCounters = TestUtils.getLinesFromPath(expected, "_COUNTERS");
        List<String> actualCounters = TestUtils.getLinesFromPath(output, "_COUNTERS");
        assertEquals("Counters", expectedCounters, actualCounters);

        // Test nbprogram
        List<String> expectedNbProgram = TestUtils.getLinesFromPath(expected, "part-nbprogram");
        List<String> actualNbProgram = TestUtils.getLinesFromPath(output+"/nbprogram", "part-*");
        assertEquals("NbProgram", expectedNbProgram, actualNbProgram);

        // Test conceptsweights
        List<String> expectedConceptsWeights = TestUtils.getLinesFromPath(expected, "part-conceptsweights");
        List<String> actualConceptsWeights = TestUtils.getLinesFromPath(output+"/conceptsweights", "d*", "w*");
        assertEquals("ConceptsWeights dt", expectedConceptsWeights, actualConceptsWeights);

        // Test bestLiveChannels
        List<String> expectedBestLiveChannels = TestUtils.getLinesFromPath(expected, "part-bestlivechannels");
        List<String> actualBestLiveChannels = TestUtils.getLinesFromPath(output+"/bestchannelsproviders/LIVE", "d*", "w*");
        assertEquals("BestLiveChannels dt", expectedBestLiveChannels, actualBestLiveChannels);
    }

    @Test
    public final void testConditionalFilterEmptyMap()
            throws ClassNotFoundException, IOException, InterruptedException, FailedJobException {

        final String inputFilterAndTimeslot = runtimeRessourceFolder.resolve("in/filtered/*/*/selected/*").getPath();
        final String predicateClass = "com.orange.profiling.ute.ravenne.util.DefaultPredicate";
        final String catalogConcepts = runtimeRessourceFolder.resolve("in/acceptedExcludedConcepts.txt").getPath();

        final String emptyConceptFilterMappingTablePath =
                runtimeRessourceFolder.resolve("in/conceptFilterMappingTableEmpty").getPath();
        final String output =
                runtimeRessourceFolder.resolve("outputEmptyMap").getPath();

        String[] args = { inputFilterAndTimeslot,
                "20200228", "2020/1", "2020/1",
                predicateClass, catalogConcepts,
                emptyConceptFilterMappingTablePath,
                optinCalculationFile,
                "0",
                output, "20", "5", "5"};
        MainPonderationConcepts.main(args);

        final String expected = runtimeRessourceFolder.resolve(
                "expected/emptyMap").getPath();

        // Test conceptsweights
        List<String> expectedConceptsWeights = TestUtils.getLinesFromPath(expected, "part-conceptsweights");
        List<String> actualConceptsWeights = TestUtils.getLinesFromPath(output+"/conceptsweights", "d*", "w*");
        assertEquals("ConceptsWeights dt", expectedConceptsWeights, actualConceptsWeights);
    }

    @Test
    public final void testConditionalFilterNonEmptyMap()
            throws ClassNotFoundException, IOException, InterruptedException, FailedJobException {

        final String inputFilterAndTimeslot = runtimeRessourceFolder.resolve("in/filtered/*/*/selected/*").getPath();
        final String predicateClass = "com.orange.profiling.ute.ravenne.util.DefaultPredicate";
        final String catalogConcepts = runtimeRessourceFolder.resolve("in/acceptedExcludedConcepts.txt").getPath();

        final String nonEmptyConceptFilterMappingTablePath =
                runtimeRessourceFolder.resolve("in/conceptFilterMappingTable").getPath();
        final String output =
                runtimeRessourceFolder.resolve("outputNonEmptyMap").getPath();

        String[] args = { inputFilterAndTimeslot, "20200228", "2020/1", "2020/1",
                predicateClass, catalogConcepts, nonEmptyConceptFilterMappingTablePath,
                optinCalculationFile,
                "0",
                output, "20", "5", "5"};

        MainPonderationConcepts.main(args);

        final String expected = runtimeRessourceFolder.resolve(
                "expected/nonEmptyMap").getPath();

        // Test conceptsweights
        List<String> expectedConceptsWeights = TestUtils.getLinesFromPath(expected, "part-conceptsweights");
        List<String> actualConceptsWeights = TestUtils.getLinesFromPath(output+"/conceptsweights", "d*", "w*");
        assertEquals("ConceptsWeights dt", expectedConceptsWeights, actualConceptsWeights);

    }

}
