package com.orange.profiling.ute.ravenne.stat.countbyprovider;

import java.io.IOException;

import org.apache.hadoop.io.Text;
import org.apache.hadoop.mrunit.mapreduce.MapDriver;
import org.apache.log4j.BasicConfigurator;
import org.apache.log4j.Level;
import org.apache.log4j.Logger;
import org.junit.Before;
import org.junit.Test;

public class CountBestChannelsProvidersMapperTest {

    private static final String CHANNELS = "112=46069,226=27284,4=24055,192=23077,481=19660";
    private static final String TYPE_LIVE ="LIVE";
    private static final String EXPECTED_VALUE = "1";

    private MapDriver<Object, Text, Text, Text> mapperDriver;

    @Before
    public void setUp() throws Exception {
        mapperDriver = MapDriver.newMapDriver(new CountBestChannelsProvidersMapper());

        BasicConfigurator.configure();
        Logger.getRootLogger().setLevel(Level.ERROR);

    }

    @Test
    public void testWeekTimebox() throws IOException {
        String inputLine1 = "100002639\tw\t"+TYPE_LIVE+"\t"+CHANNELS;
        mapperDriver.withInput(new Text(), new Text(inputLine1));

        String expectedKey = "w|5";
        mapperDriver.withOutput(new Text(expectedKey), new Text(EXPECTED_VALUE));

        
        mapperDriver.withCounter(NbProfilCounter.COUNTER_NB_BY_CHANNELS_GROUP,
                String.format(NbProfilCounter.COUNTER_NB_BY_CHANNELS_FORMAT, "w", 5), 0);
        

        mapperDriver.runTest();
    }

    @Test
    public void testDayTimebox() throws IOException {
        String inputLine1 = "123456789\td3\t"+TYPE_LIVE+"\t"+CHANNELS;
        mapperDriver.withInput(new Text(), new Text(inputLine1));

        String expectedKey = "d3|5";
        mapperDriver.withOutput(new Text(expectedKey), new Text(EXPECTED_VALUE));

        mapperDriver.withCounter(NbProfilCounter.COUNTER_NB_BY_CHANNELS_GROUP,
                String.format(NbProfilCounter.COUNTER_NB_BY_CHANNELS_FORMAT, "w", 5), 0);
        
        mapperDriver.runTest();
    }

    @Test
    public void testPeriodTimebox() throws IOException {
        String inputLine1 = "123456789\td2p4\t"+TYPE_LIVE+"\t"+CHANNELS;
        mapperDriver.withInput(new Text(), new Text(inputLine1));

        String expectedKey = "d2p4|5";
        mapperDriver.withOutput(new Text(expectedKey), new Text(EXPECTED_VALUE));
        
        mapperDriver.withCounter(NbProfilCounter.COUNTER_NB_BY_CHANNELS_GROUP,
                String.format(NbProfilCounter.COUNTER_NB_BY_CHANNELS_FORMAT, "w", 5), 0);

        mapperDriver.runTest();
    }

    @Test
    public void testTimeslotTimebox() throws IOException {
        String inputLine1 = "123456789\td5t8\t"+TYPE_LIVE+"\t"+CHANNELS;
        mapperDriver.withInput(new Text(), new Text(inputLine1));

        String expectedKey = "d5t8|5";
        mapperDriver.withOutput(new Text(expectedKey), new Text(EXPECTED_VALUE));
        mapperDriver.withCounter(NbProfilCounter.COUNTER_NB_BY_CHANNELS_GROUP,
                String.format(NbProfilCounter.COUNTER_NB_BY_CHANNELS_FORMAT, "dt", 5), 1);

        mapperDriver.runTest();
    }

    @Test
    public void testBadTimebox() throws IOException {
        //String inputLine1 = "123456789\ttd\t"+TYPE_LIVE+"\t"+CHANNELS;
        String inputLine1 = "123456789\ttd\t"+CHANNELS+"\t1000\t200";
        mapperDriver.withInput(new Text(), new Text(inputLine1));

        mapperDriver.runTest();
    }

    @Test
    public void testEmptyTimebox() throws IOException {
        String inputLine1 = "123456789\t\t"+TYPE_LIVE+"\t"+CHANNELS;
        mapperDriver.withInput(new Text(), new Text(inputLine1));
        mapperDriver.runTest();
    }

    @Test
    public void testBadLengthTimebox() throws IOException {
        String inputLine1 = "123456789\td12\t"+TYPE_LIVE+"\t"+CHANNELS;
        mapperDriver.withInput(new Text(), new Text(inputLine1));

        mapperDriver.runTest();
    }

    @Test
    public void testBadTimeslotTimebox() throws IOException {
        String inputLine1 = "123456789\td2r1\t"+TYPE_LIVE+"\t"+CHANNELS;
        mapperDriver.withInput(new Text(), new Text(inputLine1));

        mapperDriver.runTest();
    }


    @Test
    public void testBadFormat() throws IOException {
        String inputLine1 = "123456789\td5t8\t"+CHANNELS;
        mapperDriver.withInput(new Text(), new Text(inputLine1));

        mapperDriver.runTest();
    }

    @Test
    public void testNoChannels() throws IOException {;
    	String inputLine1 = "123456789\td5t8\t"+TYPE_LIVE+"\t"+"";
        mapperDriver.withInput(new Text(), new Text(inputLine1));

        String expectedKey = "d5t8|0";
        mapperDriver.withOutput(new Text(expectedKey), new Text(EXPECTED_VALUE));
        
        mapperDriver.withCounter(NbProfilCounter.COUNTER_NB_BY_CHANNELS_GROUP,
                String.format(NbProfilCounter.COUNTER_NB_BY_CHANNELS_FORMAT, "w", 5), 0);
        
        mapperDriver.runTest();
    }
}
