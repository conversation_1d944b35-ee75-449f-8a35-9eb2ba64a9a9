package com.orange.profiling.ute.ravenne.filterandtimeslot;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.fail;

import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.log4j.BasicConfigurator;
import org.apache.log4j.Level;
import org.apache.log4j.Logger;
import org.junit.Before;
import org.junit.Test;

import com.orange.profiling.common.exceptions.FailedJobException;
import com.orange.profiling.common.utils.unit_tests.TestUtils;

/**
 * <AUTHOR>
 *
 */
public class MainFilterAndTimeslotTest {

    private URI runtimeRessourceFolder;

    @Before
    public void setUp() throws URISyntaxException {
        runtimeRessourceFolder = MainFilterAndTimeslotTest.class.getResource("/ute-ravenne-filterAndTimeslot/").toURI();

        BasicConfigurator.configure();
        Logger.getRootLogger().setLevel(Level.ERROR);
    }

    @Test
    public void testMainKo() {

        String[] args = new String[1];
        args[0] = "";
        try {
            MainFilterAndTimeslot.main(args);
            fail("Should have thrown an IllegalArgumentException because no parameters");
        }
        catch (Exception e) {
            assertEquals(
                    "Takes 5 arguments : inputPathProgByMac inputPathVod beginYearSlashWeek endYearSlashWeek outputDir",
                    e.getMessage());
        }
    }

    @Test
    public final void testMainOk()
            throws ClassNotFoundException, IOException, InterruptedException, FailedJobException {

        final String inputProgByMac = runtimeRessourceFolder.resolve("in/progbymac/*").getPath();
        final String inputVod = runtimeRessourceFolder.resolve("in/vod/*").getPath();
        final String output = runtimeRessourceFolder.resolve("output").getPath();

        String[] args = new String[5];
        args[0] = inputProgByMac;
        args[1] = inputVod;
        args[2] = "2019/28";
        args[3] = "2019/29";
        args[4] = output;

        final String expected = runtimeRessourceFolder.resolve("expected").getPath();

        MainFilterAndTimeslot.main(args);

        String[] weeks = { "2019/28", "2019/29" };

        for (String week : weeks) {
            // Test _Counters
            List<String> expectedCounters = TestUtils.getLinesFromPath(expected + "/" + week, "_COUNTERS");
            List<String> actualCounters = TestUtils.getLinesFromPath(output + "/" + week, "_COUNTERS");
            assertEquals("Counters "+week, expectedCounters, actualCounters);

            // Test result selected zap
            List<String> expectedSelected = TestUtils.getLinesFromPath(expected + "/" + week, "part-selected");
            List<String> actualSelected = TestUtils.getLinesFromPath(output + "/" + week + "/selected", "part*");
            assertEquals("Selected zaps "+week, expectedSelected, actualSelected);

            // Test result too short zaps
            List<String> expectedTooShort = TestUtils.getLinesFromPath(expected + "/" + week, "part-tooshort");
            List<String> actualTooShort = TestUtils.getLinesFromPath(output + "/" + week + "/tooshort", "part*");
            assertEquals("Too short zaps "+week, expectedTooShort, actualTooShort);
        }
    }

}