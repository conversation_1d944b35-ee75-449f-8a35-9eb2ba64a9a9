package com.orange.profiling.ute.ravenne.joinOeuvresConcepts;

import com.orange.profiling.common.exceptions.FailedJobException;
import com.orange.profiling.common.utils.unit_tests.TestUtils;
import com.orange.profiling.ute.ravenne.filterandtimeslot.MainFilterAndTimeslot;
import org.apache.log4j.BasicConfigurator;
import org.apache.log4j.Level;
import org.apache.log4j.Logger;
import org.junit.Before;
import org.junit.Test;

import java.io.BufferedReader;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.URI;
import java.net.URISyntaxException;
import java.nio.charset.MalformedInputException;
import java.nio.charset.StandardCharsets;
import java.nio.file.DirectoryStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.fail;

/**
 * <AUTHOR>
 *
 */
public class MainJoinOeuvresConceptsTest {

    private URI runtimeRessourceFolder;

    @Before
    public void setUp() throws URISyntaxException {
        runtimeRessourceFolder = MainJoinOeuvresConceptsTest.class.getResource("/ute-ravenne-joinoeuvresconcepts/").toURI();

        BasicConfigurator.configure();
        Logger.getRootLogger().setLevel(Level.ERROR);
    }

    @Test
    public void testMainKo() {

        String[] args = new String[1];
        args[0] = "";
        try {
            MainJoinOeuvresConcepts.main(args);
            fail("Should have thrown an IllegalArgumentException because no parameters");
        }
        catch (Exception e) {
            assertEquals(
                    "Takes 3 arguments: cataloguePath marqueursPath outputDir",
                    e.getMessage());
        }
    }

    @Test
    public final void testMainOk()
            throws ClassNotFoundException, IOException, InterruptedException, FailedJobException {

        final String marOeuvres = runtimeRessourceFolder.resolve("in/marOeuvres/*").getPath();
        final String catalogOeuvres = runtimeRessourceFolder.resolve("in/catalogOeuvres/*").getPath();
        final String output = runtimeRessourceFolder.resolve("output/").getPath();

        String[] args = new String[3];
        args[0] = catalogOeuvres;
        args[1] = marOeuvres;
        args[2] = output;

        final String expectedFile = runtimeRessourceFolder.resolve("expected").getPath();

        MainJoinOeuvresConcepts.main(args);
        
            List<String> expected = TestUtils.getLinesFromPath(expectedFile);
            List<String> actual = TestUtils.getLinesFromPath(output , "part*");
            assertEquals("test markers profiles", expected, actual);
            
        
    }
    
}