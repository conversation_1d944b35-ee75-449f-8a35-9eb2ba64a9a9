package com.orange.profiling.ute.ravenne.filterandtimeslot;

import static org.junit.Assert.assertEquals;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.PrintStream;
import org.apache.hadoop.io.LongWritable;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mrunit.mapreduce.MapDriver;
import org.apache.log4j.BasicConfigurator;
import org.apache.log4j.Level;
import org.apache.log4j.Logger;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

import com.orange.profiling.common.utils.FieldsUtils;

public class ProgbymacMapperTest {

    private static final String TAB = FieldsUtils.TAB;
    private static final String FIELDS_4_TO_9 = "divertissement" + TAB + "BFT-007-001"  + TAB + "TP" + TAB
            + "FRANCE 3" + TAB + "FRANCE 3 TNT" + TAB + "Chaînes de la TNT";
    private static final String FIELDS_11_TO_14 = "IPTV_EZ" + TAB + "title again"  + TAB + "S" + TAB + "E";
    private static final String FIELDS_12_TO_14 = "title again"  + TAB + "S" + TAB + "E";

    MapDriver<Object, Text, Text, Text> pbmMapDriver;

    private final ByteArrayOutputStream errContent = new ByteArrayOutputStream();

    @Before
    public void setUp() {

        ProgbymacMapper pbmMapper = new ProgbymacMapper();
        pbmMapDriver = MapDriver.newMapDriver(pbmMapper);

        BasicConfigurator.configure();
        Logger.getRootLogger().setLevel(Level.ERROR);
    }

    @Before
    public void setUpStreams() {
        System.setErr(new PrintStream(this.errContent));
    }

    @After
    public final void cleanUpStreams() {
        System.setErr(null);
    }

    @Test
    public final void testProgbymacMapperOkSingle() throws IOException {
        String aid = "123456789";
        String beginZap = "1562849879";
        String dayZap = "20190711";
        String zapDuration = "1842";
        String channelId="80";
        String title = "Title of program";
        String brodcastDuration = "2000";
        String concepts="catégories/divertissement,sous-genres/jeu";
        String externalEntertainmentId = "episode-Title of serie and episode-season-X-episode-Y-2000";
        String seasonName = "Saison X";
        String seriesName = "Title of serie";
        String offerName = "LIVE";
        String supportType = "IPTV_EZ";
        String device = "stb";

        pbmMapDriver.withInput(new LongWritable(), new Text(
                String.join(TAB, aid , beginZap , zapDuration , channelId , FIELDS_4_TO_9 , title ,
                        FIELDS_11_TO_14 , brodcastDuration , concepts,  "",
                        externalEntertainmentId, seasonName, seriesName, offerName, supportType ))
            );

        pbmMapDriver.withOutput(
                new Text(String.join(TAB,  aid , "LIVE" , brodcastDuration , channelId, "", title,
                        externalEntertainmentId, seasonName, seriesName, offerName, dayZap)),
                new Text(String.join(TAB,  beginZap , zapDuration , concepts, device))
            );
        pbmMapDriver.runTest();
    }

    @Test
    public final void testProgbymacMapperOkTwoZapSameProgramAroundMidnight() throws IOException {
        String aid = "123456789";
        String beginZap1 = "1562802600"; // 2019/07/10 23:50:00 ==> on day 20190710
        String beginZap2 = "1562809800"; // 2019/07/11 01:50:00 ==> minus 4 hour should be on day 20190710 (! minus 2h because Paris time)
        String dayZap = "20190710";
        String zapDuration1 = "642";
        String zapDuration2 = "768";
        String channelId="80";
        String title = "Title of program";
        String brodcastDuration = "2000";
        String concepts="catégories/divertissement,sous-genres/jeu";
        String programId="prog123";
        String externalEntertainmentId = "episode-Title of serie and episode-season-X-episode-Y-2000";
        String seasonName = "Saison X";
        String seriesName = "Title of serie";
        String offerName = "LIVE";
        String device = "stb";

        pbmMapDriver.withInput(new LongWritable(), new Text(
                String.join(TAB, aid , beginZap1 , zapDuration1 , channelId , FIELDS_4_TO_9 , title ,
                        FIELDS_11_TO_14 , brodcastDuration , concepts , programId,
                        externalEntertainmentId, seasonName, seriesName))
            );
        pbmMapDriver.withInput(new LongWritable(), new Text(
                String.join(TAB, aid , beginZap2 , zapDuration2 , channelId , FIELDS_4_TO_9 , title ,
                        FIELDS_11_TO_14 , brodcastDuration , concepts , programId,
                        externalEntertainmentId, seasonName, seriesName))
            );

        pbmMapDriver.withOutput(
                new Text(String.join(TAB,  aid , "LIVE" , brodcastDuration , channelId, programId, title
                        , externalEntertainmentId, seasonName, seriesName, offerName, dayZap)),
                new Text(String.join(TAB,  beginZap1 , zapDuration1 , concepts, device))
            );
        pbmMapDriver.withOutput(
                new Text(String.join(TAB,  aid , "LIVE" , brodcastDuration , channelId, programId, title,
                        externalEntertainmentId, seasonName, seriesName, offerName, dayZap)),
                new Text(String.join(TAB,  beginZap2 , zapDuration2 , concepts, device))
            );
        pbmMapDriver.runTest();
    }

    @Test
    public final void testProgbymacMapperNoBroadcastDurationAndConcepts() throws IOException {
        String aid = "123456789";
        String beginZap = "1562849879";
        String dayZap = "20190711";
        String zapDuration = "1842";
        String channelId="80";
        String title = "Title of program";
        String externalEntertainmentId = "episode-Title of serie and episode-season-X-episode-Y-2000";
        String seasonName = "Saison X";
        String seriesName = "Title of serie";
        String offerName = "LIVE";
        String device = "stb";

        pbmMapDriver.withInput(new LongWritable(), new Text(
                String.join(TAB, aid , beginZap , zapDuration , channelId , FIELDS_4_TO_9 , title ,
                        FIELDS_11_TO_14 , "" , "", "", externalEntertainmentId, seasonName, seriesName, offerName ))
            );
        pbmMapDriver.withOutput(
                new Text(String.join(TAB,  aid , "LIVE" , "" , channelId, "", title,
                        externalEntertainmentId, seasonName, seriesName,offerName, dayZap)),
                new Text(String.join(TAB,  beginZap , zapDuration , "", device))
            );

        pbmMapDriver.runTest();
    }
    @Test
    public final void testProgbymacMapperNoSupportTypeForStb() throws IOException {
        String aid = "123456789";
        String beginZap = "1562849879";
        String dayZap = "20190711";
        String zapDuration = "1842";
        String channelId="80";
        String title = "Title of program";
        String brodcastDuration = "2000";
        String concepts="catégories/divertissement,sous-genres/jeu";
        String externalEntertainmentId = "episode-Title of serie and episode-season-X-episode-Y-2000";
        String seasonName = "Saison X";
        String seriesName = "Title of serie";
        String offerName = "LIVE";
        String device = "stb";

        pbmMapDriver.withInput(new LongWritable(), new Text(
                String.join(TAB, aid , beginZap , zapDuration , channelId , FIELDS_4_TO_9 , title , "",
                        FIELDS_12_TO_14 , brodcastDuration , concepts,  "",
                        externalEntertainmentId, seasonName, seriesName, offerName ))
        );

        pbmMapDriver.withOutput(
                new Text(String.join(TAB,  aid , "LIVE" , brodcastDuration , channelId, "", title,
                        externalEntertainmentId, seasonName, seriesName, offerName, dayZap)),
                new Text(String.join(TAB,  beginZap , zapDuration , concepts, device))
        );
        pbmMapDriver.runTest();
    }

    @Test
    public final void testProgbymacMapperKoZapDuration0() throws IOException {
        String aid = "123456789";
        String beginZap = "1562849879";
        String zapDuration = "0";
        String channelId="80";
        String title = "Title of program";
        String brodcastDuration = "2000";
        String concepts="catégories/divertissement,sous-genres/jeu";
        String externalEntertainmentId = "episode-Title of serie and episode-season-X-episode-Y-2000";
        String seasonName = "Saison X";
        String seriesName = "Title of serie";
        String offerName = "LIVE";

        pbmMapDriver.withInput(new LongWritable(), new Text(
                String.join(TAB, aid , beginZap , zapDuration , channelId , FIELDS_4_TO_9 , title ,
                        FIELDS_11_TO_14 , brodcastDuration , concepts, externalEntertainmentId,
                        seasonName, seriesName,offerName ))
            );

        pbmMapDriver.withCounter(Counters.ZAP_WITHOUT_DURATION,1);

        pbmMapDriver.runTest();
    }

    @Test
    public final void testFilterChannelWithHasheValues() throws IOException {
        String aid = "123456789";
        String beginZap = "1562849879";
        String dayZap = "20190711";
        String zapDuration = "500";
        String channelId1="80";
        String channelId2="CANAL+";
        String channelId3="d41d8cd98f00b204e9800998ecf8427e";
        String title = "Title of program";
        String brodcastDuration = "2000";
        String concepts="catégories/divertissement,sous-genres/jeu";
        String externalEntertainmentId = "episode-Title of serie and episode-season-X-episode-Y-2000";
        String seasonName = "Saison X";
        String seriesName = "Title of serie";
        String offerName = "LIVE";
        String programId="prog123";
        String device = "stb";


        pbmMapDriver.withInput(new LongWritable(), new Text(
                String.join(TAB, aid , beginZap , zapDuration , channelId1 , FIELDS_4_TO_9 , title ,
                        FIELDS_11_TO_14 , brodcastDuration , concepts, programId,
                        externalEntertainmentId, seasonName, seriesName, offerName ))
        );

        pbmMapDriver.withInput(new LongWritable(), new Text(
                String.join(TAB, aid , beginZap , zapDuration , channelId2 , FIELDS_4_TO_9 , title ,
                        FIELDS_11_TO_14 , brodcastDuration , concepts, programId,
                        externalEntertainmentId, seasonName, seriesName, offerName ))
        );

        pbmMapDriver.withInput(new LongWritable(), new Text(
                String.join(TAB, aid , beginZap , zapDuration , channelId3 , FIELDS_4_TO_9 , title ,
                        FIELDS_11_TO_14 , brodcastDuration , concepts, programId,
                        externalEntertainmentId, seasonName, seriesName, offerName ))
        );

        pbmMapDriver.withCounter(Counters.CHANNELID_NOT_FOUND,2);

        pbmMapDriver.withOutput(
                new Text(String.join(TAB,  aid , "LIVE" , brodcastDuration , channelId1, programId, title,
                        externalEntertainmentId, seasonName, seriesName, offerName, dayZap)),
                new Text(String.join(TAB,  beginZap , zapDuration , concepts, device))
        );

        pbmMapDriver.runTest();
    }

    @Test
    public final void testFilterChannelWithISICode() throws IOException {
        String aid = "123456789";
        String beginZap = "1562849879";
        String dayZap = "20190711";
        String zapDuration = "500";
        String channelId1="80";
        String channelId2="CANAL+";
        String channelId3="ISI-25";
        String title = "Title of program";
        String brodcastDuration = "2000";
        String concepts="catégories/divertissement,sous-genres/jeu";
        String externalEntertainmentId = "episode-Title of serie and episode-season-X-episode-Y-2000";
        String seasonName = "Saison X";
        String seriesName = "Title of serie";
        String offerName = "LIVE";
        String programId="prog123";
        String device = "stb";


        pbmMapDriver.withInput(new LongWritable(), new Text(
                String.join(TAB, aid , beginZap , zapDuration , channelId1 , FIELDS_4_TO_9 , title ,
                        FIELDS_11_TO_14 , brodcastDuration , concepts, programId,
                        externalEntertainmentId, seasonName, seriesName, offerName ))
        );

        pbmMapDriver.withInput(new LongWritable(), new Text(
                String.join(TAB, aid , beginZap , zapDuration , channelId2 , FIELDS_4_TO_9 , title ,
                        FIELDS_11_TO_14 , brodcastDuration , concepts, programId,
                        externalEntertainmentId, seasonName, seriesName, offerName ))
        );

        pbmMapDriver.withInput(new LongWritable(), new Text(
                String.join(TAB, aid , beginZap , zapDuration , channelId3 , FIELDS_4_TO_9 , title ,
                        FIELDS_11_TO_14 , brodcastDuration , concepts, programId,
                        externalEntertainmentId, seasonName, seriesName, offerName ))
        );

        pbmMapDriver.withCounter(Counters.CHANNELID_NOT_FOUND,2);

        pbmMapDriver.withOutput(
                new Text(String.join(TAB,  aid , "LIVE" , brodcastDuration , channelId1, programId, title,
                        externalEntertainmentId, seasonName, seriesName, offerName, dayZap)),
                new Text(String.join(TAB,  beginZap , zapDuration , concepts, device))
        );

        pbmMapDriver.runTest();
    }
    
    @Test
    public final void testProgbymacMapperKoZapDurationLetter() throws IOException {
        String aid = "123456789";
        String beginZap = "1562849879";
        String zapDuration = "A";
        String channelId="80";
        String title = "Title of program";
        String brodcastDuration = "2000";
        String concepts="catégories/divertissement,sous-genres/jeu";
        String externalEntertainmentId = "episode-Title of serie and episode-season-X-episode-Y-2000";
        String seasonName = "Saison X";
        String seriesName = "Title of serie";
        String offerName = "LIVE";

        pbmMapDriver.withInput(new LongWritable(), new Text(
                String.join(TAB, aid , beginZap , zapDuration , channelId , FIELDS_4_TO_9 , title ,
                        FIELDS_11_TO_14 , brodcastDuration , concepts, externalEntertainmentId
                        , seasonName, seriesName, offerName ))
            );
        pbmMapDriver.withCounter(Counters.ZAP_WITHOUT_DURATION,1);

        pbmMapDriver.runTest();
    }

    @Test
    public final void testProgbymacMapperKoZapDurationEmpty() throws IOException {
        String aid = "123456789";
        String beginZap = "1562849879";
        String zapDuration = "";
        String channelId="80";
        String title = "Title of program";
        String brodcastDuration = "2000";
        String concepts="catégories/divertissement,sous-genres/jeu";
        String externalEntertainmentId = "episode-Title of serie and episode-season-X-episode-Y-2000";
        String seasonName = "Saison X";
        String seriesName = "Title of serie";
        String offerName = "LIVE";

        pbmMapDriver.withInput(new LongWritable(), new Text(
                String.join(TAB, aid , beginZap , zapDuration , channelId , FIELDS_4_TO_9 , title ,
                        FIELDS_11_TO_14 , brodcastDuration , concepts, externalEntertainmentId
                        , seasonName, seriesName, offerName))
            );

        pbmMapDriver.withCounter(Counters.ZAP_WITHOUT_DURATION,1);

        pbmMapDriver.runTest();
    }

    @Test
    public final void testDayOfProgramBeforeMidnight() {
        Long beginZap = 1562802600L; // 2019/07/10 23:50:00 ==> on day 20190710
        String expectedDayZap = "20190710";

        String actualDayZap = ProgbymacMapper.getDayOfProgram(beginZap);

        assertEquals(expectedDayZap, actualDayZap);

    }

    @Test
    public final void testDayOfProgramBefore2h() {
        Long beginZap = 1562809800L; // 2019/07/11 01:50:00 ==> on day 20190710
        String expectedDayZap = "20190710";

        String actualDayZap = ProgbymacMapper.getDayOfProgram(beginZap);

        assertEquals(expectedDayZap, actualDayZap);

    }

    @Test
    public final void testDayOfProgramBefore3h() {
        Long beginZap = 1562813400L; // 2019/07/11 02:50:00 ==> on day 20190710 if minus 4h but 20190711 because Paris hour, not GMT hour
        String expectedDayZap = "20190711"; // Could change on winter time !!!

        String actualDayZap = ProgbymacMapper.getDayOfProgram(beginZap);

        assertEquals(expectedDayZap, actualDayZap);

    }

    @Test
    public final void testDayOfProgramBefore4h() {
        Long beginZap = 1562817000L; // 2019/07/11 03:50:00 ==> on day 20190710 if minus 4h but 20190711 because Paris hour, not GMT hour
        String expectedDayZap = "20190711";

        String actualDayZap = ProgbymacMapper.getDayOfProgram(beginZap);

        assertEquals(expectedDayZap, actualDayZap);

    }

    @Test
    public final void testDayOfProgramAfter4h() {
        Long beginZap = 1562818200L; // 2019/07/11 04:10:00 ==> on day 20190711
        String expectedDayZap = "20190711";

        String actualDayZap = ProgbymacMapper.getDayOfProgram(beginZap);

        assertEquals(expectedDayZap, actualDayZap);

    }

    @Test
    public final void testProgbymacMapperOptinReco() throws IOException {
        String aid = "123456789";
        String beginZap = "1562849879";
        String dayZap = "20190711";
        String zapDuration = "1842";
        String channelId="80";
        String title = "Title of program";
        String brodcastDuration = "2000";
        String concepts= "catégories/divertissement,sous-genres/jeu";
        String externalEntertainmentId = "episode-Title of serie and episode-season-X-episode-Y-2000";
        String seasonName = "Saison X";
        String seriesName = "Title of serie";
        String entertainmentType = "LIVE";
        String mac = "";
        String usi = "";
        String origin = "" ;
        String optin = "1/0/0/0";
        String supportType = "IPTV_EZ";
        String device = "stb";

        pbmMapDriver.withInput(new LongWritable(), new Text(
                String.join(TAB, aid , beginZap , zapDuration , channelId , FIELDS_4_TO_9 , title ,
                        FIELDS_11_TO_14 , brodcastDuration , concepts,  "",
                        externalEntertainmentId, seasonName, seriesName, entertainmentType, mac, usi, origin, optin ))
        );

        pbmMapDriver.withOutput(
                new Text(String.join(TAB,  aid , "LIVE" , brodcastDuration , channelId, "", title,
                        externalEntertainmentId, seasonName, seriesName, entertainmentType, dayZap)),
                new Text(String.join(TAB,  beginZap , zapDuration , concepts, device))
        );

        pbmMapDriver.runTest();
    }
    @Test
    public final void testProgbymacMapperOptoutReco() throws IOException, InterruptedException {
        String aid = "123456789";
        String beginZap = "1562849879";
        String dayZap = "20190711";
        String zapDuration = "1842";
        String channelId="80";
        String title = "Title of program";
        String brodcastDuration = "2000";
        String concepts= "catégories/divertissement,sous-genres/jeu";
        String externalEntertainmentId = "episode-Title of serie and episode-season-X-episode-Y-2000";
        String seasonName = "Saison X";
        String seriesName = "Title of serie";
        String entertainmentType = "LIVE";
        String mac = "";
        String usi = "";
        String origin = "" ;
        String optin = "0/1/0/0";

        pbmMapDriver.withInput(new LongWritable(), new Text(
                String.join(TAB, aid , beginZap , zapDuration , channelId , FIELDS_4_TO_9 , title ,
                        FIELDS_11_TO_14 , brodcastDuration , concepts,  "",
                        externalEntertainmentId, seasonName, seriesName, entertainmentType, mac, usi, origin, optin ))
        );

        pbmMapDriver.withCounter(Counters.PROGBYMAC_OPTOUT_RECO, 1);

        pbmMapDriver.runTest();
    }
}
