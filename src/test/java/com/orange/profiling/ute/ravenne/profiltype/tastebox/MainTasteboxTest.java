package com.orange.profiling.ute.ravenne.profiltype.tastebox;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.fail;

import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;

import org.apache.log4j.BasicConfigurator;
import org.apache.log4j.Level;
import org.apache.log4j.Logger;
import org.junit.Before;
import org.junit.Test;

import com.orange.profiling.common.exceptions.FailedJobException;
import com.orange.profiling.common.utils.unit_tests.TestUtils;

/**
 * <AUTHOR>
 *
 */
public class MainTasteboxTest {
    private URI runtimeRessourceFolder;

    @Before
    public void setUp() throws URISyntaxException {
        runtimeRessourceFolder = MainTasteboxTest.class.getResource("/ute-ravenne-tastebox/").toURI();

        BasicConfigurator.configure();
        Logger.getRootLogger().setLevel(Level.ERROR);
    }

    @Test
    public void testMainKo() {

        String[] args = new String[1];
        args[0] = "";
        try {
            MainTastebox.main(args);
            fail("Should have thrown an IllegalArgumentException because no parameters");
        }
        catch (Exception e) {
            assertEquals(
                    "Takes 3 arguments : inputOptinWeights catalogConceptFamily outputDir",
                    e.getMessage());
        }
    }

    @Test
    public final void testMainOk()
            throws IOException, InterruptedException, ClassNotFoundException, FailedJobException, URISyntaxException {

        String inputTasteBox = runtimeRessourceFolder.resolve("in/pa*").getPath();
        String catalogFamily = runtimeRessourceFolder.resolve("in/conceptfamily.csv").getPath();
        String outputDir = runtimeRessourceFolder.resolve("out").getPath();
        String expected = runtimeRessourceFolder.resolve("expected").getPath();

        String[] args = {
                inputTasteBox,
                catalogFamily,
                outputDir
        };

        MainTastebox.main(args);
        // Test _Counters
        List<String> expectedCounters = TestUtils.getLinesFromPath(expected, "_COUNTERS");
        List<String> actualCounters = TestUtils.getLinesFromPath(outputDir, "_COUNTERS");
        assertEquals("Counters", expectedCounters, actualCounters);

        // Test tastebox
        List<String> expectedTastebox = TestUtils.getLinesFromPath(expected, "part-tastebox");
        List<String> actualTastebox = TestUtils.getLinesFromPath(outputDir, "part-*");
        assertEquals("tastebox", expectedTastebox, actualTastebox);

    }

}