package com.orange.profiling.ute.ravenne.genetic.concat;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.PrintStream;
import org.apache.hadoop.io.LongWritable;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mrunit.mapreduce.MapDriver;
import org.apache.log4j.BasicConfigurator;
import org.apache.log4j.Level;
import org.apache.log4j.Logger;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

import com.orange.profiling.common.utils.FieldsUtils;
import com.orange.profiling.ute.ravenne.genetic.Counters;

/**
 * <AUTHOR>
 */
public class ConceptsWeightsMapperTest {
    private static final String MARK = "CW";
    private static final String TAB = FieldsUtils.TAB;
    private static final String DASH = FieldsUtils.DASH;
    private static final String AID = "123456789";
    private static final String OPTOUT_AID = "999999999";
    private static final String PROCESS_DAY_VALUE = "5";
    private static final String TIMEBOX_OK = "d5t6";
    private static final String TIMEBOX_KO = "d4t5";
    private static final String TIMEBOX_J = "w_kids";

    private static final String CONCEPTS = "sujets/sujets=500,sujets/sport=750,sujets/rugby=1000";
    private static final String LIVE_DURATION = "2400";
    private static final String VOD_DURATION = "1400";
    private static final String RATIO = "37";

    MapDriver<Object, Text, Text, Text> mapperDriver;

    private final ByteArrayOutputStream errContent = new ByteArrayOutputStream();

    @Before
    public void setUp() {
        ConceptsWeightsMapper conceptsWeightsMapper = new ConceptsWeightsMapper();

        mapperDriver = MapDriver.newMapDriver(conceptsWeightsMapper);
        mapperDriver.getConfiguration().set(MainConcat.PROCESS_DAY, PROCESS_DAY_VALUE);

        BasicConfigurator.configure();
        Logger.getRootLogger().setLevel(Level.ERROR);
    }

    @Before
    public void setUpStreams() {
        System.setErr(new PrintStream(this.errContent));
    }

    @After
    public final void cleanUpStreams() {
        System.setErr(null);
    }

    @Test
    public final void testConceptsWeightsMapper() throws IOException {

        mapperDriver.withInput(new LongWritable(),
                new Text(AID+TAB+TIMEBOX_OK+TAB+CONCEPTS+TAB+LIVE_DURATION+TAB+VOD_DURATION));

        mapperDriver.withOutput(new Text(AID+"-"+TIMEBOX_OK),
                new Text(MARK+TAB+CONCEPTS+TAB+RATIO));
        mapperDriver.withOutput(new Text(OPTOUT_AID+"-"+TIMEBOX_OK),
                new Text(MARK+TAB+CONCEPTS+TAB+RATIO));

        mapperDriver.runTest();
    }

    @Test
    public final void testConceptsWeightsMapperNotInTargetDay() throws IOException {

        mapperDriver.withInput(new LongWritable(),
                new Text(AID+TAB+TIMEBOX_KO+TAB+CONCEPTS+TAB+LIVE_DURATION+TAB+VOD_DURATION));

        mapperDriver.withCounter(Counters.CONCEPTS_NOT_IN_TARGET_DAY, 1);

        mapperDriver.runTest();
    }

    @Test
    public final void testConceptsWeightsMapperBadLength() throws IOException {
        String aid = "123456789";
        String timeslot = "d2t3";
        String concepts = "sujets/sujets=500,sujets/sport=750,sujets/rugby=1000";
        String liveDuration = "2400";

        mapperDriver.withInput(new LongWritable(),
                new Text(AID+TAB+TIMEBOX_OK+TAB+CONCEPTS+TAB+LIVE_DURATION));

        mapperDriver.withCounter(Counters.BAD_FORMAT, 1);

        mapperDriver.runTest();
    }

    @Test
    public final void testConceptsWeightsEmptyDuration() throws IOException {

        mapperDriver.withInput(new LongWritable(),
                new Text(AID+TAB+TIMEBOX_OK+TAB+CONCEPTS+TAB+TAB+VOD_DURATION));

        mapperDriver.withOutput(new Text(AID+"-"+TIMEBOX_OK),
                new Text(MARK+TAB+CONCEPTS+TAB+"100"));
        mapperDriver.withOutput(new Text(OPTOUT_AID+"-"+TIMEBOX_OK),
                new Text(MARK+TAB+CONCEPTS+TAB+"100"));

        mapperDriver.runTest();
    }

    @Test
    public final void testConceptsWeightsLetterDuration() throws IOException {

        mapperDriver.withInput(new LongWritable(),
                new Text(AID+TAB+TIMEBOX_OK+TAB+CONCEPTS+TAB+"2A004"+TAB+VOD_DURATION));

        mapperDriver.withOutput(new Text(AID+"-"+TIMEBOX_OK),
                new Text(MARK+TAB+CONCEPTS+TAB+"100"));
        mapperDriver.withOutput(new Text(OPTOUT_AID+"-"+TIMEBOX_OK),
                new Text(MARK+TAB+CONCEPTS+TAB+"100"));

        mapperDriver.runTest();
    }

    @Test
    public final void testConceptsWeightsZeroDuration() throws IOException {

        mapperDriver.withInput(new LongWritable(),
                new Text(AID+TAB+TIMEBOX_OK+TAB+CONCEPTS+TAB+"0"+TAB+"0"));

        mapperDriver.withOutput(new Text(AID+"-"+TIMEBOX_OK),
                new Text(MARK+TAB+CONCEPTS+TAB+"0"));
        mapperDriver.withOutput(new Text(OPTOUT_AID+"-"+TIMEBOX_OK),
                new Text(MARK+TAB+CONCEPTS+TAB+"0"));

        mapperDriver.runTest();
    }


    @Test
    public final void testConceptsTimeboxJeunesse() throws IOException {

        mapperDriver.withInput(new LongWritable(),
                new Text(AID+TAB+TIMEBOX_J+TAB+CONCEPTS+TAB+LIVE_DURATION+TAB+VOD_DURATION));

        mapperDriver.withOutput(new Text(AID+"-"+TIMEBOX_J),
                new Text(MARK+TAB+CONCEPTS+TAB+RATIO));
        mapperDriver.withOutput(new Text(OPTOUT_AID+"-"+TIMEBOX_J),
                new Text(MARK+TAB+CONCEPTS+TAB+RATIO));

        mapperDriver.runTest();
    }
}
