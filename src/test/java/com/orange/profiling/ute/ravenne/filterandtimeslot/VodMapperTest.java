package com.orange.profiling.ute.ravenne.filterandtimeslot;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.PrintStream;
import java.util.StringJoiner;

import org.apache.hadoop.io.LongWritable;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mrunit.mapreduce.MapDriver;
import org.apache.log4j.BasicConfigurator;
import org.apache.log4j.Level;
import org.apache.log4j.Logger;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

import com.orange.profiling.common.utils.FieldsUtils;


public class VodMapperTest {

    private static final String TAB = FieldsUtils.TAB;
    private static final String FIELDS_4_TO_6 = "comédie$horreur" + TAB + "null"  + TAB + "12";
    private static final String FIELDS_9_TO_12 = "S" + TAB + "E" + TAB + "Serie Title" + TAB + "id serie";

    MapDriver<Object, Text, Text, Text> vodMapDriver;

    private final ByteArrayOutputStream errContent = new ByteArrayOutputStream();

    @Before
    public void setUp() {

        VodMapper vodMapper = new VodMapper();
        vodMapDriver = MapDriver.newMapDriver(vodMapper);

        BasicConfigurator.configure();
        Logger.getRootLogger().setLevel(Level.ERROR);
    }

    @Before
    public void setUpStreams() {
        System.setErr(new PrintStream(this.errContent));
    }

    @After
    public final void cleanUpStreams() {
        System.setErr(null);
    }

    @Test
    public final void testVodMapperOkSingle() throws IOException {
        String aid = "123456789";
        String beginZap = "**********";
        String zapDuration = "1842";
        String serviceCode = "2424VIDEO";
        String contentId="ASHVSEV0108W0108644_S_2424VIDEO_1";
        String title = "Title of program";
        String brodcastDuration = "2000";
        String concepts="catégories/divertissement,sous-genres/jeu";
        String typeVod = "vod";
        String opusCode = "ASHVSEV0108W0108644";
        String onDemandExternalId = "CMS_WONT0000527373567000";
        String externalEntertainmentId = "episode-Title of serie and episode-season-X-episode-Y-2000";
        String entertainmentSeasonName = "season x";
        String entertainmentSeriesName = "episode x";
        String catchupChannelId = "catchuptv_xxx";
        String externalAssetId = "ASHVSEV0108W0108644";
        String entertainmentType = "episode";
        String entertainmentOfferName = "VOD";
        String macadress = "X.X.X.X";
        String supportType = "IPTV_EZ";
        String device = "stb";


        vodMapDriver.withInput(new LongWritable(), new Text(
                String.join(TAB, aid , beginZap , zapDuration , contentId , FIELDS_4_TO_6 , title ,
                        serviceCode, FIELDS_9_TO_12 , brodcastDuration , concepts, typeVod, opusCode,
                        onDemandExternalId, externalEntertainmentId, entertainmentSeasonName, entertainmentSeriesName,
                        catchupChannelId,externalAssetId, entertainmentType, entertainmentOfferName, macadress, supportType))
                );

        StringJoiner sj = new StringJoiner(FieldsUtils.DASH);
        String serviceCodeChannel = sj.add(serviceCode).add(externalAssetId.substring(0,3)).toString();
        String fullProvider = String.join(FieldsUtils.PIPE, serviceCodeChannel, catchupChannelId);
        vodMapDriver.withOutput(
                new Text(String.join(TAB,  aid , "VOD" , brodcastDuration , fullProvider, contentId, title,
                        externalEntertainmentId, entertainmentSeasonName, entertainmentSeriesName, entertainmentOfferName, "0")),
                new Text(String.join(TAB,  beginZap , zapDuration , concepts, device))
                );
        vodMapDriver.runTest();
    }

    @Test
    public final void testVodMapperNoBroadcastDurationAndConcepts() throws IOException {
        String aid = "123456789";
        String beginZap = "**********";
        String zapDuration = "1842";
        String serviceCode = "2424VIDEO";
        String contentId="ASHVSEV0108W0108644_S_2424VIDEO_1";
        String title = "Title of program";
        String typeVod = "vod";
        String opusCode = "ASHVSEV0108W0108644";
        String onDemandExternalId = "CMS_WONT0000527373567000";
        String externalEntertainmentId = "episode-Title of serie and episode-season-X-episode-Y-2000";
        StringJoiner sj = new StringJoiner(FieldsUtils.DASH);
        String seasonName = "Saison X";
        String seriesName = "Title of serie";
        String catchupChannelId = "catchuptv_x";
        String externalAssetId = "ASHVSEV0108W0108644";
        String entertainmentType = "episode";
        String entertainmentOfferName = "VOD";
        String macadress = "X.X.X.X";
        String supportType = "IPTV_EZ";
        String device = "stb";


        String serviceCodeChannel = sj.add(serviceCode).add(externalAssetId.substring(0,3)).toString();
        String fullProvider = String.join(FieldsUtils.PIPE, serviceCodeChannel, catchupChannelId);

        vodMapDriver.withInput(new LongWritable(), new Text(
                String.join(TAB, aid , beginZap , zapDuration , contentId , FIELDS_4_TO_6 , title ,
                        serviceCode, FIELDS_9_TO_12 , "" , "", typeVod, opusCode,
                        onDemandExternalId, externalEntertainmentId, seasonName, seriesName, catchupChannelId
                        ,externalAssetId, entertainmentType, entertainmentOfferName, macadress, supportType)));
        vodMapDriver.withOutput(
                new Text(String.join(TAB,  aid , "VOD" , "" , fullProvider, contentId, title,
                        externalEntertainmentId, seasonName, seriesName, entertainmentOfferName, "0")),
                new Text(String.join(TAB,  beginZap , zapDuration , "", device))
                );

        vodMapDriver.runTest();
    }
    @Test
    public final void testVodMapperNoSupportType() throws IOException {
        String aid = "123456789";
        String beginZap = "**********";
        String zapDuration = "1842";
        String serviceCode = "2424VIDEO";
        String contentId="ASHVSEV0108W0108644_S_2424VIDEO_1";
        String title = "Title of program";
        String brodcastDuration = "2000";
        String concepts="catégories/divertissement,sous-genres/jeu";
        String typeVod = "vod";
        String opusCode = "ASHVSEV0108W0108644";
        String onDemandExternalId = "CMS_WONT0000527373567000";
        String externalEntertainmentId = "episode-Title of serie and episode-season-X-episode-Y-2000";
        String entertainmentSeasonName = "season x";
        String entertainmentSeriesName = "episode x";
        String catchupChannelId = "catchuptv_xxx";
        String externalAssetId = "ASHVSEV0108W0108644";
        String entertainmentType = "episode";
        String entertainmentOfferName = "VOD";
        String macadress = "X.X.X.X";
        String device = "stb";


        vodMapDriver.withInput(new LongWritable(), new Text(
                String.join(TAB, aid , beginZap , zapDuration , contentId , FIELDS_4_TO_6 , title ,
                        serviceCode, FIELDS_9_TO_12 , brodcastDuration , concepts, typeVod, opusCode,
                        onDemandExternalId, externalEntertainmentId, entertainmentSeasonName, entertainmentSeriesName,
                        catchupChannelId,externalAssetId, entertainmentType, entertainmentOfferName, macadress))
        );

        StringJoiner sj = new StringJoiner(FieldsUtils.DASH);
        String serviceCodeChannel = sj.add(serviceCode).add(externalAssetId.substring(0,3)).toString();
        String fullProvider = String.join(FieldsUtils.PIPE, serviceCodeChannel, catchupChannelId);
        vodMapDriver.withOutput(
                new Text(String.join(TAB,  aid , "VOD" , brodcastDuration , fullProvider, contentId, title,
                        externalEntertainmentId, entertainmentSeasonName, entertainmentSeriesName, entertainmentOfferName, "0")),
                new Text(String.join(TAB,  beginZap , zapDuration , concepts, device))
        );
        vodMapDriver.runTest();
    }

    @Test
    public final void testVodMapperKoZapDuration0() throws IOException {
        String aid = "123456789";
        String beginZap = "**********";
        String zapDuration = "0";
        String serviceCode = "2424VIDEO";
        String contentId="ASHVSEV0108W0108644_S_2424VIDEO_1";
        String title = "Title of program";
        String brodcastDuration = "2000";
        String concepts="catégories/divertissement,sous-genres/jeu";
        String onDemandExternalId = "CMS_WONT0000527373567000";
        String externalEntertainmentId = "episode-Title of serie and episode-season-X-episode-Y-2000";
        String seasonName = "Saison X";
        String seriesName = "Title of serie";
        String catchupChannelId = "catchuptv_x";
        String externalAssetId = "ASHVSEV0108W0108644";
        String entertainmentType = "episode";
        String entertainmentOfferName = "VOD";
        String macadress = "X.X.X.X";
        String supportType = "IPTV_EZ";

        vodMapDriver.withInput(new LongWritable(), new Text(
                String.join(TAB, aid , beginZap , zapDuration , contentId , FIELDS_4_TO_6 , title ,
                        serviceCode, FIELDS_9_TO_12 , brodcastDuration , concepts,
                        onDemandExternalId, externalEntertainmentId, seasonName, seriesName, catchupChannelId
                        ,externalAssetId, entertainmentType, entertainmentOfferName, macadress, supportType))
                );

        vodMapDriver.withCounter(Counters.VOD_WITHOUT_DURATION, 1);
        vodMapDriver.runTest();
    }

    @Test
    public final void testVodMapperKoZapDurationLetter() throws IOException {
        String aid = "123456789";
        String beginZap = "**********";
        String zapDuration = "A";
        String serviceCode = "2424VIDEO";
        String contentId="ASHVSEV0108W0108644_S_2424VIDEO_1";
        String title = "Title of program";
        String brodcastDuration = "2000";
        String concepts="catégories/divertissement,sous-genres/jeu";
        String typeVod = "vod";
        String externalAssetId = "ASHVSEV0108W0108644";
        String onDemandExternalId = "CMS_WONT0000527373567000";
        String externalEntertainmentId = "episode-Title of serie and episode-season-X-episode-Y-2000";
        String seasonName = "Saison X";
        String seriesName = "Title of serie";

        vodMapDriver.withInput(new LongWritable(), new Text(
                String.join(TAB, aid , beginZap , zapDuration , contentId , FIELDS_4_TO_6 , title ,
                        serviceCode, FIELDS_9_TO_12 , brodcastDuration , concepts, typeVod, externalAssetId,
                        onDemandExternalId, externalEntertainmentId, seasonName, seriesName))
                );

        vodMapDriver.withCounter(Counters.VOD_WITHOUT_DURATION, 1);
        vodMapDriver.runTest();
    }

    @Test
    public final void testVodMapperKoZapDurationEmpty() throws IOException {
        String aid = "123456789";
        String beginZap = "**********";
        String zapDuration = "";
        String serviceCode = "2424VIDEO";
        String contentId="ASHVSEV0108W0108644_S_2424VIDEO_1";
        String title = "Title of program";
        String brodcastDuration = "2000";
        String concepts="catégories/divertissement,sous-genres/jeu";
        String typeVod = "vod";
        String externalAssetId = "ASHVSEV0108W0108644";
        String onDemandExternalId = "CMS_WONT0000527373567000";
        String externalEntertainmentId = "episode-Title of serie and episode-season-X-episode-Y-2000";
        String seasonName = "Saison X";
        String seriesName = "Title of serie";
        String catchupChannelId = "catchuptv_x";
        String entertainmentType = "episode";
        String entertainmentOfferName = "VOD";
        String macadress = "X.X.X.X";

        vodMapDriver.withInput(new LongWritable(), new Text(
                String.join(TAB, aid , beginZap , zapDuration , contentId , FIELDS_4_TO_6 , title ,
                        serviceCode, FIELDS_9_TO_12 , brodcastDuration , concepts, typeVod,
                        onDemandExternalId, externalEntertainmentId, seasonName, seriesName, catchupChannelId
                        ,externalAssetId, entertainmentType, entertainmentOfferName, macadress))
                );

        vodMapDriver.withCounter(Counters.VOD_WITHOUT_DURATION, 1);
        vodMapDriver.runTest();
    }

    @Test
    public final void testVodMapperOkSpecificChannel() throws IOException {
        String aid = "123456789";
        String beginZap = "**********";
        String zapDuration = "1842";
        String serviceCode = "TVOD1";
        String contentId="D8CU0031942S_S_TVOD1_1";
        String title = "Title of program";
        String brodcastDuration = "2000";
        String concepts="catégories/divertissement,sous-genres/jeu";
        String typeVod = "vod";
        String opusCode = "D8CU0031942S";
        String onDemandExternalId = "CMS_WONT0000527373567000";
        String externalEntertainmentId = "episode-Title of serie and episode-season-X-episode-Y-2000";
        String seasonName = "Saison X";
        String seriesName = "Title of serie";
        String catchupChannelId = "";
        String externalAssetId = "D8CU0031942S";
        String entertainmentType = "";
        String entertainmentOfferName = "";
        String macadress = "X.X.X.X";
        String supportType = "IPTV_EZ";
        String device = "stb";

        vodMapDriver.withInput(new LongWritable(), new Text(
                String.join(TAB, aid , beginZap , zapDuration , contentId , FIELDS_4_TO_6 , title ,
                        serviceCode, FIELDS_9_TO_12 , brodcastDuration , concepts, typeVod, opusCode,
                        onDemandExternalId, externalEntertainmentId, seasonName, seriesName, catchupChannelId,
                        externalAssetId, entertainmentType, entertainmentOfferName, macadress, supportType))
                );

        String serviceCodeChannel = String.join(FieldsUtils.DASH, serviceCode, externalAssetId.substring(0,2));
        String fullProvider = String.join(FieldsUtils.PIPE, serviceCodeChannel, "");
        vodMapDriver.withOutput(
                new Text(String.join(TAB,  aid , "VOD" , brodcastDuration , fullProvider, contentId, title,
                        externalEntertainmentId, seasonName, seriesName,"", "0")),
                new Text(String.join(TAB,  beginZap , zapDuration , concepts, device))
                );
        vodMapDriver.runTest();
    }

    @Test
    public final void testMapOptintReco() throws IOException, InterruptedException {
        String aid = "123456789";
        String beginZap = "**********";
        String zapDuration = "1842";
        String serviceCode = "2424VIDEO";
        String contentId="ASHVSEV0108W0108644_S_2424VIDEO_1";
        String title = "Title of program";
        String brodcastDuration = "2000";
        String concepts="catégories/divertissement,sous-genres/jeu";
        String typeVod = "vod";
        String opusCode = "ASHVSEV0108W0108644";
        String onDemandExternalId = "CMS_WONT0000527373567000";
        String externalEntertainmentId = "episode-Title of serie and episode-season-X-episode-Y-2000";
        String entertainmentSeasonName = "season x";
        String entertainmentSeriesName = "episode x";
        String catchupChannelId = "catchuptv_xxx";
        String externalAssetId = "ASHVSEV0108W0108644";
        String entertainmentType = "episode";
        String entertainmentOfferName = "VOD";
        String macaddress = "";
        String supportType = "IPTV_EZ";
        String internal_content_id = "";
        String origin = "";
        String optin = "1/1/0/0";
        String device = "stb";
        vodMapDriver.withInput(new LongWritable(), new Text(
                String.join(TAB, aid , beginZap , zapDuration , contentId , FIELDS_4_TO_6 , title ,
                        serviceCode, FIELDS_9_TO_12 , brodcastDuration , concepts, typeVod, opusCode,
                        onDemandExternalId, externalEntertainmentId, entertainmentSeasonName, entertainmentSeriesName,
                        catchupChannelId,externalAssetId, entertainmentType, entertainmentOfferName, macaddress, supportType, internal_content_id, origin, optin ))
                );

        StringJoiner sj = new StringJoiner(FieldsUtils.DASH);
        String serviceCodeChannel = sj.add(serviceCode).add(opusCode.substring(0,3)).toString();
        String fullProvider = String.join(FieldsUtils.PIPE, serviceCodeChannel, catchupChannelId);
        vodMapDriver.withOutput(
                new Text(String.join(TAB,  aid , "VOD" , brodcastDuration , fullProvider, contentId, title,
                        externalEntertainmentId, entertainmentSeasonName, entertainmentSeriesName, entertainmentOfferName,"0")),
                new Text(String.join(TAB,  beginZap , zapDuration , concepts, device))
                );

        vodMapDriver.runTest();
    }
    @Test
    public final void testVodMapOptoutReco() throws IOException, InterruptedException {
        String aid = "123456789";
        String beginZap = "**********";
        String zapDuration = "1842";
        String serviceCode = "2424VIDEO";
        String contentId = "ASHVSEV0108W0108644_S_2424VIDEO_1";
        String title = "Title of program";
        String brodcastDuration = "2000";
        String concepts = "catégories/divertissement,sous-genres/jeu";
        String typeVod = "vod";
        String opusCode = "ASHVSEV0108W0108644";
        String onDemandExternalId = "CMS_WONT0000527373567000";
        String externalEntertainmentId = "episode-Title of serie and episode-season-X-episode-Y-2000";
        String entertainmentSeasonName = "season x";
        String entertainmentSeriesName = "episode x";
        String catchupChannelId = "catchuptv_xxx";
        String externalAssetId = "ASHVSEV0108W0108644";
        String entertainmentType = "episode";
        String entertainmentOfferName = "VOD";
        String macaddress = "";
        String supportType = "IPTV_EZ";
        String internal_content_id = "";
        String origin = "";
        String optin = "0/1/0/0";

        vodMapDriver.withInput(new LongWritable(), new Text(
                String.join(TAB, aid, beginZap, zapDuration, contentId, FIELDS_4_TO_6, title,
                        serviceCode, FIELDS_9_TO_12, brodcastDuration, concepts, typeVod, opusCode,
                        onDemandExternalId, externalEntertainmentId, entertainmentSeasonName, entertainmentSeriesName,
                        catchupChannelId, externalAssetId, entertainmentType, entertainmentOfferName, macaddress, supportType, internal_content_id, origin, optin))
                );

        vodMapDriver.withCounter(Counters.VOD_OPTOUT_RECO, 1);

        vodMapDriver.runTest();
    }

}
