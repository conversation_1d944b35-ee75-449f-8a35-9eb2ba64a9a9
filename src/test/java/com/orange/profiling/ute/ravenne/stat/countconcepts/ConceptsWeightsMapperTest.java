package com.orange.profiling.ute.ravenne.stat.countconcepts;

import java.io.IOException;

import org.apache.hadoop.io.Text;
import org.apache.hadoop.mrunit.mapreduce.MapDriver;
import org.apache.log4j.BasicConfigurator;
import org.apache.log4j.Level;
import org.apache.log4j.Logger;
import org.junit.Before;
import org.junit.Test;

import com.orange.profiling.common.utils.FieldsUtils;

public class ConceptsWeightsMapperTest {

    private MapDriver<Object, Text, Text, Text> conceptsWeightsMapperDriver;
    private String CONCEPT1 = "categories/fiction";
    private String CONCEPT2 = "ambiance et ton/humoristique";
    private String CONCEPT3 = "type/série";
    private String WEIGHTED_CONCEPTS = CONCEPT1+"=300,"+CONCEPT2+"=100,"+CONCEPT3+"=200";

    @Before
    public void setUp() throws Exception {
        conceptsWeightsMapperDriver = MapDriver.newMapDriver(new ConceptsWeightsMapper());

        BasicConfigurator.configure();
        Logger.getRootLogger().setLevel(Level.ERROR);

    }

    @Test
    public void testWeekTimebox() throws IOException {
        String inputLine1 = String.join(FieldsUtils.TAB, "123456789", "w",
                WEIGHTED_CONCEPTS, "400", "200");
        conceptsWeightsMapperDriver.withInput(new Text(), new Text(inputLine1));

        String expectedValue = "T\t1";
        String expectedKey = "w|"+CONCEPT1;
        conceptsWeightsMapperDriver.withOutput(new Text(expectedKey), new Text(expectedValue));
        expectedKey = "w|"+CONCEPT2;
        conceptsWeightsMapperDriver.withOutput(new Text(expectedKey), new Text(expectedValue));
        expectedKey = "w|"+CONCEPT3;
        conceptsWeightsMapperDriver.withOutput(new Text(expectedKey), new Text(expectedValue));

        conceptsWeightsMapperDriver.runTest(false);
    }

    @Test
    public void testDayTimebox() throws IOException {
        String inputLine1 = String.join(FieldsUtils.TAB, "123456789", "d3",
                WEIGHTED_CONCEPTS, "400", "200");
        conceptsWeightsMapperDriver.withInput(new Text(), new Text(inputLine1));

        String expectedValue = "T\t1";
        String expectedKey = "d3|"+CONCEPT1;
        conceptsWeightsMapperDriver.withOutput(new Text(expectedKey), new Text(expectedValue));
        expectedKey = "d3|"+CONCEPT2;
        conceptsWeightsMapperDriver.withOutput(new Text(expectedKey), new Text(expectedValue));
        expectedKey = "d3|"+CONCEPT3;
        conceptsWeightsMapperDriver.withOutput(new Text(expectedKey), new Text(expectedValue));
        expectedKey = "d|"+CONCEPT1;
        conceptsWeightsMapperDriver.withOutput(new Text(expectedKey), new Text(expectedValue));
        expectedKey = "d|"+CONCEPT2;
        conceptsWeightsMapperDriver.withOutput(new Text(expectedKey), new Text(expectedValue));
        expectedKey = "d|"+CONCEPT3;
        conceptsWeightsMapperDriver.withOutput(new Text(expectedKey), new Text(expectedValue));

        conceptsWeightsMapperDriver.runTest(false);
    }

    @Test
    public void testPeriodTimebox() throws IOException {
        String inputLine1 = String.join(FieldsUtils.TAB, "123456789", "d3p2",
                WEIGHTED_CONCEPTS, "400", "200");
        conceptsWeightsMapperDriver.withInput(new Text(), new Text(inputLine1));

        String expectedValue = "T\t1";
        String expectedKey = "d3p2|"+CONCEPT1;
        conceptsWeightsMapperDriver.withOutput(new Text(expectedKey), new Text(expectedValue));
        expectedKey = "d3p2|"+CONCEPT2;
        conceptsWeightsMapperDriver.withOutput(new Text(expectedKey), new Text(expectedValue));
        expectedKey = "d3p2|"+CONCEPT3;
        conceptsWeightsMapperDriver.withOutput(new Text(expectedKey), new Text(expectedValue));
        expectedKey = "dp|"+CONCEPT1;
        conceptsWeightsMapperDriver.withOutput(new Text(expectedKey), new Text(expectedValue));
        expectedKey = "dp|"+CONCEPT2;
        conceptsWeightsMapperDriver.withOutput(new Text(expectedKey), new Text(expectedValue));
        expectedKey = "dp|"+CONCEPT3;
        conceptsWeightsMapperDriver.withOutput(new Text(expectedKey), new Text(expectedValue));

        conceptsWeightsMapperDriver.runTest(false);
    }

    @Test
    public void testTimeslotTimebox() throws IOException {
        String inputLine1 = String.join(FieldsUtils.TAB, "123456789", "d4t6",
                WEIGHTED_CONCEPTS, "400", "200");
        conceptsWeightsMapperDriver.withInput(new Text(), new Text(inputLine1));

        String expectedValue = "T\t1";
        String expectedKey = "d4t6|"+CONCEPT1;
        conceptsWeightsMapperDriver.withOutput(new Text(expectedKey), new Text(expectedValue));
        expectedKey = "d4t6|"+CONCEPT2;
        conceptsWeightsMapperDriver.withOutput(new Text(expectedKey), new Text(expectedValue));
        expectedKey = "d4t6|"+CONCEPT3;
        conceptsWeightsMapperDriver.withOutput(new Text(expectedKey), new Text(expectedValue));
        expectedKey = "dt|"+CONCEPT1;
        conceptsWeightsMapperDriver.withOutput(new Text(expectedKey), new Text(expectedValue));
        expectedKey = "dt|"+CONCEPT2;
        conceptsWeightsMapperDriver.withOutput(new Text(expectedKey), new Text(expectedValue));
        expectedKey = "dt|"+CONCEPT3;
        conceptsWeightsMapperDriver.withOutput(new Text(expectedKey), new Text(expectedValue));

        conceptsWeightsMapperDriver.runTest(false);
    }


    @Test
    public void testBadTimebox() throws IOException {
        String inputLine1 = String.join(FieldsUtils.TAB, "123456789", "w4z3",
                WEIGHTED_CONCEPTS, "400", "200");
        conceptsWeightsMapperDriver.withInput(new Text(), new Text(inputLine1));

        conceptsWeightsMapperDriver.runTest(false);
    }

    @Test
    public void testEmptyTimebox() throws IOException {
        String inputLine1 = String.join(FieldsUtils.TAB, "123456789", "",
                WEIGHTED_CONCEPTS, "400", "200");
        conceptsWeightsMapperDriver.withInput(new Text(), new Text(inputLine1));

        conceptsWeightsMapperDriver.runTest();
    }

    @Test
    public void testBadLengthTimebox() throws IOException {
        String inputLine1 = String.join(FieldsUtils.TAB, "123456789", "d12",
                WEIGHTED_CONCEPTS, "400", "200");
        conceptsWeightsMapperDriver.withInput(new Text(), new Text(inputLine1));

        conceptsWeightsMapperDriver.runTest();
    }

    @Test
    public void testBadTimeslotTimebox() throws IOException {
        String inputLine1 = String.join(FieldsUtils.TAB, "123456789", "d2r1",
                WEIGHTED_CONCEPTS, "400", "200");
        conceptsWeightsMapperDriver.withInput(new Text(), new Text(inputLine1));

        conceptsWeightsMapperDriver.runTest();
    }


    @Test
    public void testBadFormat() throws IOException {
        String inputLine1 = String.join(FieldsUtils.TAB, "123456789", "d2r1",
                WEIGHTED_CONCEPTS);
        conceptsWeightsMapperDriver.withInput(new Text(), new Text(inputLine1));

        conceptsWeightsMapperDriver.runTest();
    }

}
