package com.orange.profiling.ute.ravenne.genetic.pnssend;

import java.util.HashMap;
import java.util.Map;

import org.apache.hadoop.mapred.Counters.Counter;

public class CounterMock extends Counter {
    private static Map<String, CounterMock> counters = new HashMap<>();
    private String name;
    private long count = 0L;

    public static CounterMock getCounterMock(String name) {
        if (!counters.containsKey(name)) {
            counters.put(name, new CounterMock(name));
        }
        return counters.get(name);
    }

    public CounterMock(String name) {
        this.name = name;
        this.count = 0L;
    }

    @Override
    public void increment(long incr) {
        count += incr;
    }

    public static void reinit() {
        counters.clear();
    }

    public long getCount() {
        return count;
    }
}