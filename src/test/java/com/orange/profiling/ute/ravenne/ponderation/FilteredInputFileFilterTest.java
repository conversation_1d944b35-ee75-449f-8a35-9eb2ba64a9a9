package com.orange.profiling.ute.ravenne.ponderation;

import static org.junit.Assert.*;

import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.Path;
import org.apache.log4j.BasicConfigurator;
import org.apache.log4j.Level;
import org.apache.log4j.Logger;
import org.junit.Before;
import org.junit.Test;

public class FilteredInputFileFilterTest {

    private FilteredInputFileFilter filter;

    @Before
    public void setUp() {

        filter = new FilteredInputFileFilter();
        Configuration config = new Configuration();
        config.set("minDate", "2019/12");
        config.set("maxDate", "2019/16");
        config.set("outputDir","/user/profiling-ute/private/generated");
        filter.setConf(config);

        BasicConfigurator.configure();
        Logger.getRootLogger().setLevel(Level.ERROR);
    }

    @Test
    public void testProgByMacBetwenMinAndMax() {
        String filePath = "/user/profiling-ute/private/generated/ute/ravenne/filtered/2019/14/selected/part-m-00001";
        boolean processFile = filter.accept(new Path(filePath));
        assertTrue(processFile);
    }

    @Test
    public void testProgByMacEqualMin() {
        String filePath = "/user/profiling-ute/private/generated/ute/ravenne/filtered/2019/12/selected/part-m-00001";
        boolean processFile = filter.accept(new Path(filePath));
        assertTrue(processFile);
    }

    @Test
    public void testProgByMaBeforeMin() {
        String filePath = "/user/profiling-ute/private/generated/ute/ravenne/filtered/2019/11/selected/part-m-00001";
        boolean processFile = filter.accept(new Path(filePath));
        assertFalse(processFile);
    }

    @Test
    public void testProgByMacEqualMax() {
        String filePath = "/user/profiling-ute/private/generated/ute/ravenne/filtered/2019/16/selected/part-m-00001";
        boolean processFile = filter.accept(new Path(filePath));
        assertTrue(processFile);
    }

    @Test
    public void testProgByMacAfterMax() {
        String filePath = "/user/profiling-ute/private/generated/ute/ravenne/filtered/2019/17/selected/part-m-00001";
        boolean processFile = filter.accept(new Path(filePath));
        assertFalse(processFile);
    }

    @Test
    public void testNotFilteredFileType() {
        String filePath = "/user/profiling-ute/private/generated/ute/ravenne/genetic/2019/14/selected/part-m-00001";
        boolean processFile = filter.accept(new Path(filePath));
        assertTrue(processFile);
    }

    @Test
    public void testNotValidDate() {
        String filePath = "/user/profiling-ute/private/generated/ute/ravenne/filtered/2019/A/selected/part-m-00001";
        boolean processFile = filter.accept(new Path(filePath));
        assertFalse(processFile);
    }

}
