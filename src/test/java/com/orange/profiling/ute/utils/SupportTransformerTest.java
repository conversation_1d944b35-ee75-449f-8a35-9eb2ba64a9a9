package com.orange.profiling.ute.utils;

import static org.junit.Assert.assertEquals;
import org.junit.Test;

public class SupportTransformerTest {

    @Test
    public void testGetDeviceWithOTTMobile() {
        String result = SupportTransformer.getDevice("OTT_mobile", "ott123456789");
        assertEquals(SupportTransformer.DEVICE_MOBILE, result);
    }

    @Test
    public void testGetDeviceWithOtvCast() {
        String result = SupportTransformer.getDevice("otvcast", "ott123456789");
        assertEquals(SupportTransformer.DEVICE_MOBILE, result);
    }

    @Test
    public void testGetDeviceWithOTTSmartTV() {
        String result = SupportTransformer.getDevice("OTT_smarttv", "ott123456789");
        assertEquals(SupportTransformer.DEVICE_SMARTTV, result);
    }

    @Test
    public void testGetDeviceWithOTTAppleTV() {
        String result = SupportTransformer.getDevice("OTT_appletv", "ott123456789");
        assertEquals(SupportTransformer.DEVICE_SMARTTV, result);
    }

    @Test
    public void testGetDeviceWithOTTAndroidTV() {
        String result = SupportTransformer.getDevice("OTT_androidtv", "ott123456789");
        assertEquals(SupportTransformer.DEVICE_SMARTTV, result);
    }

    @Test
    public void testGetDeviceWithOTTPC() {
        String result = SupportTransformer.getDevice("OTT_pc", "ott123456789");
        assertEquals(SupportTransformer.DEVICE_WEB, result);
    }

    @Test
    public void testGetDeviceWithMLV() {
        String result = SupportTransformer.getDevice("mlv", "ott123456789");
        assertEquals(SupportTransformer.DEVICE_WEB, result);
    }

    @Test
    public void testGetDeviceWithStbIPTV() {
        String result = SupportTransformer.getDevice("IPTV_EZ", "123456789");
        assertEquals(SupportTransformer.DEVICE_STB, result);
    }

    @Test
    public void testGetDeviceWithStbWHD() {
        String result = SupportTransformer.getDevice("WHD", "123456789");
        assertEquals(SupportTransformer.DEVICE_STB, result);
    }


    @Test
    public void testGetDeviceWithNullSupportStb() {
        String result = SupportTransformer.getDevice("", "123456789");
        assertEquals(SupportTransformer.DEVICE_STB, result);
    }

    @Test
    public void testGetDeviceWithNullSupportAndAidOtt() {
        String result = SupportTransformer.getDevice("", "ott123456789");
        assertEquals("", result);
    }

}