#!/bin/bash

# Compiler le projet
mvn clean compile test-compile -DskipTests=true

# Test local avec données de test
export HADOOP_CLASSPATH="target/classes:target/test-classes:$(find target/lib -name '*.jar' | tr '\n' ':')"

hadoop jar target/workflowUteRavenne-*.jar \
com.orange.profiling.ute.ravenne.genetic.concat.MainConcat \
"src/test/resources/ute-ravenne-concat/in/2020/12/conceptsweights" \
"src/test/resources/ute-ravenne-concat/in/2020/12/bestchannelsproviders/LIVE/part*" \
"src/test/resources/ute-ravenne-concat/in/2020/12/bestchannelsproviders/VOD/part*" \
"20200316" \
"target/test-output/genetic" \
"target/test-output/genetic_ott" \
"20" "5" "5"

echo "=== Vérification des sorties ==="
echo "STB profiles:"
find target/test-output/genetic -name "part*" -type f | wc -l

echo "OTT profiles:"
find target/test-output/genetic_ott -name "part*" -type f | wc -l

echo "=== Exemple de timeslots OTT ==="
find target/test-output/genetic_ott -name "part*" -exec head -5 {} \; | grep -o 'd[0-9]t[0-9][^;]*'