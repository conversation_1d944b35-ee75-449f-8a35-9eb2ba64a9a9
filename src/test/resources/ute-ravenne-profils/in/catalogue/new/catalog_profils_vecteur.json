{"1": [{"ambiance et ton/suspense": "7.0"}], "2": [{"sujets/media et journalisme": "8.0", "sujets/presse": "2.0"}, {"sujets/presse": "6.7"}, {"sujets/media et journalisme": "5.4", "sujets/presse": "5.4"}, {"sujets/media et journalisme": "6.7"}], "3": [{"sujets/actualité": "7.8", "sujets/société": "6.6"}, {"sujets/actualité": "9.6"}, {"sujets/société": "8.1"}], "5": [{"genres/animation": "10.0"}], "6": [{"ambiance et ton/humoristique": "9.9"}, {"ambiance et ton/humoristique": "9.6", "personnages/personnages jeunesse": "3.7"}], "8": [{"genres/action": "7.8", "sous-genres/fantastique": "7.8"}, {"sous-genres/fantastique": "8.6", "sous-genres/science-fiction": "8.4"}, {"genres/action": "7.6", "sous-genres/fantastique": "7.1", "sous-genres/science-fiction": "7.1"}, {"genres/action": "8.9", "sous-genres/science-fiction": "8.6"}], "9": [{"ambiance et ton/humoristique": "9.9"}, {"ambiance et ton/humoristique": "7.7", "genres/comédie": "6.7"}, {"genres/comédie": "9.0"}], "10": [{"ambiance et ton/suspense": "8.9"}, {"ambiance et ton/angoissant": "6.4", "ambiance et ton/suspense": "8.2", "genres/thriller": "7.7"}, {"ambiance et ton/angoissant": "5.7", "ambiance et ton/suspense": "8.1"}, {"ambiance et ton/suspense": "8.7", "genres/thriller": "7.9"}], "11": [{"thèmes/aux 4 coins du monde": "8.4"}, {"sujets/voyage et évasion": "7.7", "thèmes/aux 4 coins du monde": "7.2"}, {"sujets/voyage et évasion": "9.5"}], "12": [{"thèmes/nature et vie sauvage": "7.7"}], "13": [{"ambiance et ton/humoristique": "8.6", "genres/comédie": "7.6", "thèmes/amitié": "6.8"}, {"genres/comédie": "5.9", "thèmes/amitié": "9.6"}, {"ambiance et ton/humoristique": "7.8", "thèmes/amitié": "5.9"}, {"ambiance et ton/humoristique": "9.1", "genres/comédie": "8.1"}], "14": [{"sous-genres/jeu": "6.8", "sous-genres/télé-réalité": "6.8", "thèmes/énigmes et jeu de piste": "3.3"}, {"thèmes/énigmes et jeu de piste": "7.5"}, {"sous-genres/jeu": "6.1", "sous-genres/télé-réalité": "6.1", "thèmes/énigmes et jeu de piste": "6.1"}, {"sous-genres/télé-réalité": "6.9", "thèmes/énigmes et jeu de piste": "8.4"}, {"sous-genres/jeu": "10.0", "sous-genres/télé-réalité": "5.0", "thèmes/énigmes et jeu de piste": "10.0"}, {"sous-genres/jeu": "5.0", "sous-genres/télé-réalité": "10.0", "thèmes/énigmes et jeu de piste": "5.0"}, {"sous-genres/jeu": "6.8"}, {"sous-genres/jeu": "6.8", "thèmes/énigmes et jeu de piste": "7.6"}, {"sous-genres/jeu": "10.0", "sous-genres/télé-réalité": "5.0", "thèmes/énigmes et jeu de piste": "5.0"}, {"sous-genres/jeu": "7.6", "sous-genres/télé-réalité": "7.6"}, {"sous-genres/jeu": "4.6", "sous-genres/télé-réalité": "4.6", "thèmes/énigmes et jeu de piste": "9.7"}], "15": [{"ambiance et ton/humoristique": "6.0", "ambiance et ton/suspense": "3.0", "genres/action": "3.0", "genres/comédie": "3.0", "genres/thriller": "10.0"}, {"ambiance et ton/humoristique": "4.5", "ambiance et ton/suspense": "4.5", "genres/comédie": "4.5", "genres/thriller": "4.4"}, {"ambiance et ton/suspense": "2.0", "genres/action": "2.0", "genres/comédie": "5.0", "genres/thriller": "5.0"}, {"ambiance et ton/humoristique": "5.0", "ambiance et ton/suspense": "2.0", "genres/comédie": "2.0", "genres/thriller": "7.0"}, {"ambiance et ton/humoristique": "4.7", "ambiance et ton/suspense": "4.0", "genres/action": "3.9", "genres/comédie": "4.2", "genres/thriller": "3.5"}, {"genres/action": "5.0", "genres/thriller": "10.0"}, {"ambiance et ton/suspense": "4.8", "genres/comédie": "4.9", "genres/thriller": "4.6"}, {"genres/action": "3.0", "genres/comédie": "6.0", "genres/thriller": "6.0"}, {"ambiance et ton/suspense": "5.9", "genres/action": "5.2", "genres/comédie": "4.8"}, {"ambiance et ton/humoristique": "7.0", "ambiance et ton/suspense": "6.1", "genres/action": "6.8"}, {"ambiance et ton/suspense": "3.0", "genres/action": "3.0", "genres/comédie": "3.0", "genres/thriller": "6.0"}, {"ambiance et ton/humoristique": "4.2", "genres/action": "5.1", "genres/comédie": "5.1", "genres/thriller": "8.8"}, {"ambiance et ton/suspense": "3.0", "genres/action": "6.0", "genres/comédie": "6.0", "genres/thriller": "3.0"}, {"ambiance et ton/suspense": "5.2", "genres/action": "4.8", "genres/thriller": "5.1"}, {"ambiance et ton/suspense": "6.2", "genres/comédie": "6.8"}, {"ambiance et ton/suspense": "2.0", "genres/comédie": "2.0", "genres/thriller": "10.0"}, {"genres/action": "5.0", "genres/comédie": "10.0"}, {"ambiance et ton/humoristique": "8.3", "genres/action": "5.4", "genres/comédie": "6.0"}, {"ambiance et ton/humoristique": "6.4", "ambiance et ton/suspense": "5.0", "genres/thriller": "4.9"}, {"ambiance et ton/humoristique": "3.0", "genres/action": "3.0", "genres/thriller": "10.0"}, {"ambiance et ton/humoristique": "3.0", "ambiance et ton/suspense": "3.0", "genres/comédie": "3.0", "genres/thriller": "10.0"}, {"ambiance et ton/humoristique": "3.0", "ambiance et ton/suspense": "3.0", "genres/action": "3.0", "genres/thriller": "10.0"}, {"ambiance et ton/humoristique": "5.3", "genres/comédie": "5.1", "genres/thriller": "5.9"}, {"ambiance et ton/humoristique": "6.9", "genres/action": "5.0", "genres/comédie": "5.8", "genres/thriller": "4.7"}, {"ambiance et ton/suspense": "5.3", "genres/action": "2.3", "genres/comédie": "5.3", "genres/thriller": "5.3"}, {"ambiance et ton/humoristique": "3.0", "genres/action": "6.0", "genres/comédie": "3.0", "genres/thriller": "6.0"}, {"ambiance et ton/humoristique": "8.2", "genres/action": "6.2", "genres/thriller": "6.0"}, {"ambiance et ton/humoristique": "5.4", "ambiance et ton/suspense": "4.8", "genres/action": "4.7", "genres/thriller": "4.5"}, {"ambiance et ton/humoristique": "5.1", "ambiance et ton/suspense": "5.1", "genres/comédie": "5.1"}, {"ambiance et ton/humoristique": "2.0", "ambiance et ton/suspense": "2.0", "genres/action": "2.0", "genres/comédie": "7.0", "genres/thriller": "2.0"}, {"ambiance et ton/humoristique": "4.0", "genres/comédie": "8.0", "genres/thriller": "4.0"}, {"genres/comédie": "8.1", "genres/thriller": "8.3"}, {"ambiance et ton/humoristique": "2.7", "ambiance et ton/suspense": "5.7", "genres/comédie": "2.7", "genres/thriller": "5.6"}, {"ambiance et ton/suspense": "5.1", "genres/thriller": "9.6"}, {"ambiance et ton/suspense": "3.0", "genres/action": "3.0", "genres/thriller": "10.0"}, {"ambiance et ton/suspense": "5.1", "genres/action": "3.9", "genres/comédie": "3.9", "genres/thriller": "3.8"}, {"ambiance et ton/humoristique": "6.0", "genres/action": "3.0", "genres/comédie": "3.0", "genres/thriller": "10.0"}, {"ambiance et ton/humoristique": "3.0", "genres/comédie": "6.7", "genres/thriller": "6.5"}, {"ambiance et ton/humoristique": "9.2", "genres/thriller": "8.7"}, {"genres/action": "5.0", "genres/comédie": "5.0", "genres/thriller": "5.5"}, {"ambiance et ton/humoristique": "3.0", "genres/action": "3.0", "genres/comédie": "3.0", "genres/thriller": "10.0"}, {"ambiance et ton/suspense": "3.0", "genres/action": "5.4", "genres/comédie": "3.0", "genres/thriller": "5.4"}, {"genres/action": "3.9", "genres/comédie": "9.6", "genres/thriller": "3.9"}, {"ambiance et ton/humoristique": "8.5", "ambiance et ton/suspense": "2.0", "genres/action": "5.0", "genres/comédie": "2.0", "genres/thriller": "2.0"}, {"ambiance et ton/humoristique": "5.8", "ambiance et ton/suspense": "5.6", "genres/action": "5.4", "genres/comédie": "5.3"}, {"ambiance et ton/suspense": "3.3", "genres/action": "3.3", "genres/comédie": "6.5", "genres/thriller": "3.3"}, {"ambiance et ton/humoristique": "5.0", "genres/action": "5.0", "genres/comédie": "10.0", "genres/thriller": "10.0"}, {"ambiance et ton/suspense": "5.0", "genres/comédie": "5.0", "genres/thriller": "10.0"}], "16": [{"genres/documentaire": "5.6", "sujets/voyage et évasion": "6.0", "thèmes/découverte": "7.5", "thèmes/nature et vie sauvage": "7.7"}, {"sujets/sciences et techniques": "5.8", "sujets/voyage et évasion": "5.7", "thèmes/découverte": "8.2"}, {"genres/documentaire": "5.0", "sujets/sciences et techniques": "10.0", "sujets/voyage et évasion": "5.0", "thèmes/découverte": "10.0", "thèmes/nature et vie sauvage": "10.0"}, {"thèmes/découverte": "8.0"}, {"genres/documentaire": "6.0", "sujets/voyage et évasion": "10.0", "thèmes/découverte": "3.0", "thèmes/nature et vie sauvage": "3.0"}, {"genres/documentaire": "4.0", "sujets/sciences et techniques": "8.0", "sujets/voyage et évasion": "4.0", "thèmes/découverte": "8.0"}, {"genres/documentaire": "9.2", "thèmes/découverte": "4.6", "thèmes/nature et vie sauvage": "4.6"}, {"genres/documentaire": "5.3", "sujets/sciences et techniques": "10.0", "sujets/voyage et évasion": "10.0", "thèmes/nature et vie sauvage": "5.3"}, {"genres/documentaire": "3.0", "sujets/sciences et techniques": "3.0", "sujets/voyage et évasion": "10.0", "thèmes/découverte": "6.0", "thèmes/nature et vie sauvage": "6.0"}, {"genres/documentaire": "3.0", "sujets/voyage et évasion": "10.0", "thèmes/découverte": "3.0", "thèmes/nature et vie sauvage": "3.0"}, {"genres/documentaire": "2.0", "sujets/sciences et techniques": "2.0", "sujets/voyage et évasion": "10.0", "thèmes/découverte": "2.0", "thèmes/nature et vie sauvage": "5.0"}, {"genres/documentaire": "2.7", "sujets/sciences et techniques": "2.7", "thèmes/découverte": "9.8"}, {"sujets/voyage et évasion": "6.7", "thèmes/découverte": "7.7", "thèmes/nature et vie sauvage": "7.2"}, {"genres/documentaire": "10.0", "sujets/sciences et techniques": "5.0", "sujets/voyage et évasion": "10.0", "thèmes/découverte": "10.0"}, {"genres/documentaire": "5.3", "sujets/voyage et évasion": "9.7", "thèmes/découverte": "4.8", "thèmes/nature et vie sauvage": "7.2"}, {"genres/documentaire": "10.0", "sujets/sciences et techniques": "4.0", "sujets/voyage et évasion": "10.0", "thèmes/découverte": "4.0", "thèmes/nature et vie sauvage": "5.5"}, {"genres/documentaire": "5.0", "sujets/sciences et techniques": "5.0", "sujets/voyage et évasion": "10.0", "thèmes/nature et vie sauvage": "5.0"}, {"genres/documentaire": "8.1", "sujets/sciences et techniques": "7.6", "sujets/voyage et évasion": "8.1", "thèmes/nature et vie sauvage": "8.1"}, {"genres/documentaire": "3.0", "sujets/sciences et techniques": "10.0", "thèmes/découverte": "6.0", "thèmes/nature et vie sauvage": "3.0"}, {"genres/documentaire": "7.6", "sujets/voyage et évasion": "7.6", "thèmes/découverte": "7.6"}, {"genres/documentaire": "4.1", "sujets/sciences et techniques": "4.2", "thèmes/découverte": "9.1", "thèmes/nature et vie sauvage": "6.4"}, {"genres/documentaire": "4.6", "sujets/sciences et techniques": "4.5", "sujets/voyage et évasion": "4.5", "thèmes/découverte": "9.6", "thèmes/nature et vie sauvage": "4.5"}, {"genres/documentaire": "5.0", "sujets/sciences et techniques": "10.0", "sujets/voyage et évasion": "10.0"}, {"genres/documentaire": "5.0", "sujets/sciences et techniques": "10.0", "sujets/voyage et évasion": "10.0", "thèmes/découverte": "10.0", "thèmes/nature et vie sauvage": "10.0"}, {"genres/documentaire": "4.9", "sujets/sciences et techniques": "9.8", "thèmes/découverte": "9.8"}, {"genres/documentaire": "10.0", "sujets/sciences et techniques": "5.0", "thèmes/découverte": "5.0", "thèmes/nature et vie sauvage": "10.0"}, {"sujets/sciences et techniques": "7.6", "thèmes/découverte": "8.0"}, {"genres/documentaire": "9.5", "sujets/sciences et techniques": "4.8", "thèmes/découverte": "9.5"}, {"genres/documentaire": "10.0", "sujets/voyage et évasion": "5.0", "thèmes/découverte": "5.0", "thèmes/nature et vie sauvage": "5.0"}, {"sujets/sciences et techniques": "8.3", "sujets/voyage et évasion": "8.7", "thèmes/nature et vie sauvage": "7.9"}, {"genres/documentaire": "10.0", "sujets/sciences et techniques": "10.0", "sujets/voyage et évasion": "5.0", "thèmes/nature et vie sauvage": "10.0"}, {"genres/documentaire": "5.0", "sujets/sciences et techniques": "10.0", "sujets/voyage et évasion": "5.0", "thèmes/découverte": "5.0"}, {"genres/documentaire": "10.0", "sujets/sciences et techniques": "10.0", "sujets/voyage et évasion": "5.0", "thèmes/découverte": "10.0"}, {"genres/documentaire": "5.0", "sujets/sciences et techniques": "10.0", "sujets/voyage et évasion": "10.0", "thèmes/nature et vie sauvage": "10.0"}, {"genres/documentaire": "10.0", "sujets/sciences et techniques": "5.0", "sujets/voyage et évasion": "5.0", "thèmes/découverte": "5.0"}, {"genres/documentaire": "4.8", "sujets/sciences et techniques": "9.5", "thèmes/découverte": "4.8", "thèmes/nature et vie sauvage": "4.7"}, {"genres/documentaire": "4.2", "sujets/voyage et évasion": "5.7", "thèmes/découverte": "9.3"}, {"genres/documentaire": "10.0", "sujets/voyage et évasion": "10.0", "thèmes/découverte": "5.0"}, {"genres/documentaire": "10.0", "sujets/voyage et évasion": "5.0", "thèmes/découverte": "5.0"}, {"genres/documentaire": "7.9", "sujets/voyage et évasion": "7.9", "thèmes/découverte": "7.9", "thèmes/nature et vie sauvage": "3.9"}, {"genres/documentaire": "10.0", "sujets/sciences et techniques": "5.3", "thèmes/découverte": "4.3", "thèmes/nature et vie sauvage": "5.3"}, {"genres/documentaire": "10.0", "sujets/sciences et techniques": "5.0", "thèmes/découverte": "5.0"}, {"genres/documentaire": "9.2", "sujets/voyage et évasion": "4.5", "thèmes/découverte": "9.2"}, {"genres/documentaire": "2.0", "sujets/sciences et techniques": "10.0", "sujets/voyage et évasion": "2.0", "thèmes/découverte": "7.0", "thèmes/nature et vie sauvage": "10.0"}, {"genres/documentaire": "10.0", "sujets/sciences et techniques": "5.0", "thèmes/découverte": "10.0", "thèmes/nature et vie sauvage": "10.0"}, {"genres/documentaire": "4.9", "sujets/sciences et techniques": "10.0", "thèmes/découverte": "5.0"}, {"genres/documentaire": "9.2", "sujets/voyage et évasion": "4.6", "thèmes/découverte": "4.6", "thèmes/nature et vie sauvage": "9.2"}, {"genres/documentaire": "3.0", "sujets/sciences et techniques": "10.0", "thèmes/découverte": "3.0", "thèmes/nature et vie sauvage": "6.0"}, {"genres/documentaire": "5.0", "sujets/sciences et techniques": "10.0", "sujets/voyage et évasion": "10.0", "thèmes/découverte": "5.0", "thèmes/nature et vie sauvage": "10.0"}, {"genres/documentaire": "6.0", "sujets/sciences et techniques": "3.0", "sujets/voyage et évasion": "3.0", "thèmes/découverte": "10.0"}, {"genres/documentaire": "10.0", "sujets/sciences et techniques": "10.0", "thèmes/découverte": "5.0"}, {"genres/documentaire": "7.4", "sujets/sciences et techniques": "7.4", "thèmes/découverte": "7.5"}, {"thèmes/découverte": "6.9", "thèmes/nature et vie sauvage": "6.6"}, {"genres/documentaire": "5.0", "sujets/sciences et techniques": "10.0", "sujets/voyage et évasion": "5.0", "thèmes/découverte": "10.0", "thèmes/nature et vie sauvage": "5.0"}, {"genres/documentaire": "3.0", "sujets/sciences et techniques": "10.0", "sujets/voyage et évasion": "3.0", "thèmes/découverte": "6.0", "thèmes/nature et vie sauvage": "3.0"}, {"genres/documentaire": "3.0", "sujets/sciences et techniques": "3.0", "sujets/voyage et évasion": "10.0", "thèmes/découverte": "6.0", "thèmes/nature et vie sauvage": "3.0"}, {"sujets/voyage et évasion": "7.6", "thèmes/découverte": "8.4"}, {"sujets/sciences et techniques": "6.4", "thèmes/découverte": "7.2", "thèmes/nature et vie sauvage": "6.7"}, {"genres/documentaire": "3.0", "sujets/sciences et techniques": "6.0", "sujets/voyage et évasion": "3.0", "thèmes/découverte": "10.0", "thèmes/nature et vie sauvage": "3.0"}, {"genres/documentaire": "10.0", "sujets/sciences et techniques": "5.0", "sujets/voyage et évasion": "10.0", "thèmes/découverte": "10.0", "thèmes/nature et vie sauvage": "10.0"}, {"sujets/sciences et techniques": "5.5", "sujets/voyage et évasion": "5.9", "thèmes/découverte": "7.6", "thèmes/nature et vie sauvage": "7.2"}, {"genres/documentaire": "9.4", "sujets/sciences et techniques": "9.4", "sujets/voyage et évasion": "9.4"}, {"genres/documentaire": "4.5", "sujets/sciences et techniques": "9.0", "sujets/voyage et évasion": "9.0", "thèmes/découverte": "9.0"}, {"genres/documentaire": "4.0", "sujets/sciences et techniques": "4.1", "sujets/voyage et évasion": "4.3", "thèmes/découverte": "7.1", "thèmes/nature et vie sauvage": "9.3"}, {"genres/documentaire": "10.0", "sujets/sciences et techniques": "5.0", "sujets/voyage et évasion": "5.0", "thèmes/nature et vie sauvage": "5.0"}, {"genres/documentaire": "7.1", "sujets/sciences et techniques": "7.1", "thèmes/découverte": "7.1", "thèmes/nature et vie sauvage": "7.1"}, {"genres/documentaire": "7.1", "sujets/sciences et techniques": "7.1", "sujets/voyage et évasion": "7.1", "thèmes/découverte": "7.1"}, {"genres/documentaire": "7.4", "thèmes/découverte": "8.5"}, {"genres/documentaire": "4.8", "sujets/sciences et techniques": "9.6", "sujets/voyage et évasion": "4.8", "thèmes/découverte": "4.8", "thèmes/nature et vie sauvage": "9.6"}, {"genres/documentaire": "5.0", "sujets/sciences et techniques": "10.0", "sujets/voyage et évasion": "10.0", "thèmes/découverte": "5.0", "thèmes/nature et vie sauvage": "5.0"}, {"genres/documentaire": "10.0", "sujets/sciences et techniques": "5.0", "sujets/voyage et évasion": "10.0", "thèmes/découverte": "5.0", "thèmes/nature et vie sauvage": "10.0"}, {"genres/documentaire": "5.0", "sujets/sciences et techniques": "10.0", "thèmes/découverte": "10.0", "thèmes/nature et vie sauvage": "10.0"}, {"genres/documentaire": "4.8", "sujets/sciences et techniques": "4.9", "thèmes/découverte": "4.9", "thèmes/nature et vie sauvage": "9.7"}, {"genres/documentaire": "10.0", "sujets/voyage et évasion": "5.0", "thèmes/découverte": "10.0", "thèmes/nature et vie sauvage": "5.0"}, {"genres/documentaire": "5.0", "sujets/sciences et techniques": "5.0", "sujets/voyage et évasion": "10.0", "thèmes/découverte": "10.0", "thèmes/nature et vie sauvage": "10.0"}, {"genres/documentaire": "10.0", "sujets/sciences et techniques": "5.0", "sujets/voyage et évasion": "5.0", "thèmes/découverte": "5.0", "thèmes/nature et vie sauvage": "10.0"}, {"genres/documentaire": "6.9", "sujets/sciences et techniques": "6.9", "sujets/voyage et évasion": "6.9", "thèmes/découverte": "6.9", "thèmes/nature et vie sauvage": "6.9"}, {"genres/documentaire": "4.6", "sujets/sciences et techniques": "4.9", "thèmes/découverte": "9.6"}, {"genres/documentaire": "10.0", "sujets/sciences et techniques": "10.0", "thèmes/découverte": "10.0", "thèmes/nature et vie sauvage": "5.0"}, {"genres/documentaire": "10.0", "sujets/sciences et techniques": "5.0", "sujets/voyage et évasion": "5.0", "thèmes/découverte": "5.0", "thèmes/nature et vie sauvage": "5.0"}, {"genres/documentaire": "4.8", "sujets/sciences et techniques": "4.8", "sujets/voyage et évasion": "10.0", "thèmes/découverte": "10.0", "thèmes/nature et vie sauvage": "5.1"}, {"genres/documentaire": "3.0", "sujets/sciences et techniques": "3.0", "sujets/voyage et évasion": "10.0", "thèmes/découverte": "6.0"}, {"genres/documentaire": "5.2", "sujets/sciences et techniques": "10.0", "thèmes/découverte": "4.5", "thèmes/nature et vie sauvage": "10.0"}, {"genres/documentaire": "4.7", "sujets/sciences et techniques": "9.4", "sujets/voyage et évasion": "4.7", "thèmes/découverte": "4.7", "thèmes/nature et vie sauvage": "4.7"}, {"genres/documentaire": "4.8", "sujets/sciences et techniques": "4.8", "sujets/voyage et évasion": "9.6", "thèmes/découverte": "9.6"}, {"genres/documentaire": "3.8", "sujets/sciences et techniques": "4.1", "sujets/voyage et évasion": "4.2", "thèmes/découverte": "9.4"}, {"genres/documentaire": "6.4", "thèmes/découverte": "8.1", "thèmes/nature et vie sauvage": "7.3"}, {"genres/documentaire": "8.6", "sujets/sciences et techniques": "8.6", "sujets/voyage et évasion": "4.1", "thèmes/découverte": "8.6", "thèmes/nature et vie sauvage": "8.6"}, {"genres/documentaire": "10.0", "sujets/voyage et évasion": "10.0", "thèmes/nature et vie sauvage": "5.0"}, {"genres/documentaire": "5.0", "sujets/sciences et techniques": "5.0", "sujets/voyage et évasion": "10.0", "thèmes/découverte": "5.0", "thèmes/nature et vie sauvage": "10.0"}, {"genres/documentaire": "5.0", "sujets/sciences et techniques": "5.0", "sujets/voyage et évasion": "10.0", "thèmes/découverte": "5.0", "thèmes/nature et vie sauvage": "5.0"}, {"genres/documentaire": "5.0", "sujets/sciences et techniques": "10.0", "sujets/voyage et évasion": "5.0", "thèmes/nature et vie sauvage": "5.0"}, {"genres/documentaire": "5.0", "sujets/sciences et techniques": "10.0", "thèmes/découverte": "10.0", "thèmes/nature et vie sauvage": "5.0"}, {"genres/documentaire": "10.0", "sujets/sciences et techniques": "10.0", "sujets/voyage et évasion": "10.0", "thèmes/découverte": "5.0", "thèmes/nature et vie sauvage": "10.0"}], "17": [{"type/téléfilm": "6.0"}], "18": [{"sujets/littérature": "10.0"}, {"sujets/art et culture": "7.5"}, {"sujets/art et culture": "5.4", "sujets/littérature": "5.4"}], "19": [{"ambiance et ton/onirique": "7.7", "genres/documentaire": "7.7"}, {"ambiance et ton/onirique": "8.7", "genres/documentaire": "4.3"}, {"ambiance et ton/onirique": "8.2"}, {"ambiance et ton/onirique": "3.9", "genres/documentaire": "8.0"}], "20": [{"thèmes/vengeance et auto-justice": "7.5"}], "21": [{"genres/comédie romantique": "4.7", "thèmes/amour et séduction": "9.4"}, {"thèmes/amour et séduction": "8.2"}], "23": [{"genres/action": "8.3"}, {"genres/action": "8.2", "thèmes/seul contre tous": "5.7"}, {"genres/action": "9.8", "sujets/arts martiaux": "5.4"}, {"genres/action": "9.2", "sujets/arts martiaux": "4.7", "thèmes/seul contre tous": "8.2"}, {"genres/action": "5.0", "sujets/arts martiaux": "5.0", "thèmes/seul contre tous": "10.0"}], "24": [{"genres/historique": "8.0", "sujets/Histoire": "8.0"}, {"genres/historique": "8.0"}, {"genres/historique": "9.4", "sujets/Histoire": "4.7"}, {"genres/historique": "4.9", "sujets/Histoire": "9.8"}, {"sujets/Histoire": "8.3"}], "25": [{"sous-genres/télé-réalité": "8.6"}], "26": [{"sous-genres/science-fiction": "7.9"}], "27": [{"ambiance et ton/triste": "7.5", "ambiance et ton/émouvant": "7.5", "genres/drame": "4.9", "genres/mélodrame": "4.9"}, {"ambiance et ton/triste": "3.0", "ambiance et ton/émouvant": "10.0", "genres/drame": "3.0", "thèmes/mal de vivre": "3.0"}, {"ambiance et ton/triste": "9.5", "ambiance et ton/émouvant": "4.8", "genres/drame": "4.8", "genres/mélodrame": "4.8"}, {"ambiance et ton/triste": "9.9", "genres/drame": "5.0", "genres/mélodrame": "5.0"}, {"ambiance et ton/triste": "5.7", "ambiance et ton/émouvant": "6.7", "genres/drame": "8.5", "thèmes/mal de vivre": "5.7"}, {"ambiance et ton/triste": "5.8", "genres/drame": "2.5", "genres/mélodrame": "2.5", "thèmes/mal de vivre": "5.8"}, {"ambiance et ton/triste": "10.0", "ambiance et ton/émouvant": "5.0", "genres/drame": "2.0", "genres/mélodrame": "2.0"}, {"ambiance et ton/triste": "4.7", "ambiance et ton/émouvant": "9.4", "genres/drame": "4.7", "genres/mélodrame": "4.7"}, {"genres/drame": "8.5"}, {"ambiance et ton/émouvant": "5.2", "genres/mélodrame": "6.6"}, {"ambiance et ton/triste": "4.2", "ambiance et ton/émouvant": "8.4", "genres/mélodrame": "6.2", "thèmes/mal de vivre": "4.2"}, {"ambiance et ton/émouvant": "7.4", "genres/drame": "3.7", "genres/mélodrame": "3.7", "thèmes/mal de vivre": "3.7"}, {"ambiance et ton/triste": "10.0", "ambiance et ton/émouvant": "10.0", "genres/mélodrame": "3.0", "thèmes/mal de vivre": "5.0"}, {"ambiance et ton/émouvant": "5.0", "genres/mélodrame": "7.0", "thèmes/mal de vivre": "10.0"}, {"genres/drame": "4.3", "genres/mélodrame": "4.3", "thèmes/mal de vivre": "7.4"}, {"ambiance et ton/triste": "4.7", "genres/drame": "4.9", "genres/mélodrame": "5.0"}, {"ambiance et ton/triste": "10.0", "ambiance et ton/émouvant": "10.0", "genres/mélodrame": "7.0", "thèmes/mal de vivre": "5.0"}, {"ambiance et ton/triste": "6.0", "ambiance et ton/émouvant": "7.7", "genres/mélodrame": "6.4"}, {"genres/drame": "4.8", "genres/mélodrame": "4.8"}, {"ambiance et ton/triste": "5.0", "ambiance et ton/émouvant": "10.0", "genres/mélodrame": "3.0", "thèmes/mal de vivre": "5.0"}, {"thèmes/mal de vivre": "8.2"}, {"ambiance et ton/triste": "5.0", "ambiance et ton/émouvant": "10.0", "genres/drame": "2.0", "genres/mélodrame": "2.0"}, {"genres/drame": "8.1", "thèmes/mal de vivre": "6.4"}, {"ambiance et ton/émouvant": "10.0", "genres/drame": "2.0", "genres/mélodrame": "2.0", "thèmes/mal de vivre": "5.0"}, {"ambiance et ton/triste": "10.0", "ambiance et ton/émouvant": "5.0", "genres/drame": "5.0", "thèmes/mal de vivre": "10.0"}, {"ambiance et ton/émouvant": "6.0", "genres/drame": "3.0", "genres/mélodrame": "7.0"}, {"ambiance et ton/émouvant": "3.0", "genres/mélodrame": "2.0", "thèmes/mal de vivre": "6.0"}, {"ambiance et ton/triste": "3.9", "genres/drame": "4.4", "genres/mélodrame": "4.4", "thèmes/mal de vivre": "3.9"}, {"genres/mélodrame": "6.0", "thèmes/mal de vivre": "6.3"}, {"ambiance et ton/triste": "10.0", "ambiance et ton/émouvant": "10.0", "genres/drame": "2.0", "genres/mélodrame": "2.0"}, {"ambiance et ton/triste": "5.0", "ambiance et ton/émouvant": "5.0", "genres/drame": "5.0", "genres/mélodrame": "5.0", "thèmes/mal de vivre": "10.0"}, {"ambiance et ton/triste": "9.4", "ambiance et ton/émouvant": "9.4", "genres/drame": "4.7", "genres/mélodrame": "4.7", "thèmes/mal de vivre": "9.4"}, {"ambiance et ton/triste": "9.7", "genres/drame": "4.9", "thèmes/mal de vivre": "4.9"}, {"ambiance et ton/triste": "4.5", "genres/drame": "4.5", "genres/mélodrame": "4.5", "thèmes/mal de vivre": "9.0"}, {"ambiance et ton/émouvant": "7.6", "genres/drame": "6.3", "thèmes/mal de vivre": "7.3"}, {"ambiance et ton/émouvant": "6.3", "genres/drame": "2.8", "genres/mélodrame": "2.8", "thèmes/mal de vivre": "6.3"}, {"ambiance et ton/triste": "4.5", "ambiance et ton/émouvant": "9.0", "genres/drame": "4.5", "genres/mélodrame": "4.5", "thèmes/mal de vivre": "4.5"}, {"ambiance et ton/triste": "9.8", "ambiance et ton/émouvant": "4.9", "genres/drame": "4.9", "thèmes/mal de vivre": "4.9"}, {"ambiance et ton/émouvant": "7.9", "thèmes/mal de vivre": "7.6"}, {"ambiance et ton/triste": "7.3", "thèmes/mal de vivre": "7.4"}, {"ambiance et ton/émouvant": "6.3", "genres/mélodrame": "4.5", "thèmes/mal de vivre": "5.9"}, {"ambiance et ton/triste": "9.8", "ambiance et ton/émouvant": "9.8", "genres/drame": "4.9", "thèmes/mal de vivre": "4.9"}, {"ambiance et ton/émouvant": "4.1", "genres/drame": "4.1", "genres/mélodrame": "4.2", "thèmes/mal de vivre": "4.1"}, {"ambiance et ton/triste": "7.5", "ambiance et ton/émouvant": "7.5", "genres/mélodrame": "5.0", "thèmes/mal de vivre": "7.5"}, {"ambiance et ton/triste": "9.5", "ambiance et ton/émouvant": "4.8", "genres/drame": "4.8", "genres/mélodrame": "4.8", "thèmes/mal de vivre": "4.8"}, {"genres/drame": "3.0", "genres/mélodrame": "7.0"}, {"ambiance et ton/triste": "5.0", "ambiance et ton/émouvant": "5.0", "genres/drame": "2.0", "thèmes/mal de vivre": "10.0"}, {"ambiance et ton/triste": "3.0", "ambiance et ton/émouvant": "3.0", "genres/mélodrame": "5.0", "thèmes/mal de vivre": "3.0"}, {"ambiance et ton/triste": "4.9", "genres/mélodrame": "6.7"}, {"ambiance et ton/émouvant": "3.0", "genres/drame": "3.0", "genres/mélodrame": "3.0", "thèmes/mal de vivre": "6.0"}, {"ambiance et ton/triste": "4.5", "ambiance et ton/émouvant": "4.5", "genres/drame": "4.5", "genres/mélodrame": "4.5", "thèmes/mal de vivre": "4.5"}, {"ambiance et ton/triste": "5.0", "genres/mélodrame": "6.8", "thèmes/mal de vivre": "5.0"}, {"ambiance et ton/triste": "10.0", "ambiance et ton/émouvant": "5.3", "genres/mélodrame": "6.5"}, {"ambiance et ton/triste": "5.6", "ambiance et ton/émouvant": "6.1", "genres/drame": "8.7"}, {"ambiance et ton/triste": "6.3", "genres/drame": "8.6", "thèmes/mal de vivre": "6.5"}, {"ambiance et ton/triste": "9.9", "genres/drame": "5.0", "thèmes/mal de vivre": "9.9"}, {"genres/mélodrame": "7.0", "thèmes/mal de vivre": "2.0"}, {"ambiance et ton/émouvant": "8.8", "genres/drame": "4.8", "genres/mélodrame": "4.9"}, {"ambiance et ton/émouvant": "6.9", "genres/drame": "8.5"}, {"ambiance et ton/triste": "5.8", "ambiance et ton/émouvant": "6.1", "thèmes/mal de vivre": "6.0"}, {"ambiance et ton/triste": "6.9", "genres/drame": "8.9"}, {"ambiance et ton/triste": "6.0", "genres/mélodrame": "5.0", "thèmes/mal de vivre": "3.0"}], "28": [{"sujets/actualité": "6.0", "sujets/media et journalisme": "6.0", "sujets/presse": "10.0"}, {"sujets/actualité": "7.2", "sujets/media et journalisme": "4.8", "sujets/presse": "4.8", "type/débat": "9.7"}, {"sujets/actualité": "9.9", "sujets/media et journalisme": "4.8", "sujets/presse": "4.8"}, {"sujets/actualité": "10.0", "sujets/media et journalisme": "5.2", "sujets/presse": "5.2", "type/débat": "5.2"}, {"sujets/actualité": "8.8", "type/débat": "5.8"}, {"sujets/actualité": "9.0", "sujets/presse": "6.1", "type/débat": "9.0"}, {"sujets/presse": "2.0", "type/débat": "10.0"}, {"sujets/actualité": "8.3", "sujets/media et journalisme": "5.5"}, {"sujets/actualité": "10.0", "sujets/media et journalisme": "10.0", "sujets/presse": "10.0", "type/débat": "10.0"}, {"sujets/actualité": "7.7", "sujets/media et journalisme": "5.0", "type/débat": "6.3"}, {"sujets/media et journalisme": "10.0", "sujets/presse": "10.0"}, {"type/débat": "10.0"}, {"sujets/media et journalisme": "4.1", "type/débat": "9.9"}, {"sujets/actualité": "10.0"}, {"sujets/actualité": "10.0", "sujets/presse": "5.0", "type/débat": "5.0"}, {"sujets/presse": "7.0", "type/débat": "10.0"}, {"sujets/actualité": "2.0", "sujets/media et journalisme": "2.0", "sujets/presse": "2.0", "type/débat": "10.0"}, {"sujets/actualité": "10.0", "sujets/presse": "3.0", "type/débat": "2.0"}, {"sujets/media et journalisme": "10.0", "type/débat": "5.0"}, {"sujets/media et journalisme": "2.0", "sujets/presse": "2.0", "type/débat": "10.0"}, {"sujets/actualité": "5.0", "sujets/media et journalisme": "10.0", "sujets/presse": "10.0", "type/débat": "10.0"}, {"sujets/actualité": "10.0", "sujets/media et journalisme": "2.0", "sujets/presse": "2.0", "type/débat": "4.4"}, {"sujets/actualité": "9.9", "sujets/presse": "6.2"}, {"sujets/actualité": "5.0", "sujets/media et journalisme": "2.0", "sujets/presse": "2.0", "type/débat": "10.0"}], "29": [{"personnages/médecin": "8.2"}], "30": [{"sujets/sciences et techniques": "8.6"}], "31": [{"personnages/monstres": "4.6", "personnages/vampires": "4.6", "sous-genres/science-fiction": "9.2"}, {"personnages/monstres": "9.1"}, {"personnages/fantômes": "8.9"}, {"personnages/monstres": "4.5", "personnages/vampires": "4.5", "sous-genres/science-fiction": "4.5"}, {"personnages/vampires": "7.6"}, {"personnages/fantômes": "5.0", "personnages/monstres": "5.0", "personnages/vampires": "5.0", "sous-genres/science-fiction": "10.0"}, {"personnages/monstres": "7.1", "sous-genres/science-fiction": "7.7"}, {"personnages/fantômes": "3.0", "personnages/vampires": "5.0"}, {"personnages/fantômes": "6.0", "personnages/monstres": "5.5", "sous-genres/science-fiction": "6.1"}, {"personnages/vampires": "7.8", "sous-genres/science-fiction": "9.3"}, {"personnages/fantômes": "10.0", "personnages/monstres": "5.0", "personnages/vampires": "5.0", "sous-genres/science-fiction": "5.0"}, {"personnages/fantômes": "3.5", "personnages/monstres": "3.5", "personnages/vampires": "3.5", "sous-genres/science-fiction": "3.5"}, {"personnages/fantômes": "6.0", "personnages/vampires": "4.3", "sous-genres/science-fiction": "6.0"}, {"personnages/fantômes": "8.7", "personnages/monstres": "4.3", "personnages/vampires": "4.3", "sous-genres/science-fiction": "8.7"}, {"personnages/fantômes": "7.2", "sous-genres/science-fiction": "7.0"}, {"personnages/fantômes": "9.6", "personnages/monstres": "4.7", "personnages/vampires": "4.7"}, {"personnages/fantômes": "7.6", "personnages/monstres": "7.7"}, {"personnages/fantômes": "7.3", "personnages/vampires": "4.9"}, {"personnages/monstres": "5.0", "personnages/vampires": "5.1"}], "32": [{"sous-genres/télé-achat": "9.5"}], "34": [{"sujets/football": "9.1"}], "35": [{"catégories/contes et légendes": "5.4", "genres/aventure": "8.5", "thèmes/survie": "5.7"}, {"catégories/contes et légendes": "10.0", "thèmes/parcours initiatique": "10.0", "thèmes/survie": "10.0"}, {"genres/aventure": "7.9"}, {"catégories/contes et légendes": "10.0", "genres/aventure": "3.0", "thèmes/chasse au trésor": "3.0", "thèmes/parcours initiatique": "3.0"}, {"catégories/contes et légendes": "10.0", "genres/aventure": "5.0", "thèmes/chasse au trésor": "5.0", "thèmes/parcours initiatique": "5.0", "thèmes/survie": "5.0"}, {"genres/aventure": "7.8", "thèmes/chasse au trésor": "6.9"}, {"genres/aventure": "8.7", "thèmes/survie": "6.8"}, {"catégories/contes et légendes": "5.9", "genres/aventure": "5.9", "thèmes/chasse au trésor": "5.9", "thèmes/parcours initiatique": "5.9", "thèmes/survie": "5.9"}, {"catégories/contes et légendes": "4.8", "thèmes/chasse au trésor": "9.7", "thèmes/parcours initiatique": "4.8"}, {"catégories/contes et légendes": "9.7", "thèmes/survie": "9.7"}, {"catégories/contes et légendes": "5.0", "thèmes/chasse au trésor": "5.0", "thèmes/parcours initiatique": "5.0", "thèmes/survie": "5.0"}, {"genres/aventure": "8.1", "thèmes/parcours initiatique": "5.3", "thèmes/survie": "5.2"}, {"genres/aventure": "7.5", "personnages/aventurier": "7.5", "thèmes/parcours initiatique": "7.5"}, {"catégories/contes et légendes": "4.2", "genres/aventure": "8.5", "thèmes/chasse au trésor": "4.0", "thèmes/survie": "4.0"}, {"catégories/contes et légendes": "2.7", "genres/aventure": "10.0", "thèmes/chasse au trésor": "2.7", "thèmes/parcours initiatique": "5.7", "thèmes/survie": "2.7"}, {"genres/aventure": "7.8", "thèmes/chasse au trésor": "5.3", "thèmes/parcours initiatique": "5.2"}, {"catégories/contes et légendes": "4.7", "genres/aventure": "4.7", "thèmes/chasse au trésor": "9.4", "thèmes/parcours initiatique": "4.7"}, {"catégories/contes et légendes": "6.0", "thèmes/chasse au trésor": "6.0", "thèmes/survie": "3.0"}, {"genres/aventure": "4.3", "thèmes/chasse au trésor": "4.3", "thèmes/parcours initiatique": "4.3", "thèmes/survie": "8.7"}, {"catégories/contes et légendes": "10.0", "thèmes/chasse au trésor": "3.0", "thèmes/survie": "3.0"}, {"catégories/contes et légendes": "9.4", "thèmes/chasse au trésor": "6.3", "thèmes/parcours initiatique": "5.4"}, {"catégories/contes et légendes": "5.0", "genres/aventure": "10.0", "thèmes/chasse au trésor": "10.0", "thèmes/parcours initiatique": "5.0", "thèmes/survie": "5.0"}, {"catégories/contes et légendes": "3.0", "genres/aventure": "6.0", "thèmes/chasse au trésor": "10.0", "thèmes/survie": "3.0"}, {"thèmes/chasse au trésor": "10.0", "thèmes/parcours initiatique": "5.0", "thèmes/survie": "5.0"}, {"catégories/contes et légendes": "10.0", "genres/aventure": "5.0", "thèmes/parcours initiatique": "10.0", "thèmes/survie": "5.0"}, {"catégories/contes et légendes": "5.0", "genres/aventure": "5.0", "thèmes/chasse au trésor": "10.0", "thèmes/parcours initiatique": "5.0", "thèmes/survie": "5.0"}, {"catégories/contes et légendes": "5.0", "genres/aventure": "10.0", "thèmes/chasse au trésor": "5.0", "thèmes/parcours initiatique": "10.0", "thèmes/survie": "5.0"}, {"catégories/contes et légendes": "8.0", "thèmes/chasse au trésor": "8.0", "thèmes/survie": "8.0"}, {"catégories/contes et légendes": "5.0", "thèmes/chasse au trésor": "10.0", "thèmes/parcours initiatique": "5.0", "thèmes/survie": "5.0"}, {"catégories/contes et légendes": "5.5", "genres/aventure": "7.7", "thèmes/chasse au trésor": "5.8"}, {"catégories/contes et légendes": "3.9", "genres/aventure": "9.0", "thèmes/chasse au trésor": "3.4", "thèmes/parcours initiatique": "3.3", "thèmes/survie": "3.3"}, {"catégories/contes et légendes": "5.0", "genres/aventure": "5.0", "thèmes/chasse au trésor": "5.0", "thèmes/survie": "10.0"}, {"catégories/contes et légendes": "4.7", "genres/aventure": "5.1", "thèmes/parcours initiatique": "5.1", "thèmes/survie": "10.0"}, {"genres/aventure": "7.7", "thèmes/parcours initiatique": "5.6"}, {"thèmes/chasse au trésor": "10.0", "thèmes/parcours initiatique": "10.0", "thèmes/survie": "5.0"}, {"genres/aventure": "4.1", "thèmes/chasse au trésor": "8.3", "thèmes/parcours initiatique": "8.3"}, {"catégories/contes et légendes": "10.0", "genres/aventure": "5.0", "thèmes/chasse au trésor": "5.0", "thèmes/parcours initiatique": "10.0"}, {"catégories/contes et légendes": "5.0", "thèmes/chasse au trésor": "5.0", "thèmes/survie": "10.0"}, {"catégories/contes et légendes": "5.0", "genres/aventure": "5.0", "thèmes/parcours initiatique": "10.0", "thèmes/survie": "5.0"}, {"catégories/contes et légendes": "10.0", "genres/aventure": "2.5", "thèmes/chasse au trésor": "2.5", "thèmes/survie": "2.5"}, {"catégories/contes et légendes": "6.0", "genres/aventure": "6.0", "thèmes/parcours initiatique": "3.0", "thèmes/survie": "6.0"}, {"catégories/contes et légendes": "9.9", "thèmes/parcours initiatique": "9.9"}, {"thèmes/chasse au trésor": "9.8", "thèmes/survie": "9.8"}, {"catégories/contes et légendes": "8.0", "genres/aventure": "4.0", "thèmes/survie": "8.0"}, {"catégories/contes et légendes": "10.0", "genres/aventure": "3.0", "thèmes/parcours initiatique": "3.0", "thèmes/survie": "3.0"}, {"genres/aventure": "8.1", "thèmes/chasse au trésor": "4.4", "thèmes/survie": "4.6"}, {"thèmes/chasse au trésor": "9.9", "thèmes/parcours initiatique": "9.9"}, {"genres/aventure": "5.0", "personnages/aventurier": "5.0"}, {"catégories/contes et légendes": "4.4", "genres/aventure": "7.8", "thèmes/chasse au trésor": "4.3", "thèmes/parcours initiatique": "4.3"}, {"catégories/contes et légendes": "9.5", "genres/aventure": "4.8", "thèmes/parcours initiatique": "4.8", "thèmes/survie": "4.8"}, {"catégories/contes et légendes": "10.0", "thèmes/chasse au trésor": "5.0", "thèmes/survie": "5.0"}, {"catégories/contes et légendes": "5.3", "genres/aventure": "9.0", "thèmes/chasse au trésor": "2.3", "thèmes/parcours initiatique": "5.3", "thèmes/survie": "2.3"}, {"genres/aventure": "4.6", "thèmes/chasse au trésor": "9.3", "thèmes/parcours initiatique": "4.6", "thèmes/survie": "4.6"}, {"catégories/contes et légendes": "8.1", "thèmes/chasse au trésor": "8.6"}, {"catégories/contes et légendes": "5.0", "genres/aventure": "5.0", "thèmes/parcours initiatique": "10.0", "thèmes/survie": "10.0"}, {"catégories/contes et légendes": "3.0", "thèmes/chasse au trésor": "10.0", "thèmes/parcours initiatique": "3.0"}, {"catégories/contes et légendes": "4.9", "genres/aventure": "7.9", "thèmes/parcours initiatique": "4.8"}, {"genres/aventure": "4.7", "thèmes/chasse au trésor": "4.7", "thèmes/parcours initiatique": "9.3", "thèmes/survie": "4.7"}, {"catégories/contes et légendes": "10.0", "genres/aventure": "5.8", "thèmes/chasse au trésor": "2.8", "thèmes/parcours initiatique": "2.8"}, {"catégories/contes et légendes": "5.0", "genres/aventure": "5.0", "thèmes/chasse au trésor": "10.0", "thèmes/survie": "5.0"}, {"catégories/contes et légendes": "9.8", "genres/aventure": "4.9", "thèmes/chasse au trésor": "4.9", "thèmes/survie": "4.8"}, {"catégories/contes et légendes": "4.1", "genres/aventure": "8.4", "thèmes/parcours initiatique": "4.2", "thèmes/survie": "4.1"}, {"catégories/contes et légendes": "5.0", "thèmes/chasse au trésor": "5.0", "thèmes/parcours initiatique": "10.0"}, {"catégories/contes et légendes": "5.0", "genres/aventure": "10.0", "thèmes/chasse au trésor": "5.0", "thèmes/parcours initiatique": "5.0", "thèmes/survie": "10.0"}, {"catégories/contes et légendes": "6.0", "genres/aventure": "6.0", "thèmes/chasse au trésor": "3.0", "thèmes/parcours initiatique": "3.0", "thèmes/survie": "6.0"}, {"catégories/contes et légendes": "5.0", "thèmes/chasse au trésor": "10.0", "thèmes/survie": "5.0"}, {"catégories/contes et légendes": "4.7", "genres/aventure": "7.2"}, {"catégories/contes et légendes": "10.0", "thèmes/parcours initiatique": "5.0", "thèmes/survie": "5.0"}, {"catégories/contes et légendes": "9.1", "genres/aventure": "4.5", "thèmes/chasse au trésor": "4.5", "thèmes/parcours initiatique": "4.5"}, {"genres/aventure": "5.0", "thèmes/chasse au trésor": "10.0", "thèmes/survie": "10.0"}, {"genres/aventure": "8.2", "thèmes/chasse au trésor": "4.0", "thèmes/parcours initiatique": "3.8", "thèmes/survie": "3.9"}, {"catégories/contes et légendes": "2.7", "genres/aventure": "9.0", "thèmes/chasse au trésor": "2.7", "thèmes/parcours initiatique": "2.7", "thèmes/survie": "5.7"}, {"catégories/contes et légendes": "5.0", "genres/aventure": "7.0", "thèmes/chasse au trésor": "5.0", "thèmes/parcours initiatique": "5.0", "thèmes/survie": "2.0"}, {"catégories/contes et légendes": "3.0", "thèmes/chasse au trésor": "7.0", "thèmes/parcours initiatique": "7.0"}], "36": [{"sujets/ovalie": "5.5", "sujets/rugby": "9.7"}, {"sujets/rugby": "6.7"}], "37": [{"sujets/cyclisme": "9.2"}], "39": [{"sujets/basketball": "9.6"}]}