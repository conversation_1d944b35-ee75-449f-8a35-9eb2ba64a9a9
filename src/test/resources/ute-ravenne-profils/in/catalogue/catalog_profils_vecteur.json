{"1": [{"catégories/film": 2.5, "catégories/série": 8.0, "genres/thriller": 2.5, "ambiance et ton/suspense": 10.0}, {"catégories/information": 4, "catégories/série": 10, "sujets/actualité": 4, "sujets/media et journalisme": 4, "sujets/presse": 4}, {"catégories/information": 4, "catégories/série": 10, "genres/comédie": 10, "sujets/actualité": 4, "ambiance et ton/humoristique": 10}, {"catégories/magazine": 10, "catégories/série": 10, "sujets/société": 10, "ambiance et ton/suspense": 10}, {"catégories/divertissement": 4.0, "catégories/série": 10.0}, {"catégories/information": 3.5, "catégories/météo": 3.5, "catégories/série": 10.0}, {"catégories/information": 8.5, "catégories/météo": 8.5, "catégories/série": 10.0}, {"catégories/magazine": 4.5, "catégories/série": 10.0, "sujets/société": 4.5}, {"catégories/série": 10.0, "ambiance et ton/suspense": 2.5}, {"catégories/série": 10}, {"catégories/série": 10.0, "genres/comédie": 2.5, "ambiance et ton/humoristique": 2.5}, {"catégories/information": 6.67, "catégories/série": 10.0, "genres/comédie": 10.0, "sujets/actualité": 5.0, "sujets/la famille": 10.0, "sujets/media et journalisme": 5.0, "sujets/presse": 5.0, "ambiance et ton/humoristique": 10.0}, {"catégories/magazine": 10, "catégories/série": 10, "sujets/société": 10}, {"catégories/série jeunesse": 10}, {"catégories/série": 10.0, "genres/comédie": 4.5, "ambiance et ton/humoristique": 4.5}, {"catégories/série": 10, "sous-genres/science-fiction": 10}, {"catégories/divertissement": 10, "catégories/série": 10, "ambiance et ton/suspense": 10}, {"catégories/film": 10, "catégories/série": 10, "genres/thriller": 10, "ambiance et ton/suspense": 10}, {"catégories/information": 8.5, "catégories/série": 10.0, "sujets/actualité": 8.5, "sujets/media et journalisme": 8.5, "sujets/presse": 8.5}, {"catégories/information": 10, "catégories/série": 10, "ambiance et ton/humoristique": 10}, {"catégories/série": 10, "genres/comédie": 10, "ambiance et ton/humoristique": 10, "thèmes/amitié": 10}, {"catégories/série": 10.0, "ambiance et ton/suspense": 6.67}, {"catégories/série": 10.0, "genres/comédie": 9.0, "ambiance et ton/humoristique": 9.0}, {"catégories/série": 10, "genres/historique": 10, "sujets/Histoire": 10}, {"catégories/magazine": 2.5, "catégories/série": 10.0, "sujets/société": 2.5}, {"catégories/série": 10, "genres/action": 10}, {"catégories/film": 10, "catégories/série": 10, "ambiance et ton/suspense": 10}, {"catégories/information": 10, "catégories/série": 10, "genres/comédie": 10, "sujets/actualité": 10, "ambiance et ton/humoristique": 10}, {"catégories/dessins animés": 3.5, "catégories/série jeunesse": 10.0}, {"catégories/dessins animés": 9.0, "catégories/série jeunesse": 7.38}, {"catégories/divertissement": 10, "catégories/série": 10}, {"catégories/film": 10, "catégories/série": 10}, {"catégories/information": 10, "catégories/série": 10, "sujets/actualité": 10}, {"catégories/magazine": 10, "catégories/série": 4, "sujets/société": 10}, {"catégories/divertissement": 10, "catégories/série": 10, "genres/comédie": 10, "ambiance et ton/humoristique": 10}, {"catégories/divertissement": 10, "catégories/série": 10, "sous-genres/jeu": 10}, {"catégories/film": 5, "catégories/série": 5, "genres/thriller": 5, "ambiance et ton/suspense": 10}, {"catégories/information": 10, "catégories/météo": 10, "catégories/série": 10, "genres/comédie": 10, "ambiance et ton/humoristique": 10}, {"catégories/magazine": 10, "catégories/série": 10, "genres/comédie": 10, "sujets/société": 10, "ambiance et ton/humoristique": 10}, {"catégories/information": 10, "catégories/magazine": 10, "catégories/météo": 5, "catégories/série": 5, "genres/comédie": 5, "sujets/actualité": 5, "sujets/art et culture": 10, "sujets/la famille": 5, "sujets/media et journalisme": 5, "sujets/presse": 5, "ambiance et ton/humoristique": 5}, {"catégories/information": 10, "catégories/météo": 5, "catégories/série": 5, "genres/comédie": 5, "sujets/actualité": 5, "ambiance et ton/humoristique": 5}, {"catégories/autre": 5, "catégories/information": 10, "catégories/magazine": 10, "catégories/météo": 5, "catégories/série": 5, "ambiance et ton/humoristique": 5}, {"catégories/magazine": 10, "catégories/série": 10}, {"catégories/série jeunesse": 10, "catégories/série": 10, "genres/comédie": 10, "ambiance et ton/humoristique": 10}, {"catégories/information": 10, "catégories/série": 10, "sujets/actualité": 10, "ambiance et ton/suspense": 10}, {"catégories/divertissement": 4, "catégories/série": 10, "ambiance et ton/suspense": 10}, {"catégories/film": 4, "catégories/série": 10}, {"catégories/information": 4.5, "catégories/magazine": 4.5, "catégories/météo": 4.5, "catégories/série": 10.0, "sujets/sport": 4.5}, {"catégories/série": 10.0, "genres/action": 4.0}, {"catégories/série": 10, "personnages/médecin": 10}, {"catégories/information": 10, "catégories/magazine": 5, "catégories/météo": 5, "catégories/série": 5, "genres/comédie": 5, "sujets/actualité": 5, "sujets/société": 5, "ambiance et ton/humoristique": 5}, {"catégories/information": 3.5, "catégories/série": 10.0, "genres/comédie": 10.0, "sujets/actualité": 3.5, "ambiance et ton/humoristique": 10.0, "thèmes/amitié": 10.0}, {"catégories/dessins animés": 10, "catégories/série": 10, "genres/comédie": 10, "ambiance et ton/humoristique": 10}, {"catégories/dessins animés": 4, "catégories/série": 10}, {"catégories/magazine": 10, "catégories/série": 10, "sujets/sport": 10}, {"catégories/information": 4.5, "catégories/météo": 4.5, "catégories/série": 10.0, "genres/comédie": 4.5, "ambiance et ton/humoristique": 4.5}, {"catégories/information": 10, "catégories/série": 10}, {"catégories/série": 10, "personnages/fantômes": 10, "sous-genres/science-fiction": 10}, {"catégories/série": 10, "thèmes/vengeance et auto-justice": 10}, {"catégories/information": 10, "catégories/magazine": 10, "catégories/série": 10, "ambiance et ton/humoristique": 10}, {"catégories/film": 10, "catégories/série": 10, "genres/comédie": 10, "ambiance et ton/humoristique": 10, "ambiance et ton/suspense": 10}, {"catégories/film": 10, "catégories/série": 10, "genres/comédie": 10, "ambiance et ton/humoristique": 10}, {"catégories/information": 5, "catégories/magazine": 5, "catégories/météo": 5, "catégories/série": 10, "genres/comédie": 10, "sujets/art et culture": 5, "sujets/la famille": 10, "sujets/musique": 5, "ambiance et ton/humoristique": 10}, {"catégories/magazine": 4, "catégories/série": 10, "sujets/société": 4, "ambiance et ton/suspense": 10}, {"catégories/information": 4, "catégories/série": 10, "sujets/actualité": 4}, {"catégories/série": 10, "genres/action": 5, "ambiance et ton/suspense": 5}, {"catégories/magazine": 4, "catégories/série": 10, "genres/comédie": 10, "sujets/société": 4, "ambiance et ton/humoristique": 10}, {"catégories/film": 4, "catégories/série": 10, "genres/thriller": 4, "ambiance et ton/suspense": 4}, {"catégories/information": 10, "catégories/magazine": 10, "catégories/météo": 5, "catégories/série": 5}, {"catégories/information": 5, "catégories/magazine": 5, "catégories/série": 10, "genres/comédie": 10, "sujets/actualité": 5, "sujets/art et culture": 5, "sujets/la famille": 10, "sujets/media et journalisme": 5, "sujets/musique": 5, "sujets/presse": 5, "ambiance et ton/humoristique": 10}, {"catégories/divertissement": 4, "catégories/série": 10, "sous-genres/jeu": 4}, {"catégories/information": 10, "catégories/magazine": 10, "catégories/météo": 5, "catégories/série": 5, "genres/comédie": 5, "sujets/actualité": 5, "sujets/art et culture": 10, "sujets/la famille": 5, "sujets/media et journalisme": 5, "sujets/musique": 5, "sujets/presse": 5, "ambiance et ton/humoristique": 5}, {"catégories/divertissement": 4, "catégories/série": 10, "genres/comédie": 10, "ambiance et ton/humoristique": 10}, {"catégories/film": 4, "catégories/série": 10, "ambiance et ton/suspense": 10}, {"catégories/information": 10, "catégories/magazine": 5, "catégories/météo": 5, "catégories/série": 5}, {"catégories/divertissement": 10, "catégories/série": 10, "sous-genres/télé-réalité": 10}, {"catégories/série jeunesse": 10, "catégories/série": 10}, {"catégories/série": 10, "genres/comédie": 10, "sujets/la famille": 10, "ambiance et ton/humoristique": 10}, {"catégories/divertissement": 10, "catégories/série": 4}, {"catégories/musique": 10, "catégories/série": 10, "type/clip": 10, "genres/musical": 10}, {"catégories/dessins animés": 10, "catégories/série": 4, "genres/comédie": 4, "ambiance et ton/humoristique": 4}, {"catégories/film": 10, "catégories/série": 10, "genres/action": 10, "ambiance et ton/suspense": 10}, {"catégories/divertissement": 5, "catégories/information": 10, "catégories/météo": 5, "catégories/série": 5, "genres/comédie": 5, "sous-genres/jeu": 5, "sujets/actualité": 5, "ambiance et ton/humoristique": 5}, {"catégories/magazine": 10, "catégories/série": 10, "sujets/art et culture": 10, "thèmes/amour et séduction": 10}, {"catégories/série": 10, "genres/comédie": 5, "ambiance et ton/humoristique": 5, "thèmes/amitié": 5}, {"catégories/série": 10, "genres/action": 10, "lieu de l'action/Terrain de jeu de super héros": 10, "lieu de l'action/lieu imaginaire": 10}, {"catégories/série": 10, "genres/historique": 5, "sujets/Histoire": 5}, {"catégories/série": 10, "personnages/monstres": 10, "personnages/vampires": 10, "sous-genres/science-fiction": 10}], "2": [{"catégories/divertissement": 5.0, "catégories/information": 10.0, "catégories/magazine": 10.0, "catégories/météo": 5.0}, {"catégories/divertissement": 5, "catégories/information": 10, "catégories/météo": 5}, {"catégories/divertissement": 10, "catégories/information": 10, "catégories/météo": 10}, {"catégories/information": 8.5, "catégories/magazine": 10.0, "sujets/actualité": 8.5, "sujets/media et journalisme": 8.5, "sujets/presse": 8.5}, {"catégories/information": 10.0, "sujets/actualité": 10.0, "sujets/media et journalisme": 4.0, "sujets/presse": 4.0}, {"catégories/information": 10, "catégories/magazine": 4, "sujets/actualité": 10, "sujets/société": 4}, {"catégories/information": 10.0, "catégories/magazine": 4.25, "catégories/météo": 4.25, "sujets/actualité": 6.25, "sujets/media et journalisme": 5.5, "sujets/presse": 5.5}, {"catégories/information": 10, "catégories/magazine": 10, "sujets/actualité": 10, "sujets/politique": 10}, {"catégories/information": 10, "catégories/série": 10, "sujets/actualité": 10}, {"catégories/autre": 5, "catégories/information": 10, "catégories/météo": 5, "sujets/actualité": 5, "sujets/media et journalisme": 5, "sujets/presse": 5}, {"catégories/divertissement": 4.6, "catégories/information": 10.0, "catégories/météo": 4.6, "sous-genres/jeu": 4.6, "sujets/actualité": 6.0}, {"catégories/information": 9.0, "catégories/magazine": 9.0}, {"catégories/information": 10, "sujets/actualité": 10}, {"catégories/information": 10.0, "catégories/météo": 3.33, "sujets/actualité": 7.67, "sujets/media et journalisme": 3.33, "sujets/presse": 3.33}, {"catégories/information": 10.0, "catégories/météo": 4.45, "sujets/actualité": 6.09}, {"catégories/information": 10.0, "catégories/météo": 4.8, "sous-genres/jeu": 3.0, "sujets/actualité": 5.8}, {"catégories/information": 10.0, "catégories/météo": 7.75}, {"catégories/information": 10, "catégories/reportage": 10}, {"catégories/autre": 5, "catégories/information": 10, "catégories/magazine": 5, "catégories/météo": 5, "catégories/série": 5, "ambiance et ton/humoristique": 5}, {"catégories/divertissement": 5.0, "catégories/information": 10.0, "catégories/magazine": 5.0, "catégories/météo": 5.0}, {"catégories/divertissement": 5.0, "catégories/information": 10.0, "catégories/magazine": 5.0, "catégories/météo": 5.0, "sujets/actualité": 5.0, "sujets/media et journalisme": 5.0, "sujets/presse": 5.0}, {"catégories/divertissement": 10, "catégories/information": 10, "catégories/météo": 5, "sous-genres/jeu": 10, "sujets/actualité": 5}, {"catégories/information": 6.5, "catégories/magazine": 10.0, "sujets/actualité": 4.0, "sujets/media et journalisme": 4.0, "sujets/presse": 4.0, "sujets/société": 8.25}, {"catégories/information": 10.0, "catégories/magazine": 4.0, "catégories/météo": 4.0, "sujets/actualité": 6.67, "sujets/sport": 4.0}, {"catégories/information": 10.0, "catégories/magazine": 4.5, "catégories/météo": 4.5, "sujets/actualité": 6.0, "sujets/société": 4.5}, {"catégories/information": 10, "catégories/magazine": 5, "catégories/météo": 5, "catégories/série": 5, "ambiance et ton/humoristique": 5}, {"catégories/information": 10.0, "catégories/magazine": 7.0, "sujets/actualité": 6.0, "sujets/media et journalisme": 6.0, "sujets/presse": 6.0}, {"catégories/information": 10.0, "catégories/magazine": 7.33, "catégories/météo": 7.33}, {"catégories/information": 10, "catégories/magazine": 10, "sujets/actualité": 10, "sujets/société": 10}, {"catégories/information": 10, "catégories/magazine": 10, "catégories/météo": 5, "catégories/série": 5, "ambiance et ton/humoristique": 5}, {"catégories/divertissement": 5, "catégories/information": 5, "catégories/magazine": 10}, {"catégories/information": 10, "catégories/magazine": 5, "catégories/météo": 5, "catégories/série": 10, "genres/comédie": 10, "sujets/actualité": 5, "sujets/art et culture": 5, "sujets/la famille": 10, "sujets/media et journalisme": 5, "sujets/musique": 5, "sujets/presse": 5, "ambiance et ton/humoristique": 10}, {"catégories/information": 10, "catégories/magazine": 10, "catégories/météo": 5, "sujets/actualité": 5, "sujets/media et journalisme": 5, "sujets/presse": 5, "sujets/société": 10}, {"catégories/information": 10, "catégories/météo": 10, "catégories/série": 10, "genres/comédie": 10, "ambiance et ton/humoristique": 10}, {"catégories/information": 10, "catégories/série": 10, "genres/comédie": 10, "sujets/actualité": 10, "ambiance et ton/humoristique": 10}, {"catégories/information": 10.0, "sujets/actualité": 10.0, "sujets/media et journalisme": 9.0, "sujets/presse": 9.0}, {"catégories/information": 10, "catégories/magazine": 10, "catégories/météo": 5, "catégories/série": 5, "genres/comédie": 5, "sujets/actualité": 5, "sujets/art et culture": 10, "sujets/la famille": 5, "sujets/media et journalisme": 5, "sujets/presse": 5, "ambiance et ton/humoristique": 5}, {"catégories/information": 10, "catégories/météo": 5, "catégories/série": 5, "genres/comédie": 5, "sujets/actualité": 5, "ambiance et ton/humoristique": 5}, {"catégories/information": 10.0, "catégories/météo": 5.59, "sujets/actualité": 5.06, "sujets/media et journalisme": 3.94, "sujets/presse": 3.94}, {"catégories/autre": 5, "catégories/information": 10, "catégories/magazine": 10, "catégories/météo": 5, "catégories/série": 5, "ambiance et ton/humoristique": 5}, {"catégories/divertissement": 5.0, "catégories/information": 10.0, "catégories/magazine": 5.0, "catégories/météo": 5.0, "catégories/série": 5.0, "ambiance et ton/humoristique": 5.0}, {"catégories/divertissement": 5, "catégories/information": 10, "catégories/météo": 5, "sujets/actualité": 5, "sujets/media et journalisme": 5, "sujets/presse": 5}, {"catégories/divertissement": 10, "catégories/information": 4, "catégories/météo": 4}, {"catégories/divertissement": 10.0, "catégories/information": 5.5, "sous-genres/jeu": 10.0, "sujets/actualité": 4.0}, {"catégories/divertissement": 10, "catégories/information": 10, "catégories/magazine": 10}, {"catégories/information": 4, "catégories/série": 10, "sujets/actualité": 4, "sujets/media et journalisme": 4, "sujets/presse": 4}, {"catégories/information": 7, "catégories/magazine": 10, "thèmes/découverte": 4}, {"catégories/information": 10, "catégories/magazine": 10, "sujets/actualité": 10, "sujets/sport": 10}, {"catégories/information": 10, "catégories/magazine": 10, "catégories/météo": 10, "sujets/société": 10}, {"catégories/information": 10, "catégories/magazine": 10, "catégories/météo": 10, "sujets/sport": 10}, {"catégories/information": 4, "catégories/série": 10, "genres/comédie": 10, "sujets/actualité": 4, "ambiance et ton/humoristique": 10}, {"catégories/information": 8.5, "catégories/série": 10.0, "sujets/actualité": 8.5, "sujets/media et journalisme": 8.5, "sujets/presse": 8.5}, {"catégories/information": 10, "catégories/série": 10, "ambiance et ton/humoristique": 10}, {"catégories/divertissement": 10.0, "catégories/information": 6.5, "catégories/météo": 6.5, "sous-genres/jeu": 10.0, "thèmes/argent et trafics": 6.5}, {"catégories/divertissement": 10, "catégories/information": 10, "sous-genres/jeu": 10, "sujets/actualité": 10}, {"catégories/divertissement": 10, "catégories/information": 10, "sujets/actualité": 10}, {"catégories/information": 10, "catégories/série": 10, "sujets/actualité": 10, "ambiance et ton/suspense": 10}, {"catégories/divertissement": 4, "catégories/information": 10, "catégories/météo": 10, "sous-genres/jeu": 4, "thèmes/argent et trafics": 4}, {"catégories/divertissement": 7.5, "catégories/information": 10.0, "sous-genres/jeu": 7.5, "sujets/actualité": 5.0}, {"catégories/divertissement": 10, "catégories/information": 10, "sujets/actualité": 10, "sujets/media et journalisme": 10, "sujets/presse": 10}, {"catégories/information": 4.5, "catégories/magazine": 4.5, "catégories/météo": 4.5, "catégories/série": 10.0, "sujets/sport": 4.5}, {"catégories/divertissement": 4, "catégories/information": 10, "catégories/météo": 10, "sous-genres/jeu": 4}, {"catégories/information": 10, "catégories/météo": 3, "sujets/actualité": 8, "sujets/media et journalisme": 8, "sujets/presse": 8}, {"catégories/information": 10, "catégories/magazine": 5, "catégories/météo": 5, "catégories/série": 5, "genres/comédie": 5, "sujets/actualité": 5, "sujets/société": 5, "ambiance et ton/humoristique": 5}, {"catégories/information": 3.5, "catégories/série": 10.0, "genres/comédie": 10.0, "sujets/actualité": 3.5, "ambiance et ton/humoristique": 10.0, "thèmes/amitié": 10.0}, {"catégories/autre": 5.0, "catégories/divertissement": 5.0, "catégories/information": 10.0, "catégories/météo": 5.0, "sujets/actualité": 5.0, "sujets/media et journalisme": 5.0, "sujets/presse": 5.0}, {"catégories/information": 4.5, "catégories/météo": 4.5, "catégories/série": 10.0, "genres/comédie": 4.5, "ambiance et ton/humoristique": 4.5}, {"catégories/information": 10.0, "catégories/météo": 6.5, "sujets/actualité": 4.5, "sujets/media et journalisme": 4.5, "sujets/presse": 4.5, "sujets/sport": 2.0}, {"catégories/information": 10, "catégories/série": 10}, {"catégories/divertissement": 4, "catégories/information": 10, "sous-genres/jeu": 4, "sujets/actualité": 10}, {"catégories/information": 10, "catégories/magazine": 10, "catégories/météo": 5, "thèmes/découverte": 5}, {"catégories/information": 10.0, "catégories/météo": 7.5, "ambiance et ton/humoristique": 3.5}, {"catégories/information": 10, "catégories/reportage": 10, "genres/documentaire": 10, "sujets/actualité": 10, "thèmes/découverte": 10}, {"catégories/information": 10, "catégories/magazine": 10, "catégories/météo": 10, "sujets/art et culture": 10}, {"catégories/information": 10, "catégories/magazine": 10, "catégories/série": 10, "ambiance et ton/humoristique": 10}, {"catégories/information": 10, "catégories/magazine": 10, "catégories/météo": 10, "sujets/art et culture": 10, "sujets/musique": 10}, {"catégories/information": 5, "catégories/magazine": 5, "catégories/météo": 5, "catégories/série": 10, "genres/comédie": 10, "sujets/art et culture": 5, "sujets/la famille": 10, "sujets/musique": 5, "ambiance et ton/humoristique": 10}, {"catégories/information": 4, "catégories/série": 10, "sujets/actualité": 4}, {"catégories/information": 10, "catégories/magazine": 4, "sujets/actualité": 10, "sujets/media et journalisme": 10, "sujets/presse": 10, "sujets/société": 4}, {"catégories/information": 10, "catégories/magazine": 10, "catégories/météo": 5, "sujets/actualité": 5, "sujets/media et journalisme": 5, "sujets/presse": 5}, {"catégories/information": 10, "catégories/reportage": 10, "genres/documentaire": 10, "sujets/actualité": 10, "sujets/société": 10}, {"catégories/information": 10, "catégories/magazine": 10, "catégories/météo": 5, "catégories/série": 5}, {"catégories/information": 5, "catégories/magazine": 5, "catégories/série": 10, "genres/comédie": 10, "sujets/actualité": 5, "sujets/art et culture": 5, "sujets/la famille": 10, "sujets/media et journalisme": 5, "sujets/musique": 5, "sujets/presse": 5, "ambiance et ton/humoristique": 10}, {"catégories/information": 10, "catégories/magazine": 10, "catégories/météo": 5, "catégories/série": 5, "genres/comédie": 5, "sujets/actualité": 5, "sujets/art et culture": 10, "sujets/la famille": 5, "sujets/media et journalisme": 5, "sujets/musique": 5, "sujets/presse": 5, "ambiance et ton/humoristique": 5}, {"catégories/information": 10, "catégories/magazine": 5, "catégories/météo": 5, "catégories/série": 5}, {"catégories/information": 10, "catégories/magazine": 10, "catégories/reportage": 10}, {"catégories/information": 9.0, "catégories/magazine": 9.0, "sujets/actualité": 4.5, "sujets/media et journalisme": 4.5, "sujets/presse": 4.5, "sujets/société": 8.0}, {"catégories/dessins animés": 10, "catégories/information": 4, "catégories/météo": 4}, {"catégories/divertissement": 5, "catégories/information": 10, "catégories/météo": 5, "catégories/série": 5, "genres/comédie": 5, "sous-genres/jeu": 5, "sujets/actualité": 5, "ambiance et ton/humoristique": 5}, {"catégories/information": 10, "catégories/magazine": 10, "catégories/météo": 10, "sujets/actualité": 10, "thèmes/découverte": 10}, {"catégories/information": 10, "catégories/magazine": 4, "catégories/météo": 10, "sujets/société": 4}], "3": [{"catégories/information": 3.5, "catégories/magazine": 10.0, "catégories/météo": 3.5, "sujets/société": 10.0}, {"catégories/information": 10, "catégories/magazine": 10, "catégories/météo": 5}, {"catégories/magazine": 5, "catégories/reportage": 5, "genres/documentaire": 5, "sujets/société": 10}, {"catégories/information": 10, "catégories/magazine": 10, "catégories/météo": 10, "sujets/société": 10}, {"catégories/magazine": 10, "sujets/société": 5, "sujets/sport": 5}, {"catégories/magazine": 10, "catégories/série": 4, "sujets/société": 10}, {"catégories/reportage": 10, "genres/documentaire": 10, "sujets/société": 10}, {"genres/comédie": 10, "sujets/société": 10, "ambiance et ton/humoristique": 10}, {"catégories/magazine": 10, "catégories/série": 10, "sujets/société": 10, "ambiance et ton/suspense": 10}, {"catégories/divertissement": 10, "catégories/magazine": 4, "sujets/société": 4}, {"catégories/film": 10, "catégories/magazine": 10, "sujets/société": 10}, {"catégories/information": 4, "catégories/magazine": 10, "sujets/actualité": 4, "sujets/société": 10}, {"catégories/information": 5.33, "catégories/magazine": 10.0, "catégories/météo": 5.33, "sujets/société": 5.33}, {"catégories/information": 10, "catégories/magazine": 4, "sujets/actualité": 10, "sujets/société": 4}, {"catégories/information": 10.0, "catégories/magazine": 4.5, "catégories/météo": 4.5, "sujets/actualité": 6.0, "sujets/société": 4.5}, {"catégories/information": 10, "catégories/magazine": 10, "type/débat": 10, "actions/discussion": 10, "sujets/actualité": 10}, {"catégories/magazine": 10, "genres/historique": 10, "sujets/Histoire": 10, "sujets/société": 10}, {"catégories/magazine": 10, "catégories/reportage": 10, "genres/documentaire": 10, "sujets/société": 10, "thèmes/découverte": 10}, {"catégories/reportage": 10, "genres/documentaire": 10, "sujets/société": 10, "ambiance et ton/onirique": 10}, {"catégories/divertissement": 4, "catégories/magazine": 10, "sujets/société": 10}, {"catégories/divertissement": 10, "catégories/information": 10, "sous-genres/jeu": 10, "sujets/actualité": 10}, {"catégories/divertissement": 10, "catégories/magazine": 10, "sous-genres/jeu": 10, "sujets/société": 10}, {"catégories/information": 10, "catégories/magazine": 10, "catégories/météo": 5, "sujets/actualité": 5, "sujets/media et journalisme": 5, "sujets/presse": 5, "sujets/société": 10}, {"catégories/magazine": 10, "catégories/série": 10, "genres/comédie": 10, "sujets/société": 10, "ambiance et ton/humoristique": 10}, {"catégories/dessins animés": 10, "catégories/magazine": 10, "sujets/société": 10}, {"catégories/divertissement": 10.0, "catégories/magazine": 9.0, "sujets/société": 9.0}, {"catégories/information": 10.0, "catégories/magazine": 7.33, "catégories/météo": 7.33}, {"catégories/information": 10, "catégories/magazine": 10, "sujets/actualité": 10, "sujets/société": 10}, {"catégories/magazine": 2.5, "catégories/série": 10.0, "sujets/société": 2.5}, {"catégories/magazine": 10, "lieu de l'action/habitation": 10, "sujets/société": 10}, {"catégories/magazine": 10, "catégories/série": 10, "sujets/société": 10}, {"catégories/divertissement": 10, "catégories/information": 10, "sujets/actualité": 10}, {"catégories/film": 10, "catégories/magazine": 10, "genres/thriller": 10, "sujets/société": 10, "ambiance et ton/suspense": 10}, {"catégories/magazine": 10, "catégories/reportage": 4, "genres/documentaire": 4, "sujets/société": 10, "thèmes/découverte": 4}, {"catégories/information": 10, "catégories/magazine": 5, "catégories/météo": 5, "catégories/série": 5, "genres/comédie": 5, "sujets/actualité": 5, "sujets/société": 5, "ambiance et ton/humoristique": 5}, {"catégories/reportage": 10, "genres/documentaire": 10, "sujets/société": 5, "thèmes/découverte": 5}, {"catégories/magazine": 10, "sujets/politique": 5, "sujets/société": 5}, {"catégories/magazine": 10, "sujets/société": 3, "sujets/sport": 8}, {"catégories/magazine": 4, "catégories/série": 10, "sujets/société": 4, "ambiance et ton/suspense": 10}, {"sous-genres/jeu": 10, "sujets/société": 10}, {"catégories/information": 10, "catégories/magazine": 4, "sujets/actualité": 10, "sujets/media et journalisme": 10, "sujets/presse": 10, "sujets/société": 4}, {"catégories/magazine": 4, "catégories/série": 10, "genres/comédie": 10, "sujets/société": 4, "ambiance et ton/humoristique": 10}, {"catégories/magazine": 10, "catégories/musique": 10, "type/clip": 10, "genres/musical": 10, "sujets/société": 10}, {"catégories/information": 10, "catégories/reportage": 10, "genres/documentaire": 10, "sujets/actualité": 10, "sujets/société": 10}, {"catégories/magazine": 8, "catégories/reportage": 3, "genres/documentaire": 3, "sujets/société": 10}, {"catégories/film": 10, "catégories/magazine": 10, "genres/comédie": 10, "sujets/société": 10, "ambiance et ton/humoristique": 10}, {"catégories/magazine": 10, "sujets/société": 8, "sujets/sport": 3}, {"catégories/divertissement": 10, "catégories/magazine": 4, "sous-genres/jeu": 10, "sujets/société": 4}, {"catégories/information": 9.0, "catégories/magazine": 9.0, "sujets/actualité": 4.5, "sujets/media et journalisme": 4.5, "sujets/presse": 4.5, "sujets/société": 8.0}, {"catégories/magazine": 3, "catégories/reportage": 8, "genres/documentaire": 8, "sujets/société": 10}, {"catégories/information": 10, "catégories/magazine": 4, "catégories/météo": 10, "sujets/société": 4}], "4": [{"catégories/information": 10, "catégories/magazine": 10, "catégories/météo": 10, "sujets/sport": 10}, {"sujets/sport": 10}, {"catégories/magazine": 5, "catégories/sport": 5, "sujets/football": 5, "sujets/sport": 10}, {"catégories/magazine": 10, "sujets/sport": 10}, {"catégories/sport": 10}, {"catégories/sport": 10, "sujets/football": 10, "sujets/sport": 10}, {"catégories/information": 10, "catégories/magazine": 10, "sujets/actualité": 10, "sujets/sport": 10}, {"catégories/sport": 10, "sujets/ovalie": 10, "sujets/rugby": 10, "sujets/sport": 10}, {"catégories/divertissement": 10, "catégories/magazine": 10, "sujets/sport": 10}, {"catégories/information": 10.0, "catégories/magazine": 4.0, "catégories/météo": 4.0, "sujets/actualité": 6.67, "sujets/sport": 4.0}, {"catégories/magazine": 10, "sujets/société": 5, "sujets/sport": 5}, {"catégories/divertissement": 10, "catégories/magazine": 10, "sous-genres/jeu": 10, "sujets/sport": 10}, {"catégories/information": 4.5, "catégories/magazine": 4.5, "catégories/météo": 4.5, "catégories/série": 10.0, "sujets/sport": 4.5}, {"catégories/magazine": 10, "sujets/media et journalisme": 10, "sujets/presse": 10, "sujets/sport": 10}, {"catégories/magazine": 10, "catégories/sport": 5, "sujets/sciences et techniques": 5, "sujets/sport": 5}, {"catégories/magazine": 10, "catégories/série": 10, "sujets/sport": 10}, {"catégories/information": 10.0, "catégories/météo": 6.5, "sujets/actualité": 4.5, "sujets/media et journalisme": 4.5, "sujets/presse": 4.5, "sujets/sport": 2.0}, {"catégories/divertissement": 10, "catégories/sport": 10, "sujets/football": 10, "sujets/sport": 10}, {"catégories/magazine": 10, "sujets/société": 3, "sujets/sport": 8}, {"catégories/dessins animés": 10, "catégories/magazine": 4, "sujets/sport": 4}, {"catégories/sport": 10, "sujets/cyclisme": 10, "sujets/sport": 10}, {"catégories/magazine": 3, "catégories/sport": 8, "sujets/football": 8, "sujets/sport": 10}, {"catégories/magazine": 10, "sujets/société": 8, "sujets/sport": 3}, {"catégories/magazine": 10, "lieu de l'action/Europe": 10, "sujets/sport": 10}, {"catégories/reportage": 10, "catégories/sport": 10}, {"catégories/magazine": 5, "catégories/sport": 5, "sujets/ovalie": 5, "sujets/rugby": 5, "sujets/sport": 10}, {"catégories/magazine": 10, "sujets/basketball": 10, "sujets/sport": 10}], "5": [{"catégories/film": 10, "format/animation": 10, "genres/animation": 10}, {"format/animation": 10, "genres/animation": 10}], "6": [{"catégories/dessins animés": 3.5, "catégories/série jeunesse": 10.0}, {"catégories/dessins animés": 10.0, "genres/aventure": 3.5}, {"catégories/dessins animés": 10.0, "personnages/médecin": 2.5}, {"catégories/dessins animés": 10.0, "personnages/personnages jeunesse": 2.5}, {"catégories/dessins animés": 10.0, "personnages/personnages jeunesse": 2.5, "personnages/scooby-doo": 2.5}, {"catégories/dessins animés": 10}, {"catégories/dessins animés": 10.0, "catégories/divertissement": 3.0}, {"catégories/dessins animés": 10.0, "catégories/divertissement": 3.0, "sous-genres/jeu": 3.0}, {"catégories/dessins animés": 10.0, "catégories/information": 3.0}, {"catégories/dessins animés": 10.0, "catégories/magazine jeunesse": 3.0}, {"catégories/dessins animés": 10.0, "catégories/magazine": 3.0, "sujets/société": 2.75}, {"catégories/dessins animés": 10.0, "catégories/magazine": 3.5}, {"catégories/dessins animés": 10.0, "catégories/série jeunesse": 3.0}, {"catégories/dessins animés": 10.0, "catégories/série": 5.17}, {"catégories/dessins animés": 10, "format/animation": 10, "format/dessin animé": 10}, {"catégories/magazine jeunesse": 10}, {"catégories/dessins animés": 9.0, "catégories/série jeunesse": 7.38}, {"catégories/dessins animés": 10, "catégories/magazine": 10, "sujets/société": 10}, {"catégories/série jeunesse": 10}, {"catégories/série jeunesse": 10, "catégories/série": 10, "genres/comédie": 10, "ambiance et ton/humoristique": 10}, {"catégories/dessins animés": 10.0, "personnages/prince et princesse": 2.5}, {"catégories/dessins animés": 10, "catégories/série": 10, "genres/comédie": 10, "ambiance et ton/humoristique": 10}, {"catégories/dessins animés": 4, "catégories/série": 10}, {"catégories/dessins animés": 10, "catégories/divertissement": 10}, {"catégories/dessins animés": 10.0, "catégories/film": 3.0}, {"catégories/dessins animés": 10, "catégories/magazine": 4, "sujets/sport": 4}, {"catégories/dessins animés": 10, "catégories/musique": 4, "type/clip": 4, "genres/musical": 4}, {"catégories/dessins animés": 10.0, "personnages/détective": 2.5}, {"catégories/dessins animés": 10, "catégories/information": 4, "sujets/actualité": 4}, {"catégories/dessins animés": 10.0, "personnages/barbapapa": 2.5, "personnages/personnages jeunesse": 2.5}, {"catégories/dessins animés": 10, "personnages/personnages jeunesse": 10, "personnages/scooby-doo": 10}, {"catégories/divertissement": 10, "catégories/série jeunesse": 10}, {"catégories/magazine jeunesse": 4, "catégories/série jeunesse": 10}, {"catégories/série jeunesse": 10, "catégories/série": 10}, {"catégories/dessins animés": 10, "catégories/information": 4, "catégories/météo": 4}, {"catégories/dessins animés": 10, "catégories/série": 4, "genres/comédie": 4, "ambiance et ton/humoristique": 4}], "7": [{"catégories/musique": 10, "type/clip": 10, "genres/musical": 10}, {"catégories/magazine": 10, "sujets/art et culture": 10, "sujets/musique": 10}, {"catégories/reportage": 10, "genres/documentaire": 10, "sujets/art et culture": 10, "sujets/musique": 10}, {"catégories/information": 10, "catégories/magazine": 10, "catégories/météo": 10, "sujets/art et culture": 10, "sujets/musique": 10}, {"genres/musical": 10}, {"catégories/dessins animés": 10, "catégories/musique": 4, "type/clip": 4, "genres/musical": 4}, {"catégories/magazine": 10, "catégories/musique": 10, "type/clip": 10, "genres/musical": 10, "sujets/société": 10}, {"catégories/musique": 10, "catégories/série": 10, "type/clip": 10, "genres/musical": 10}, {"sujets/art et culture": 10, "sujets/musique": 10}], "8": [{"catégories/série": 10, "genres/action": 10, "lieu de l'action/Terrain de jeu de super héros": 10, "lieu de l'action/lieu imaginaire": 10}, {"catégories/dessins animés": 10, "personnages/personnages jeunesse": 5, "personnages/scooby-doo": 5}], "9": [{"genres/comédie": 10, "ambiance et ton/humoristique": 10}, {"ambiance et ton/humoristique": 10}, {"genres/comédie": 10}, {"catégories/film": 10.0, "genres/comédie": 9.0, "ambiance et ton/humoristique": 9.0}, {"catégories/information": 10, "catégories/série": 10, "genres/comédie": 10, "sujets/actualité": 10, "ambiance et ton/humoristique": 10}, {"catégories/magazine": 10, "catégories/série": 10, "genres/comédie": 10, "sujets/société": 10, "ambiance et ton/humoristique": 10}, {"catégories/information": 4, "catégories/série": 10, "genres/comédie": 10, "sujets/actualité": 4, "ambiance et ton/humoristique": 10}, {"catégories/autre": 5, "catégories/information": 10, "catégories/magazine": 5, "catégories/météo": 5, "catégories/série": 5, "ambiance et ton/humoristique": 5}, {"catégories/film": 10, "genres/comédie": 5, "ambiance et ton/humoristique": 5}, {"catégories/information": 6.67, "catégories/série": 10.0, "genres/comédie": 10.0, "sujets/actualité": 5.0, "sujets/la famille": 10.0, "sujets/media et journalisme": 5.0, "sujets/presse": 5.0, "ambiance et ton/humoristique": 10.0}, {"catégories/information": 10, "catégories/magazine": 5, "catégories/météo": 5, "catégories/série": 5, "ambiance et ton/humoristique": 5}, {"catégories/série": 10.0, "genres/comédie": 4.5, "ambiance et ton/humoristique": 4.5}, {"catégories/série": 10.0, "genres/comédie": 9.0, "ambiance et ton/humoristique": 9.0}, {"catégories/divertissement": 5.0, "catégories/information": 10.0, "catégories/magazine": 5.0, "catégories/météo": 5.0, "catégories/série": 5.0, "ambiance et ton/humoristique": 5.0}, {"catégories/divertissement": 10, "catégories/série": 10, "genres/comédie": 10, "ambiance et ton/humoristique": 10}, {"catégories/information": 10, "catégories/météo": 10, "catégories/série": 10, "genres/comédie": 10, "ambiance et ton/humoristique": 10}, {"catégories/information": 10, "catégories/série": 10, "ambiance et ton/humoristique": 10}, {"catégories/information": 10, "catégories/magazine": 10, "catégories/météo": 5, "catégories/série": 5, "genres/comédie": 5, "sujets/actualité": 5, "sujets/art et culture": 10, "sujets/la famille": 5, "sujets/media et journalisme": 5, "sujets/presse": 5, "ambiance et ton/humoristique": 5}, {"genres/comédie": 10, "sujets/société": 10, "ambiance et ton/humoristique": 10}, {"catégories/autre": 5, "catégories/information": 10, "catégories/magazine": 10, "catégories/météo": 5, "catégories/série": 5, "ambiance et ton/humoristique": 5}, {"catégories/information": 10, "catégories/magazine": 5, "catégories/météo": 5, "catégories/série": 10, "genres/comédie": 10, "sujets/actualité": 5, "sujets/art et culture": 5, "sujets/la famille": 10, "sujets/media et journalisme": 5, "sujets/musique": 5, "sujets/presse": 5, "ambiance et ton/humoristique": 10}, {"catégories/information": 10, "catégories/météo": 5, "catégories/série": 5, "genres/comédie": 5, "sujets/actualité": 5, "ambiance et ton/humoristique": 5}, {"catégories/série": 10, "genres/comédie": 10, "ambiance et ton/humoristique": 10, "thèmes/amitié": 10}, {"catégories/série jeunesse": 10, "catégories/série": 10, "genres/comédie": 10, "ambiance et ton/humoristique": 10}, {"catégories/information": 10, "catégories/magazine": 5, "catégories/météo": 5, "catégories/série": 5, "genres/comédie": 5, "sujets/actualité": 5, "sujets/société": 5, "ambiance et ton/humoristique": 5}, {"catégories/dessins animés": 10, "catégories/série": 10, "genres/comédie": 10, "ambiance et ton/humoristique": 10}, {"catégories/information": 4.5, "catégories/météo": 4.5, "catégories/série": 10.0, "genres/comédie": 4.5, "ambiance et ton/humoristique": 4.5}, {"catégories/information": 10.0, "catégories/météo": 7.5, "ambiance et ton/humoristique": 3.5}, {"catégories/information": 10, "catégories/magazine": 10, "catégories/série": 10, "ambiance et ton/humoristique": 10}, {"catégories/film": 10, "catégories/série": 10, "genres/comédie": 10, "ambiance et ton/humoristique": 10, "ambiance et ton/suspense": 10}, {"catégories/film": 10, "catégories/série": 10, "genres/comédie": 10, "ambiance et ton/humoristique": 10}, {"catégories/information": 5, "catégories/magazine": 5, "catégories/météo": 5, "catégories/série": 10, "genres/comédie": 10, "sujets/art et culture": 5, "sujets/la famille": 10, "sujets/musique": 5, "ambiance et ton/humoristique": 10}, {"catégories/film": 10, "genres/action": 5, "genres/comédie": 5, "ambiance et ton/humoristique": 5}, {"catégories/film": 10, "genres/comédie": 5, "genres/thriller": 5, "ambiance et ton/humoristique": 5, "ambiance et ton/suspense": 5}, {"catégories/magazine": 4, "catégories/série": 10, "genres/comédie": 10, "sujets/société": 4, "ambiance et ton/humoristique": 10}, {"catégories/film": 10, "catégories/magazine": 10, "genres/comédie": 10, "sujets/société": 10, "ambiance et ton/humoristique": 10}, {"catégories/information": 5, "catégories/magazine": 5, "catégories/série": 10, "genres/comédie": 10, "sujets/actualité": 5, "sujets/art et culture": 5, "sujets/la famille": 10, "sujets/media et journalisme": 5, "sujets/musique": 5, "sujets/presse": 5, "ambiance et ton/humoristique": 10}, {"catégories/information": 10, "catégories/magazine": 10, "catégories/météo": 5, "catégories/série": 5, "genres/comédie": 5, "sujets/actualité": 5, "sujets/art et culture": 10, "sujets/la famille": 5, "sujets/media et journalisme": 5, "sujets/musique": 5, "sujets/presse": 5, "ambiance et ton/humoristique": 5}, {"catégories/divertissement": 10, "catégories/film": 10, "genres/comédie": 10, "ambiance et ton/humoristique": 10}, {"catégories/divertissement": 4, "catégories/série": 10, "genres/comédie": 10, "ambiance et ton/humoristique": 10}, {"catégories/série": 10, "genres/comédie": 10, "sujets/la famille": 10, "ambiance et ton/humoristique": 10}, {"catégories/dessins animés": 10, "catégories/série": 4, "genres/comédie": 4, "ambiance et ton/humoristique": 4}, {"catégories/divertissement": 5, "catégories/information": 10, "catégories/météo": 5, "catégories/série": 5, "genres/comédie": 5, "sous-genres/jeu": 5, "sujets/actualité": 5, "ambiance et ton/humoristique": 5}, {"genres/comédie romantique": 10, "genres/comédie": 10}, {"catégories/film": 10, "genres/comédie": 5, "sous-genres/science-fiction": 5, "ambiance et ton/humoristique": 5}], "10": [{"ambiance et ton/suspense": 10}, {"catégories/série": 10.0, "ambiance et ton/suspense": 6.67}, {"catégories/film": 5, "catégories/série": 5, "genres/thriller": 5, "ambiance et ton/suspense": 10}, {"catégories/film": 2.5, "catégories/série": 8.0, "genres/thriller": 2.5, "ambiance et ton/suspense": 10.0}, {"catégories/film": 10, "genres/thriller": 5, "ambiance et ton/suspense": 5}, {"catégories/film": 10, "genres/thriller": 10, "ambiance et ton/suspense": 10}, {"catégories/divertissement": 10, "catégories/film": 10, "genres/thriller": 10, "ambiance et ton/suspense": 10}, {"catégories/divertissement": 10, "catégories/série": 10, "ambiance et ton/suspense": 10}, {"catégories/film": 10, "catégories/magazine": 10, "genres/thriller": 10, "sujets/société": 10, "ambiance et ton/suspense": 10}, {"catégories/film": 10, "catégories/série": 10, "genres/thriller": 10, "ambiance et ton/suspense": 10}, {"catégories/film": 10, "catégories/série": 10, "ambiance et ton/suspense": 10}, {"catégories/magazine": 10, "catégories/série": 10, "sujets/société": 10, "ambiance et ton/suspense": 10}, {"catégories/information": 10, "catégories/série": 10, "sujets/actualité": 10, "ambiance et ton/suspense": 10}, {"catégories/divertissement": 4, "catégories/série": 10, "ambiance et ton/suspense": 10}, {"catégories/film": 10, "ambiance et ton/suspense": 10}, {"catégories/film": 10, "catégories/série": 10, "genres/comédie": 10, "ambiance et ton/humoristique": 10, "ambiance et ton/suspense": 10}, {"catégories/magazine": 4, "catégories/série": 10, "sujets/société": 4, "ambiance et ton/suspense": 10}, {"catégories/série": 10, "genres/action": 5, "ambiance et ton/suspense": 5}, {"catégories/film": 10, "genres/comédie": 5, "genres/thriller": 5, "ambiance et ton/humoristique": 5, "ambiance et ton/suspense": 5}, {"catégories/film": 10, "genres/action": 5, "genres/thriller": 5, "ambiance et ton/suspense": 5}, {"catégories/film": 4, "catégories/série": 10, "ambiance et ton/suspense": 10}, {"catégories/film": 10, "catégories/série": 10, "genres/action": 10, "ambiance et ton/suspense": 10}, {"catégories/film": 10, "ambiance et ton/suspense": 5}, {"catégories/film": 10, "ambiance et ton/angoissant": 10}, {"catégories/film": 10, "sous-genres/horreur": 10}], "11": [{"catégories/reportage": 10, "genres/documentaire": 10, "sujets/voyage et évasion": 10, "thèmes/aux 4 coins du monde": 10}, {"catégories/reportage": 10, "genres/documentaire": 10, "lieu de l'action/île": 10, "sujets/voyage et évasion": 10, "thèmes/aux 4 coins du monde": 10}, {"thèmes/aux 4 coins du monde": 10}, {"catégories/magazine": 10, "catégories/reportage": 10, "thèmes/aux 4 coins du monde": 10}], "12": [{"catégories/reportage": 10, "genres/documentaire": 10, "thèmes/nature et vie sauvage": 10}, {"catégories/magazine": 10, "catégories/reportage": 10, "genres/documentaire": 10, "sujets/société": 10, "thèmes/nature et vie sauvage": 10}], "13": [{"catégories/série": 10, "genres/comédie": 10, "ambiance et ton/humoristique": 10, "thèmes/amitié": 10}, {"catégories/information": 3.5, "catégories/série": 10.0, "genres/comédie": 10.0, "sujets/actualité": 3.5, "ambiance et ton/humoristique": 10.0, "thèmes/amitié": 10.0}, {"catégories/magazine": 10, "catégories/reportage": 10, "genres/documentaire": 10, "sujets/société": 10, "thèmes/nature et vie sauvage": 10}, {"catégories/série": 10, "genres/comédie": 5, "ambiance et ton/humoristique": 5, "thèmes/amitié": 5}], "14": [{"sous-genres/jeu": 10}, {"catégories/divertissement": 10.0, "sous-genres/jeu": 9.0}, {"catégories/divertissement": 10.0, "catégories/information": 5.5, "sous-genres/jeu": 10.0, "sujets/actualité": 4.0}, {"catégories/divertissement": 10.0, "catégories/information": 5.83, "catégories/météo": 5.83, "sous-genres/jeu": 10.0}, {"catégories/divertissement": 10, "catégories/magazine": 10, "sous-genres/jeu": 10, "sujets/sport": 10}, {"catégories/divertissement": 10.0, "sous-genres/jeu": 4.0}, {"catégories/divertissement": 10, "catégories/information": 10, "catégories/météo": 5, "sous-genres/jeu": 10, "sujets/actualité": 5}, {"catégories/divertissement": 10, "catégories/magazine": 10, "sous-genres/jeu": 10, "sujets/société": 10}, {"catégories/divertissement": 10, "catégories/série": 10, "sous-genres/jeu": 10}, {"catégories/divertissement": 10.0, "catégories/information": 6.5, "catégories/météo": 6.5, "sous-genres/jeu": 10.0, "thèmes/argent et trafics": 6.5}, {"catégories/divertissement": 4, "catégories/information": 10, "catégories/météo": 10, "sous-genres/jeu": 4, "thèmes/argent et trafics": 4}, {"catégories/divertissement": 7.5, "catégories/information": 10.0, "sous-genres/jeu": 7.5, "sujets/actualité": 5.0}, {"catégories/divertissement": 4, "catégories/information": 10, "catégories/météo": 10, "sous-genres/jeu": 4}, {"catégories/divertissement": 4, "catégories/information": 10, "sous-genres/jeu": 4, "sujets/actualité": 10}, {"catégories/divertissement": 10, "sous-genres/jeu": 10, "thèmes/argent et trafics": 10}, {"sous-genres/jeu": 10, "sujets/société": 10}, {"catégories/divertissement": 10, "sous-genres/jeu": 10, "thèmes/argent et trafics": 5}, {"catégories/divertissement": 4, "catégories/série": 10, "sous-genres/jeu": 4}, {"catégories/divertissement": 10, "catégories/magazine": 4, "sous-genres/jeu": 10, "sujets/société": 4}, {"catégories/divertissement": 10, "catégories/film": 10, "sous-genres/jeu": 10}, {"catégories/divertissement": 5, "catégories/information": 10, "catégories/météo": 5, "catégories/série": 5, "genres/comédie": 5, "sous-genres/jeu": 5, "sujets/actualité": 5, "ambiance et ton/humoristique": 5}], "15": [{"catégories/divertissement": 10, "catégories/film": 10}, {"catégories/film": 10, "sous-genres/science-fiction": 10}, {"catégories/divertissement": 10, "catégories/film": 10, "genres/thriller": 10, "ambiance et ton/suspense": 10}, {"catégories/film": 10, "catégories/série": 10, "ambiance et ton/suspense": 10}, {"catégories/film": 10.0, "genres/comédie": 9.0, "ambiance et ton/humoristique": 9.0}, {"catégories/film": 10}, {"catégories/film": 10, "genres/comédie": 5, "ambiance et ton/humoristique": 5}, {"catégories/film": 10, "genres/thriller": 5, "ambiance et ton/suspense": 5}, {"catégories/film": 10, "catégories/magazine": 10, "sujets/société": 10}, {"catégories/film": 10, "catégories/série": 10}, {"catégories/film": 10, "genres/action": 10}, {"catégories/film": 10, "genres/thriller": 10, "ambiance et ton/suspense": 10}, {"catégories/film": 10, "catégories/magazine": 10, "genres/thriller": 10, "sujets/société": 10, "ambiance et ton/suspense": 10}, {"catégories/film": 5, "catégories/série": 5, "genres/thriller": 5, "ambiance et ton/suspense": 10}, {"catégories/film": 10, "catégories/série": 10, "genres/thriller": 10, "ambiance et ton/suspense": 10}, {"catégories/magazine": 10, "sujets/art et culture": 10, "sujets/cinéma": 10}, {"catégories/film": 10, "ambiance et ton/suspense": 10}, {"catégories/film": 4, "catégories/série": 10}, {"catégories/dessins animés": 10.0, "catégories/film": 3.0}, {"catégories/film": 10, "catégories/série": 10, "genres/comédie": 10, "ambiance et ton/humoristique": 10, "ambiance et ton/suspense": 10}, {"catégories/film": 10, "catégories/série": 10, "genres/comédie": 10, "ambiance et ton/humoristique": 10}, {"catégories/film": 10, "genres/action": 5, "genres/comédie": 5, "ambiance et ton/humoristique": 5}, {"catégories/reportage": 10, "genres/documentaire": 10, "sujets/art et culture": 10, "sujets/cinéma": 10}, {"catégories/film": 10, "genres/comédie": 5, "genres/thriller": 5, "ambiance et ton/humoristique": 5, "ambiance et ton/suspense": 5}, {"catégories/film": 4, "catégories/série": 10, "genres/thriller": 4, "ambiance et ton/suspense": 4}, {"catégories/film": 10, "genres/action": 5, "genres/thriller": 5, "ambiance et ton/suspense": 5}, {"catégories/film": 10, "genres/action": 5}, {"catégories/film": 10, "catégories/magazine": 10, "genres/comédie": 10, "sujets/société": 10, "ambiance et ton/humoristique": 10}, {"catégories/film": 10, "catégories/reportage": 10}, {"catégories/divertissement": 10, "catégories/film": 10, "genres/comédie": 10, "ambiance et ton/humoristique": 10}, {"catégories/film": 10, "genres/historique": 10}, {"catégories/film": 4, "catégories/série": 10, "ambiance et ton/suspense": 10}, {"catégories/divertissement": 10, "catégories/film": 10, "sous-genres/jeu": 10}, {"catégories/film": 10, "catégories/série": 10, "genres/action": 10, "ambiance et ton/suspense": 10}, {"catégories/film": 10, "ambiance et ton/suspense": 5}, {"catégories/film": 10, "thèmes/amour et séduction": 10}, {"catégories/film": 10, "ambiance et ton/angoissant": 10}, {"catégories/film": 10, "sous-genres/horreur": 10}], "16": [{"catégories/information": 5.33, "catégories/magazine": 10.0, "catégories/météo": 5.33, "sujets/société": 5.33}, {"catégories/information": 7, "catégories/magazine": 10, "thèmes/découverte": 4}, {"thèmes/découverte": 10}, {"catégories/divertissement": 5, "catégories/information": 10, "catégories/magazine": 10, "catégories/météo": 5, "sujets/actualité": 5, "sujets/media et journalisme": 5, "sujets/presse": 5}, {"catégories/information": 4, "catégories/magazine": 10, "sujets/actualité": 4, "sujets/media et journalisme": 4, "sujets/presse": 4}, {"catégories/information": 4.5, "catégories/magazine": 10.0}, {"catégories/magazine": 10, "thèmes/découverte": 10}, {"catégories/magazine": 10, "sujets/commerce et économie": 10}, {"catégories/magazine": 10.0, "sujets/société": 4.0}, {"catégories/magazine": 10, "catégories/reportage": 10}, {"catégories/reportage": 10}, {"catégories/reportage": 10, "genres/documentaire": 10, "thèmes/découverte": 10}, {"catégories/reportage": 10, "genres/documentaire": 10}, {"catégories/reportage": 10, "genres/documentaire": 10, "sujets/sciences et techniques": 10}, {"catégories/information": 4, "catégories/magazine": 10, "sujets/actualité": 4, "sujets/société": 10}, {"catégories/magazine": 10, "catégories/reportage": 10, "genres/documentaire": 10, "sujets/société": 10, "thèmes/découverte": 10}, {"catégories/reportage": 10, "genres/documentaire": 10, "objets/voiture": 10, "sujets/sciences et techniques": 10}, {"catégories/reportage": 10, "genres/documentaire": 10, "sujets/art et culture": 10, "sujets/musique": 10}, {"catégories/magazine": 10, "lieu de l'action/habitation": 10, "sujets/société": 10}, {"catégories/information": 10, "catégories/magazine": 10, "catégories/météo": 5, "catégories/série": 5, "ambiance et ton/humoristique": 5}, {"catégories/reportage": 10, "genres/documentaire": 10, "genres/historique": 10, "sujets/Histoire": 10}, {"catégories/reportage": 10, "genres/documentaire": 10, "sujets/société": 10}, {"catégories/reportage": 10, "genres/documentaire": 10, "sujets/voyage et évasion": 10, "thèmes/aux 4 coins du monde": 10}, {"catégories/reportage": 10, "genres/documentaire": 10, "lieu de l'action/île": 10, "sujets/voyage et évasion": 10, "thèmes/aux 4 coins du monde": 10}, {"catégories/divertissement": 4, "catégories/magazine": 10, "sujets/société": 10}, {"catégories/reportage": 10, "genres/documentaire": 10, "thèmes/nature et vie sauvage": 10}, {"catégories/divertissement": 5, "catégories/information": 5, "catégories/magazine": 10}, {"catégories/magazine": 10, "catégories/série": 10}, {"catégories/magazine": 10, "catégories/reportage": 4, "genres/documentaire": 4, "sujets/société": 10, "thèmes/découverte": 4}, {"catégories/reportage": 10, "genres/documentaire": 10, "sujets/société": 5, "thèmes/découverte": 5}, {"catégories/information": 10, "catégories/magazine": 10, "catégories/météo": 5, "thèmes/découverte": 5}, {"catégories/information": 10, "catégories/reportage": 10, "genres/documentaire": 10, "sujets/actualité": 10, "thèmes/découverte": 10}, {"catégories/magazine": 8, "catégories/reportage": 3, "genres/documentaire": 3, "sujets/société": 10}, {"catégories/film": 10, "catégories/reportage": 10}, {"catégories/divertissement": 10, "catégories/reportage": 10, "genres/documentaire": 10, "thèmes/découverte": 10}, {"catégories/divertissement": 10.0, "catégories/magazine": 10.0}, {"catégories/magazine": 10, "catégories/météo": 10, "thèmes/découverte": 10}, {"catégories/information": 10, "catégories/magazine": 10, "catégories/météo": 10, "sujets/actualité": 10, "thèmes/découverte": 10}], "17": [{"catégories/série": 10.0, "catégories/téléfilm": 3.5, "type/téléfilm": 3.5}, {"catégories/série": 10, "catégories/téléfilm": 10, "type/téléfilm": 10}, {"catégories/téléfilm": 10, "type/téléfilm": 10}], "18": [{"catégories/magazine": 10, "sujets/art et culture": 10}, {"catégories/magazine": 10, "sujets/art et culture": 10, "sujets/cinéma": 10}, {"catégories/magazine": 10, "sujets/art et culture": 10, "sujets/musique": 10}, {"catégories/reportage": 10, "genres/documentaire": 10, "sujets/art et culture": 10, "sujets/musique": 10}, {"catégories/information": 10, "catégories/magazine": 10, "catégories/météo": 5, "catégories/série": 5, "genres/comédie": 5, "sujets/actualité": 5, "sujets/art et culture": 10, "sujets/la famille": 5, "sujets/media et journalisme": 5, "sujets/presse": 5, "ambiance et ton/humoristique": 5}, {"catégories/information": 10, "catégories/magazine": 5, "catégories/météo": 5, "catégories/série": 10, "genres/comédie": 10, "sujets/actualité": 5, "sujets/art et culture": 5, "sujets/la famille": 10, "sujets/media et journalisme": 5, "sujets/musique": 5, "sujets/presse": 5, "ambiance et ton/humoristique": 10}, {"catégories/reportage": 10, "genres/documentaire": 10, "sujets/art et culture": 10}, {"catégories/information": 10, "catégories/magazine": 10, "catégories/météo": 10, "sujets/art et culture": 10}, {"catégories/information": 10, "catégories/magazine": 10, "catégories/météo": 10, "sujets/art et culture": 10, "sujets/musique": 10}, {"catégories/information": 5, "catégories/magazine": 5, "catégories/météo": 5, "catégories/série": 10, "genres/comédie": 10, "sujets/art et culture": 5, "sujets/la famille": 10, "sujets/musique": 5, "ambiance et ton/humoristique": 10}, {"catégories/reportage": 10, "genres/documentaire": 10, "sujets/art et culture": 10, "sujets/cinéma": 10}, {"catégories/information": 10, "catégories/magazine": 10, "catégories/météo": 5, "catégories/série": 5, "genres/comédie": 5, "sujets/actualité": 5, "sujets/art et culture": 10, "sujets/la famille": 5, "sujets/media et journalisme": 5, "sujets/musique": 5, "sujets/presse": 5, "ambiance et ton/humoristique": 5}, {"sujets/art et culture": 10, "sujets/musique": 10}, {"catégories/magazine": 10, "catégories/série": 10, "sujets/art et culture": 10, "thèmes/amour et séduction": 10}], "19": [{"catégories/reportage": 10, "genres/documentaire": 10, "sujets/société": 10, "ambiance et ton/onirique": 10}], "20": [{"catégories/série": 10, "thèmes/vengeance et auto-justice": 10}], "21": [{"catégories/magazine": 10, "catégories/série": 10, "sujets/art et culture": 10, "thèmes/amour et séduction": 10}, {"catégories/film": 10, "thèmes/amour et séduction": 10}, {"genres/comédie romantique": 10, "genres/comédie": 10}], "22": [{"catégories/divertissement": 10.0, "sous-genres/jeu": 4.0}, {"catégories/divertissement": 10, "catégories/information": 4, "catégories/météo": 4}, {"catégories/divertissement": 10, "catégories/information": 10, "catégories/magazine": 10}, {"catégories/divertissement": 10, "catégories/série": 10}, {"catégories/divertissement": 10, "catégories/série": 10, "sous-genres/jeu": 10}, {"catégories/divertissement": 10.0, "sous-genres/jeu": 9.0}, {"catégories/divertissement": 10.0, "catégories/information": 5.83, "catégories/météo": 5.83, "sous-genres/jeu": 10.0}, {"catégories/divertissement": 10}, {"catégories/divertissement": 5, "catégories/information": 10, "catégories/météo": 5, "sujets/actualité": 5, "sujets/media et journalisme": 5, "sujets/presse": 5}, {"catégories/divertissement": 10, "catégories/magazine": 4, "sujets/société": 4}, {"catégories/divertissement": 10, "catégories/magazine": 10, "sujets/sport": 10}, {"catégories/divertissement": 4, "catégories/magazine": 10, "sujets/société": 10}, {"catégories/divertissement": 10, "catégories/magazine": 10, "sous-genres/jeu": 10, "sujets/société": 10}, {"catégories/divertissement": 10, "catégories/série": 10, "genres/comédie": 10, "ambiance et ton/humoristique": 10}, {"catégories/divertissement": 10.0, "catégories/magazine": 9.0, "sujets/société": 9.0}, {"catégories/divertissement": 10.0, "catégories/information": 6.5, "catégories/météo": 6.5, "sous-genres/jeu": 10.0, "thèmes/argent et trafics": 6.5}, {"catégories/divertissement": 10, "catégories/information": 10, "sous-genres/jeu": 10, "sujets/actualité": 10}, {"catégories/divertissement": 10, "catégories/information": 10, "sujets/actualité": 10}, {"catégories/divertissement": 5.0, "catégories/information": 10.0, "catégories/magazine": 5.0, "catégories/météo": 5.0}, {"catégories/divertissement": 5.0, "catégories/information": 10.0, "catégories/magazine": 10.0, "catégories/météo": 5.0}, {"catégories/divertissement": 5, "catégories/information": 10, "catégories/météo": 5}, {"catégories/divertissement": 10, "catégories/film": 10}, {"catégories/divertissement": 10, "catégories/information": 10, "catégories/météo": 10}, {"catégories/divertissement": 10, "catégories/série": 10, "ambiance et ton/suspense": 10}, {"catégories/divertissement": 5, "catégories/information": 5, "catégories/magazine": 10}, {"catégories/divertissement": 5.0, "catégories/information": 10.0, "catégories/magazine": 5.0, "catégories/météo": 5.0, "catégories/série": 5.0, "ambiance et ton/humoristique": 5.0}, {"catégories/divertissement": 10, "catégories/film": 10, "genres/thriller": 10, "ambiance et ton/suspense": 10}, {"catégories/divertissement": 4, "catégories/série": 10, "ambiance et ton/suspense": 10}, {"catégories/divertissement": 10, "catégories/information": 10, "sujets/actualité": 10, "sujets/media et journalisme": 10, "sujets/presse": 10}, {"catégories/autre": 5.0, "catégories/divertissement": 5.0, "catégories/information": 10.0, "catégories/météo": 5.0, "sujets/actualité": 5.0, "sujets/media et journalisme": 5.0, "sujets/presse": 5.0}, {"catégories/dessins animés": 10, "catégories/divertissement": 10}, {"catégories/divertissement": 10, "catégories/sport": 10, "sujets/football": 10, "sujets/sport": 10}, {"catégories/divertissement": 10, "catégories/film": 10, "genres/comédie": 10, "ambiance et ton/humoristique": 10}, {"catégories/divertissement": 4, "catégories/série": 10, "genres/comédie": 10, "ambiance et ton/humoristique": 10}, {"catégories/divertissement": 10, "catégories/série jeunesse": 10}, {"catégories/divertissement": 10, "catégories/reportage": 10, "genres/documentaire": 10, "thèmes/découverte": 10}, {"catégories/divertissement": 10, "catégories/série": 4}, {"catégories/divertissement": 10.0, "catégories/magazine": 10.0}], "23": [{"catégories/film": 10, "genres/action": 10}, {"catégories/série": 10, "genres/action": 10}, {"genres/action": 10}, {"catégories/série": 10.0, "genres/action": 4.0}, {"catégories/film": 10, "genres/action": 5, "genres/comédie": 5, "ambiance et ton/humoristique": 5}, {"catégories/série": 10, "genres/action": 5, "ambiance et ton/suspense": 5}, {"catégories/film": 10, "genres/action": 5, "genres/thriller": 5, "ambiance et ton/suspense": 5}, {"catégories/film": 10, "genres/action": 5}, {"catégories/film": 10, "catégories/série": 10, "genres/action": 10, "ambiance et ton/suspense": 10}, {"catégories/série": 10, "genres/action": 10, "lieu de l'action/Terrain de jeu de super héros": 10, "lieu de l'action/lieu imaginaire": 10}], "24": [{"catégories/magazine": 10.0, "genres/historique": 4.0, "sujets/Histoire": 4.0, "sujets/société": 9.0}, {"catégories/magazine": 10, "genres/historique": 10, "sujets/Histoire": 10, "sujets/société": 10}, {"catégories/reportage": 10, "genres/documentaire": 10, "genres/historique": 10, "sujets/Histoire": 10}, {"catégories/série": 10, "genres/historique": 10, "sujets/Histoire": 10}, {"catégories/film": 10, "genres/historique": 10}, {"catégories/série": 10, "genres/historique": 5, "sujets/Histoire": 5}], "25": [{"catégories/divertissement": 10, "sous-genres/télé-réalité": 10}, {"catégories/divertissement": 10, "catégories/série": 10, "sous-genres/télé-réalité": 10}], "26": [{"sous-genres/fantastique": 10, "sous-genres/science-fiction": 10}, {"catégories/film": 10, "sous-genres/science-fiction": 10}, {"catégories/série": 10, "sous-genres/science-fiction": 10}, {"catégories/série": 10, "personnages/fantômes": 10, "sous-genres/science-fiction": 10}, {"catégories/film": 10, "genres/comédie": 5, "sous-genres/science-fiction": 5, "ambiance et ton/humoristique": 5}, {"catégories/série": 10, "personnages/monstres": 10, "personnages/vampires": 10, "sous-genres/science-fiction": 10}], "27": [{"genres/drame": 10}, {"genres/comédie dramatique": 10, "genres/drame": 10}], "28": [{"catégories/information": 10, "catégories/magazine": 10, "type/débat": 10, "actions/discussion": 10, "sujets/actualité": 10}, {"actions/discussion": 10}, {"catégories/magazine": 10, "sujets/politique": 10}, {"catégories/magazine": 10, "type/débat": 10, "actions/discussion": 10}, {"catégories/divertissement": 5.0, "catégories/information": 10.0, "catégories/magazine": 5.0, "catégories/météo": 5.0, "sujets/actualité": 5.0, "sujets/media et journalisme": 5.0, "sujets/presse": 5.0}, {"catégories/information": 6.5, "catégories/magazine": 10.0, "sujets/actualité": 4.0, "sujets/media et journalisme": 4.0, "sujets/presse": 4.0, "sujets/société": 8.25}, {"catégories/information": 8.5, "catégories/magazine": 10.0, "sujets/actualité": 8.5, "sujets/media et journalisme": 8.5, "sujets/presse": 8.5}, {"catégories/information": 10.0, "sujets/actualité": 10.0, "sujets/media et journalisme": 4.0, "sujets/presse": 4.0}, {"catégories/information": 10.0, "sujets/actualité": 10.0, "sujets/media et journalisme": 9.0, "sujets/presse": 9.0}, {"catégories/information": 10.0, "catégories/magazine": 4.25, "catégories/météo": 4.25, "sujets/actualité": 6.25, "sujets/media et journalisme": 5.5, "sujets/presse": 5.5}, {"catégories/information": 10.0, "catégories/magazine": 7.0, "sujets/actualité": 6.0, "sujets/media et journalisme": 6.0, "sujets/presse": 6.0}, {"catégories/information": 10, "catégories/magazine": 10, "sujets/actualité": 10, "sujets/politique": 10}, {"catégories/information": 10.0, "catégories/météo": 5.59, "sujets/actualité": 5.06, "sujets/media et journalisme": 3.94, "sujets/presse": 3.94}, {"catégories/information": 8.5, "catégories/série": 10.0, "sujets/actualité": 8.5, "sujets/media et journalisme": 8.5, "sujets/presse": 8.5}, {"catégories/information": 10, "catégories/magazine": 10, "catégories/météo": 5, "sujets/actualité": 5, "sujets/media et journalisme": 5, "sujets/presse": 5, "sujets/société": 10}, {"catégories/magazine": 10, "sujets/politique": 5, "sujets/société": 5}], "29": [{"catégories/série": 10, "personnages/médecin": 10}], "30": [{"catégories/reportage": 10, "genres/documentaire": 10, "objets/voiture": 10, "sujets/sciences et techniques": 10}, {"catégories/magazine": 10, "sujets/sciences et techniques": 10}, {"catégories/magazine": 10, "catégories/sport": 5, "sujets/sciences et techniques": 5, "sujets/sport": 5}], "31": [{"catégories/série": 10, "personnages/fantômes": 10, "sous-genres/science-fiction": 10}, {"catégories/série": 10, "personnages/monstres": 10, "personnages/vampires": 10, "sous-genres/science-fiction": 10}], "32": [{"catégories/magazine": 10, "sous-genres/télé-achat": 10}], "33": [{"catégories/événement": 10}], "34": [{"catégories/divertissement": 10, "catégories/sport": 10, "sujets/football": 10, "sujets/sport": 10}, {"catégories/magazine": 3, "catégories/sport": 8, "sujets/football": 8, "sujets/sport": 10}], "35": [{"genres/aventure": 10}], "36": [{"catégories/sport": 10, "sujets/ovalie": 10, "sujets/rugby": 10, "sujets/sport": 10}, {"catégories/magazine": 5, "catégories/sport": 5, "sujets/ovalie": 5, "sujets/rugby": 5, "sujets/sport": 10}], "37": [{"catégories/sport": 10, "sujets/cyclisme": 10, "sujets/sport": 10}], "38": [{"catégories/autre": 10}], "39": [{"catégories/magazine": 10, "sujets/basketball": 10, "sujets/sport": 10}]}