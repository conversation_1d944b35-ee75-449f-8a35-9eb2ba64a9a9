{"1": {"name": "série", "includes": [], "score": {"ambiance et ton/suspense": "2", "catégories/série": "6"}}, "2": {"name": "Info-addict", "includes": [], "score": {"catégories/programme d'information": "1", "catégories/météo": "1", "sujets/media et journalisme": "4", "sujets/presse": "4"}}, "3": {"name": "sujet de société", "includes": [], "score": {"catégories/information": "3", "catégories/magazine": "4", "sujets/actualité": "1", "sujets/société": "2"}}, "4": {"name": "fan de sport", "includes": [], "score": {"Genre Orange/émission sports": "3 ", "Genre Orange/mag. sports": "3"}}, "5": {"name": "fan d'animation", "includes": [], "score": {"catégories/film": "1", "genres/animation": "2"}}, "6": {"name": "kids", "includes": [], "score": {"ambiance et ton/humoristique": "1", "catégories/dessins animés": "3", "personnages/personnages jeunesse": "1"}}, "7": {"name": "paroles et musique", "includes": [], "score": {"Genre Orange/musical": "3", "Genre Orange/concert": "3", "Genre Orange/doc. musique": "3"}}, "8": {"name": "super-héros", "includes": [], "score": {"personnages/super-héros": "4", "genres/action": "2", "sous-genres/fantastique": "2", "sous-genres/science-fiction": "2", "personnages/super pouvoir": "2", "personnages/méchant d’anthologie": "2", "envies/montée d’adrénaline": " 2", "catégories/fiction": "1", "ambiance et ton/horreur": "-4", "personnages/DC comics": "2", "personnages/wonder woman": "2", "personnages/superman": "2"}}, "9": {"name": "rire et comédie", "includes": [], "score": {"ambiance et ton/humoristique": "3", "envies/éclats de rire": "3", "genres/comédie": "4", "catégories/émissions": "-2", "genres/thriller": "-2", "genres/action": "-2", "genres/aventure": "-2"}}, "10": {"name": "suspense et frisson", "includes": [], "score": {"ambiance et ton/angoissant": "1", "ambiance et ton/suspense": "9", "genres/thriller": "5", "personnages/monstres": "-2", "sous-genres/science-fiction": "-4", "sous-genres/fantastique": "-4", "catégories/fiction": "3", "Genre Orange/suspense": "4"}}, "11": {"name": "horizons lointains", "includes": [], "score": {"thèmes/aux 4 coins du monde": "10", "sujets/voyage et évasion": "6", "sujets/magazine": "1", "sujets/reportage": "3", "sujets/documentaire": "2"}}, "12": {"name": "nature et vie sauvage", "includes": [], "score": {"thèmes/nature et vie sauvage": "2"}}, "13": {"name": "rire et amitié", "includes": [], "score": {"ambiance et ton/humoristique": "6", "genres/comédie": "3", "thèmes/amitié": "6"}}, "14": {"name": "jeu tv", "includes": [], "score": {"Genre Orange/jeu": "8", "catégories/divertissement": "4", "sous-genres/jeu": "6", "sous-genres/télé-réalité": "1", "thèmes/énigmes et jeu de piste": "4", "audience/pour toute la famille": "4", "catégories/fiction": "-4"}}, "15": {"name": "film", "includes": [], "score": {"ambiance et ton/humoristique": "1", "ambiance et ton/suspense": "2", "catégories/film": "10", "genres/action": "1", "genres/comédie": "2", "genres/thriller": "2"}}, "16": {"name": "Curiosité et soif de connaissances", "includes": [], "score": {"catégories/magazine": "1", "catégories/reportage": "1", "genres/documentaire": "1", "sujets/sciences et techniques": "2", "sujets/voyage et évasion": "2", "thèmes/découverte": "5", "thèmes/nature et vie sauvage": "2"}}, "17": {"name": "fiction", "includes": [], "score": {"type/téléfilm": "1"}}, "18": {"name": "art et culture", "includes": [], "score": {"Genre Orange/émission culturelle": "3", "catégories/magazine": "3", "Genre Orange/culture": "3", "envies/se cultiver": "3", "sujets/littérature": "3", "sujets/art et culture": "4"}}, "19": {"name": "rêves et réalité", "includes": [], "score": {"ambiance et ton/onirique": "3", "catégories/reportage": "1", "genres/documentaire": "1"}}, "20": {"name": "Vengeance et auto-justice", "includes": [], "score": {"thèmes/vengeance et auto-justice": "1"}}, "21": {"name": "Amour et séduction", "includes": [], "score": {"genres/comédie romantique": "1", "thèmes/amour et séduction": "2", "catégories/fiction": "0", "envies/soirées entre filles": "0", "ambiance et ton/sentimental": "0", "avertissements/scènes pouvant heurter la sensibilité du public": "-1", "avertissements/scènes de nature sexuelle": "-2"}}, "22": {"name": "Divertissement", "includes": [], "score": {"catégories/divertissement": "2", "sous-genres/jeu": "1"}}, "23": {"name": "gros bras", "includes": [], "score": {"catégories/fiction": "2", "genres/action": "4", "envies/montée d'adrénaline": "3", "actions/combat": "2", "thèmes/seul contre tous": "2", "sujets/arts martiaux": "2", "sous-genres/super-héros": "-1", "ambiance et ton/humoristique": "-1", "sous-genres/fantastique": "-1", "sous-genres/science-fiction": "-1"}}, "24": {"name": "histoire", "includes": [], "score": {"genres/historique": "1", "sujets/Histoire": "1"}}, "25": {"name": "télé-réalité", "includes": [], "score": {"catégories/divertissement": "1", "sous-genres/télé-réalité": "2"}}, "26": {"name": "science-fiction", "includes": [], "score": {"sous-genres/anticipation": "2", "sous-genres/science-fiction": "1"}}, "27": {"name": "drame et tragédie", "includes": [], "score": {"genres/drame": "8", "genres/mélodrame": "8", "thèmes/mal de vivre": "8", "envies/à vos mouchoirs": "2", "ambiance et ton/émouvant": "2", "ambiance et ton/triste": "2", "type/happy end": " -8 ", "genres/comédie dramatique": "-4"}}, "28": {"name": "dé<PERSON> d'idée", "includes": [], "score": {"actions/discussion": "4", "catégories/information": "5", "catégories/magazine": "3", "sujets/actualité": "4", "sujets/media et journalisme": "3", "sujets/presse": "3", "sujets/se cultiver": "1", "type/débat": "4"}}, "29": {"name": "médecine", "includes": [], "score": {"catégories/série": "1", "personnages/médecin": "1"}}, "30": {"name": "Sciences et techniques", "includes": [], "score": {"catégories/reportage": "1", "sujets/sciences et techniques": "2"}}, "31": {"name": "Monstres et créatures", "includes": [], "score": {"personnages/fantômes": "1", "personnages/monstres": "1", "personnages/vampires": "1", "sous-genres/science-fiction": "1"}}, "32": {"name": "<PERSON> de télé-a<PERSON>t", "includes": [], "score": {"sous-genres/télé-achat": "1"}}, "33": {"name": "Evènement", "includes": [], "score": {"catégories/événement": "1"}}, "34": {"name": "Football", "includes": [], "score": {"sujets/football": "5", "Genre Orange/émission sports": "3", "Genre Orange/mag. sports": "3", "Genre Orange/football": "5"}}, "35": {"name": "Aventure", "includes": [], "score": {"genres/aventure": "4", "sous genres/western": "2", "sous genres/cape et d’épée": "2", "sous genres/peplum": "2", "catégories/contes et légendes": "2", "thèmes/chasse au trésor": "2", "personnages/aventurier": "2", "personnages/aventuriers": "2", "thèmes/survie": "1", "envies/montée d’adrénaline": "1", "thèmes/parcours initiatique": "1", "sujets/sports": "-4", "sous-genres/jeu": "-2", "sous-genres/science-fiction": "-2"}}, "36": {"name": "Rugby", "includes": [], "score": {"sujets/ovalie": "1", "sujets/rugby": "2"}}, "37": {"name": "Cyclisme", "includes": [], "score": {"sujets/cyclisme": "1"}}, "38": {"name": "Divers", "includes": [], "score": {"catégories/autre": "1"}}, "39": {"name": "Basketball", "includes": [], "score": {"sujets/basketball": "1"}}}