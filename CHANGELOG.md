## 0.11.0 (2025-04-04)
### Features
* Clean-output-marprofile [(f7a22d6)](https://gitlab.tech.orange/dataia-scoring/profiling/dags/Ravenne/commits/f7a22d6588236b310b882c45551a2d6a1df383f2)

## 0.10.0 (2025-04-03)
### Features
* DFYPROFTV-2988-marqueurs-profils [(2c2961b)](https://gitlab.tech.orange/dataia-scoring/profiling/dags/Ravenne/commits/2c2961bc6ab06fd15f77db53de20bd2d6d01b4db)

## 0.9.0 (2025-04-02)
### Features
* Dfy3212 verticales sol1 [(7209efe)](https://gitlab.tech.orange/dataia-scoring/profiling/dags/Ravenne/commits/7209efe746b688d7ec0dedb4839763a3cc8c3194)

## 0.8.0 (2025-02-28)
### Features
* Increase-ttl-cluster-dataproc [(838e01f)](https://gitlab.tech.orange/dataia-scoring/profiling/dags/Ravenne/commits/838e01f81cc53d5f0939868b06113d0bbe0641ba)

## 0.7.1 (2025-01-23)
### Bugfix
* ImageVersion :2.0 [(d57336f)](https://gitlab.tech.orange/dataia-scoring/profiling/dags/Ravenne/commits/d57336f4667620789ab4b73b7eecba5873edeb36)

## 0.7.0 (2025-01-13)
### Features
* DataProcMigrate spark 3.1.3 [(0704358)](https://gitlab.tech.orange/dataia-scoring/profiling/dags/Ravenne/commits/0704358e7f326d71f3700a697f73c6f9decdbf55)
*  feature-dataPro-a90e23a3-SNAPSHOT [(900b9f2)](https://gitlab.tech.orange/dataia-scoring/profiling/dags/Ravenne/commits/900b9f2177113de38dcbdb41445b8165107d2fe5)

## 0.6.0 (2024-12-16)
### Features
* Adapter les dags de tous les workflows pour éviter les doublons dans le chargement des dépendances [(f16de47)](https://gitlab.tech.orange/dataia-scoring/profiling/dags/Ravenne/commits/f16de47353725d348e303b82e1c60dafa39e7a7f)
* Improve documentation [(b7acd08)](https://gitlab.tech.orange/dataia-scoring/profiling/dags/Ravenne/commits/b7acd08531dccd973be8dadac7681fe24a063e65)

## 0.5.4 (2024-10-14)
### Features
* Dora ref: main [(d3bf702)](https://gitlab.tech.orange/dataia-scoring/profiling/dags/Ravenne/commits/d3bf70269365449268e62c872d6009aa37500323)
* Dora ref: feature/doraEnv [(bc6dee7)](https://gitlab.tech.orange/dataia-scoring/profiling/dags/Ravenne/commits/bc6dee79a661e7fbde569f4334ae821521a3b76f)

## 0.5.3 (2024-10-04)
### Features
* OptimDashboardConcepts [(498eb59)](https://gitlab.tech.orange/dataia-scoring/profiling/dags/Ravenne/commits/498eb591eb80ad1810cae95bc57b3436f34c77c0)
* DFYPROFTV-2856-path-fastmarkers [(8c12642)](https://gitlab.tech.orange/dataia-scoring/profiling/dags/Ravenne/commits/8c126421ce839b4105d0e142773c81d8ff4eda4d)

### Bugfix
* Put back RELEASE dependency for ute-mr-common and ute-parent [(2532c79)](https://gitlab.tech.orange/dataia-scoring/profiling/dags/Ravenne/commits/2532c792a1e729796f9bbd50bf91b89cfac87844)
* Move visionnageRavenneTable outside to refstor for security purpose [(6629c04)](https://gitlab.tech.orange/dataia-scoring/profiling/dags/Ravenne/commits/6629c04c87e93956fcd3f86462d468f42bf91496)
* Correction query_transformation_generatedRavenne.sql [(f030ff4)](https://gitlab.tech.orange/dataia-scoring/profiling/dags/Ravenne/commits/f030ff46b700aba02dd961861ea85928c5389bac)
* Add_filter_OptinReco_forVodAndProgbymac [(db5c1cf)](https://gitlab.tech.orange/dataia-scoring/profiling/dags/Ravenne/commits/db5c1cfef68647fbe127c7dc87144e1ef8b415ca)
* AugmentationTTLCluster [(6ff727f)](https://gitlab.tech.orange/dataia-scoring/profiling/dags/Ravenne/commits/6ff727fb8f6c0c285a2d529406ef10c3e4e24c96)
* Airflow bug scheduler update schedule interval [(b67e757)](https://gitlab.tech.orange/dataia-scoring/profiling/dags/Ravenne/commits/b67e757c44057275040f728af422c10de1f3ab2b)
* Filter isi channels from best channels [(f45cf40)](https://gitlab.tech.orange/dataia-scoring/profiling/dags/Ravenne/commits/f45cf405d12cc3dc2ce5daf105ebd94004155b84)
* Adaptations for new timeboxes dxpyod [(d0ebaa5)](https://gitlab.tech.orange/dataia-scoring/profiling/dags/Ravenne/commits/d0ebaa543e19dd9adcde56cab23fc960bb3b5c0f)

## 0.5.2 (2024-06-06)
### Bugfix
* Move visionnageRavenneTable outside to refstor for security purpose [(6629c04)](https://gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/6629c04c87e93956fcd3f86462d468f42bf91496)

## 0.5.1 (2024-05-22)
### Bugfix
* Correction query_transformation_generatedRavenne.sql [(f030ff4)](https://gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/f030ff46b700aba02dd961861ea85928c5389bac)

## 0.5.0 (2024-05-07)
### Features
* OptimDashboardConcepts [(498eb59)](https://gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/498eb591eb80ad1810cae95bc57b3436f34c77c0)

## 0.4.0 (2024-04-24)
### Features
* DFYPROFTV-2856-path-fastmarkers [(8c12642)](https://gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/8c126421ce839b4105d0e142773c81d8ff4eda4d)

## 0.3.5 (2024-04-11)
### Bugfix
* Add_filter_OptinReco_forVodAndProgbymac [(db5c1cf)](https://gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/db5c1cfef68647fbe127c7dc87144e1ef8b415ca)

## 0.3.4 (2024-03-13)
### Bugfix
* AugmentationTTLCluster [(6ff727f)](https://gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/6ff727fb8f6c0c285a2d529406ef10c3e4e24c96)

## 0.3.3 (2024-02-22)
### Bugfix
* Airflow bug scheduler update schedule interval [(b67e757)](https://gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/b67e757c44057275040f728af422c10de1f3ab2b)

## 0.3.2 (2024-01-23)
### Bugfix
* Filter isi channels from best channels [(f45cf40)](https://gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/f45cf405d12cc3dc2ce5daf105ebd94004155b84)

## 0.3.1 (2023-11-27)
### Bugfix
* Adaptations for new timeboxes dxpyod [(d0ebaa5)](https://gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/d0ebaa543e19dd9adcde56cab23fc960bb3b5c0f)

## 0.3.0 (2023-10-25)
### Features
* AddNewAction [(6c68966)](https://gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/6c6896628d2c1f534a2d06b3df432971ca39a9c8)

## 0.2.0 (2023-09-06)
### Features
* Use dag helper [(e16ab3d)](https://gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/e16ab3deb6ef50c8d7b66ff9924e37d1daf8b14a)

### Bugfix
* Add trigger_rule ALL_DONE for check_success [(73763ac)](https://gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/73763ac99a3c111ec548b324db4b188b7cc1b327)
* Bad substitution in filters with &{ instead of ${ [(d6da459)](https://gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/d6da459e0ef154179d5c6a025eda387b2f0b14f9)
* Error on older versions of bash [(e09495e)](https://gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/e09495ec904cc1b18858740581903d55a3190d8b)
* Add forgottent caputre true on counterdiff [(5dc4e28)](https://gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/5dc4e28d0be92163e5ae31e93582bc0df05c0be3)
* Add check &{variable} in config_*.json files [(fe96331)](https://gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/fe963319ed825e2f1936508fc137b218bd1e6b9f)
* Fix optincalcul tests [(b9c6e75)](https://gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/b9c6e7527fa2d1e999c785b049c5c39784688c5c)
* Increment DEFAULT_TOP_OD_PROVIDERS limit to 20 [(dd64a9b)](https://gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/dd64a9be17d2203ef0b996c0a76534a85b81c397)

## 0.1.1 (2023-06-14)
### Bugfix
* Per uniformize [(2e6916e)](https://gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/2e6916e079845c9f0a4b22b34495e17307224632)

## 0.1.0 (2023-06-06)
### Features
* Add limit bestproviders [(77731b2)](https://gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/77731b26c07b69ff4e7bf41ba7bb3412168d8e73)

## 0.0.1 (2023-05-25)
### Bugfix
* Use template with tag deployement [(61c89fb)](https://gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/61c89fb36d379a5b9db05e41dbf180fbab7201f4)

## 0.0.0 (2023-05-22)
### Features
* Tansfer_pnsdata_to_corcodile_bucket [(f6e8a28)](https://gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/f6e8a28178e9d2859eb01545da448f708d2ec0b6)
* Add profiltype in dag [(4211c75)](https://gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/4211c7530b42a6be022b3f3401d5b319100c3d76)
* Add genetic ans stat action [(5acb053)](https://gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/5acb053a954e7c752b2a0e065684cbfb5b17c787)
* Add job_filterAndTimeslot and job_ponderation_concepts [(37c423d)](https://gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/37c423df8a1217f168cacd7f5689475874778af2)
* Init gc workflow [(cbfff48)](https://gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/cbfff48ab577e0efe798c337807ee3d76d3e116a)
* /gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/1be064a65b517763c0d2abdb1e4251cf40f40fd8)
* /gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/04249694d2fb93d9656b6849e5a9479ccb9a4929)
* /gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/a6b71eca84fc6dd9590b983fdf9d3b2b39f29f6f)
* /gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/9b3045daf7640c0521d6c83f33fbf249a1b7934a)
* /gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/074f435815c9c733a4bd2ce606efa41f521bae35)
* Master' into D900_per_mergeExLibris [(cca4991)](https://gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/cca499165f1e1188d8f33c9d33a19177da8d0cf7)
* /gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/eaae7fc532af7f73d4c12a0ad823f025a0fb0910)
* /gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/7f95f05e3ed3f4b744b01c8ab535921da603122e)
* /gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/dafda796a0adb89f4febcd4af46890d07205c90b)
* /gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/7429e6fb7e0bb89c1ad1a0f79cdca9072822bb35)
* Master' into D900_per_mergeExLibris [(e52f657)](https://gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/e52f657ff81878e9925e3062804b2751abc69e1e)
* /gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/f12c58900d172c20f6ca4c9149d0de3594231c9b)
* /gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/a1d7bdf4172ae904615dc88fae74a6e9623e5adf)
* /gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/0bdefd5fae74d05b12a4f92ad94b7e8a47c3d0ed)
* /gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/d27f110d1853076b56b728739c33a01d8a49e77a)
* /gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/077427f89a95dab32c8edb4a29547ed574841ccb)
* /gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/98491b191a20c3a94e521f2815ed6781fe0e9413)
* /gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/90ca0be1d82b362d7ce1b48da9e74e37e737e613)
* /gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/c10d3b9834f60ed8c11da73a4250f034c27de568)
* /gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/732a095acd78fa9ace2a9feffd782c7e542a2bda)
* /gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/e26248febdf42324320be6f3ac746628267c8f9e)
* /gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/1ea6e77007f99b85ec3f280081bad1bde96ff807)
* /gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/9091c853cb3fa1d63b0acfa6e0f14e5cc91ae153)
* /gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/328f94fdbb5c5b9336d719e2ca14a4b00113b186)
* /gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/47c066dad78da08c8742637437ff463c10ee8565)
* /gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/8f55fa69a636539d20485bfee26f4e15bf78fb13)
* /gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/9289a9907b9bd7d1ae9e6a03eab7bf7ba31e0cf2)
* /gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/25dba944cf8e44735323acd7b12f347fda083025)
* /gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/fc97fac72d1ebe01aac998424f7b853b585b4443)
* /gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/d6d5481bd53857ba9a23454140f310548ff9e046)
* /gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/3fb337029e34a6e2e10cd695fbf69df9a8205112)
* /gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/0b7ce9b846bff125a8b9703a317dc51a828af968)
* /gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/d3353576354e149989bda2df4e916adb32cea9b6)

### Bugfix
* Bucket name for sensor job [(551d5ab)](https://gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/551d5ab6cb23a3b62f89636a8bc8f27783d890b6)
*  bad character [(9440bc0)](https://gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/9440bc05dfe2660c5b8c68f55d70e19d55da984a)
* /gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/e48ad225703778ed196bca1b8d11bf228f7145b7)
* Change pondaration main class [(e54f97e)](https://gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/e54f97ecd770a19b9cdf93778af144b44344a119)
* Add a loop to create path filtred ravenne without * [(e2a5610)](https://gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/e2a56100d2919574e0f0132b410937871a9a7484)
* /gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/eccf351cf9a2ff3130dbef19200f949b0c1f5c40)
* Switched genetic-counterdiff on fullAnonymized folder [(42d2404)](https://gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/42d240445d7f8c64c22dd8ba05019b10cb9d4c60)
* Restore optout profil after gdpr migration [(b332c47)](https://gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/b332c47eb34acd1b9993e943e39dd3ab176a1457)
* /gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/86966f4c4ee1a62453aed9e20b42aa417574f169)
* /gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/fa3730bbf5d7a8b5591264511af196a69af286c0)
* /gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/8783471b9ec21c9629e44692e7bb330e6db53f09)
* /gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/7aa15c284c036ff3f20e207269285aeab02fb386)
* /gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/1054be4f41adb8dd40f10fb4e52f6d71fb0f2f0d)
* /gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/c0624c141d08f14d8e07751250082f381f7d91fb)
* /gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/f83763c445e08c32d31864eac8144ee51a8679eb)
* /gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/ed65d53baedeab1af3c4d6357e14fe163809ae62)
* /gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/e607c2e0c2d856a0ca09cab8168cd32463d16548)
* /gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/20d02648c48c5ba2f6c14db0d3f1f7aecabb7199)
* /gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/93c959c7042970e889a4727a0d09267854535bb2)
* /gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/26b178464db3919f4c965bd0ac3013d543013554)
* /gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/886e44bdbc08faa5bd734531fe4c6962958522b5)
* Pub and contentId/vod [(5dd2659)](https://gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/5dd2659f8e9fd0bc069dfff824f97895eb716587)
* /gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/02eb2815eca9c7cce3a3e5bcaff0782ab9e37252)
* /gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/de93607a305812977de844070c3228aba8a80600)
* /gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/3261e4fd151c2df0af3219d4385793f3aecc0f83)
* /gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/e8c0220a696cb667f77b3829acf00aaa28c06472)
* /gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/05ba8c0b5ea3a55af142127ecc994d832cf43035)
* /gitlab.si.francetelecom.fr/SDFY-CDA/profiling/workflows/Ravenne/commits/bb63e1da7c3325c9539a97b536cb63260d781be3)
