BRANCH_NAME=$(shell git rev-parse --abbrev-ref HEAD | sed -e 's/\//-/g' | sed -e 's/_/-/g' | sed -e 's/ /-/g')
VERSION=VERSION_DEV
suffix_env=dev
suffix_legacy_env=dev
cProject=ofr-pfg-profilingtv-${suffix_env}
cEnv=cloud-composer2-${suffix_env}
#cProject=orange-arti-common-${suffix_legacy_env}
#cEnv=cloud-composer2-${suffix_legacy_env}
cLocation=europe-west1
DAG_COMPOSER_URI=$(shell gcloud composer environments list --project "${cProject}" --locations "${cLocation}" --format json --filter="name ~ ${cEnv}" | jq -r ".[0].config.dagGcsPrefix" | sed 's/\/dags//g')
PROJECT_NAME=$(basename $(shell echo $(shell (git config --local remote.origin.url)) | rev | cut -d / -f 1 | rev))
PROJECT_GROUP=profiling
SUFFIX_TV=allwf
ARTIFACT_BUCKET_NAME=ofr-pfg-artifact-${SUFFIX_TV}-${suffix_env}
BUILD_ENV=gcp-${suffix_legacy_env}
WORKFLOW_NAME=workflowUte${PROJECT_NAME}

export

env:
	env

clean:
	bash src/main/workflow_gcp/bin/clean_all_on_gcp.sh

check-dags:
	bash src/main/workflow_gcp/bin/check_filters_and_dags.sh

build-jars:
	(find . -name "*__pycache__*" -exec rm -rf {} \; ) || true;
	sh src/main/workflow_gcp/bin/build_jars.sh

push-to-gcp:
	sh src/main/workflow_gcp/bin/push_all_to_gcp.sh

build-and-push-to-gcp: clean check-dags build-jars push-to-gcp
