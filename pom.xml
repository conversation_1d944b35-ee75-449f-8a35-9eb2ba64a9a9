<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.orange.profiling</groupId>
        <artifactId>ute-parent</artifactId>
        <version>RELEASE</version>
    </parent>
    <artifactId>workflowUteRavenne</artifactId>
    <version>VERSION_DEV</version>
    <name>${project.artifactId}</name>
    <description>${project.artifactId}</description>
    <packaging>jar</packaging>
    <distributionManagement>
        <repository>
            <id>profiling-ute</id>
            <url>URL</url>
        </repository>
    </distributionManagement>
    <properties>
        <junit.version>4.12</junit.version>
	<arti.dag.version>3_0_0</arti.dag.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.orange.profiling</groupId>
            <artifactId>ute-mr-common</artifactId>
            <version>LATEST</version>
            <!-- ne pas exclure spark, car contient commons-lang3 utilisé pour classe Optin -->
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
            <version>2.10.0</version>
        </dependency>
        <dependency>
            <groupId>com.orange.profiling</groupId>
            <artifactId>ute-counters-check</artifactId>
            <version>RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.googlecode.json-simple</groupId>
            <artifactId>json-simple</artifactId>
            <version>1.1.1</version>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>${junit.version}</version>
            <scope>test</scope>
        </dependency>
    </dependencies>
    <build>
        <resources>
            <resource>
                <directory>src/main/java/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy-dependencies</id>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                            <goal>properties</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}/lib</outputDirectory>
                            <overWriteReleases>false</overWriteReleases>
                            <overWriteSnapshots>false</overWriteSnapshots>
                            <overWriteIfNewer>true</overWriteIfNewer>
                            <includeScope>runtime</includeScope>
                            <includeGroupIds>
                                com.orange.profiling,com.google.guava,commons-cli,joda-time,com.googlecode.json-simple
                            </includeGroupIds>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <version>3.0.0</version>
                <executions>
                    <execution>
                        <id>jar-name</id>
                        <phase>package</phase>
                        <goals>
                            <goal>regex-properties</goal>
                        </goals>
                        <configuration>
                            <regexPropertySettings>
                                <regexPropertySetting>
                                    <name>json-simple</name>
                                    <value>${com.googlecode.json-simple:json-simple:jar}</value>
                                    <regex>.*/(json-simple.*\.jar$)</regex>
                                    <replacement>$1</replacement>
                                    <failIfNoMatch>true</failIfNoMatch>
                                </regexPropertySetting>
                                <regexPropertySetting>
                                    <name>ute-counters-check-jar</name>
                                    <value>${com.orange.profiling:ute-counters-check:jar}</value>
                                    <regex>.*/(ute.*\.jar$)</regex>
                                    <replacement>$1</replacement>
                                    <failIfNoMatch>true</failIfNoMatch>
                                </regexPropertySetting>
                                <regexPropertySetting>
                                    <name>ute-mr-common-jar</name>
                                    <value>${com.orange.profiling:ute-mr-common:jar}</value>
                                    <regex>.*/(ute.*\.jar$)</regex>
                                    <replacement>$1</replacement>
                                    <failIfNoMatch>true</failIfNoMatch>
                                </regexPropertySetting>
                                <regexPropertySetting>
                                    <name>joda-time</name>
                                    <value>${joda-time:joda-time:jar}</value>
                                    <regex>.*/(joda-time.*\.jar$)</regex>
                                    <replacement>$1</replacement>
                                    <failIfNoMatch>true</failIfNoMatch>
                                </regexPropertySetting>
                                <regexPropertySetting>
                                    <name>guava</name>
                                    <value>${com.google.guava:guava:jar}</value>
                                    <regex>.*/(guava.*\.jar$)</regex>
                                    <replacement>$1</replacement>
                                    <failIfNoMatch>true</failIfNoMatch>
                                </regexPropertySetting>
                                <regexPropertySetting>
                                    <name>commons-cli</name>
                                    <value>${commons-cli:commons-cli:jar}</value>
                                    <regex>.*/(commons-cli.*\.jar$)</regex>
                                    <replacement>$1</replacement>
                                    <failIfNoMatch>true</failIfNoMatch>
                                </regexPropertySetting>
                            </regexPropertySettings>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <artifactId>maven-assembly-plugin</artifactId>
                <version>2.3</version>
                <executions>
                    <execution>
                        <id>make-assembly-gcp-configs</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                        <configuration>
                            <descriptor>src/main/assembly/gcp_configs.xml</descriptor>
                        </configuration>
                    </execution>
                    <execution>
                        <id>make-assembly-gcp-dags</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                        <configuration>
                            <descriptor>src/main/assembly/gcp_dags.xml</descriptor>
                        </configuration>
                    </execution>
                    <execution>
                        <id>make-assembly-gcp-data</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                        <configuration>
                            <descriptor>src/main/assembly/gcp_data.xml</descriptor>
                        </configuration>
                    </execution>
                    <execution>
                        <id>make-assembly-gcp-libs</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                        <configuration>
                            <descriptor>src/main/assembly/gcp_libs.xml</descriptor>
                        </configuration>
                    </execution>
                    <execution>
                        <id>make-assembly-gcp-scripts</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                        <configuration>
                            <descriptor>src/main/assembly/gcp_scripts.xml</descriptor>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.7.9</version>
                <configuration>
                    <destFile>${basedir}/target/jacoco.exec</destFile>
                    <dataFile>${basedir}/target/jacoco.exec</dataFile>
                </configuration>
                <executions>
                    <execution>
                        <id>default-prepare-agent</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>default-report</id>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        <filters>
            <filter>src/main/filters/filter-${env}.properties</filter>
        </filters>
    </build>
    <organization>
        <name>orange</name>
        <url>www.orange.fr</url>
    </organization>
    <profiles>
        <profile>
            <id>gcp-dev</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <env>gcp-dev</env>
            </properties>
        </profile>
        <profile>
            <id>gcp-preprod</id>
            <properties>
                <env>gcp-preprod</env>
            </properties>
        </profile>
        <profile>
            <id>gcp-prod</id>
            <properties>
                <env>gcp-prod</env>
            </properties>
        </profile>
    </profiles>
</project>
